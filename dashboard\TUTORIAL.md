# 📚 仪表板使用教程

## 🎯 界面布局说明

### 左侧边栏
- **📁 数据管理**：上传Excel文件
- **📅 时间范围**：选择分析时间段
- **🏢 栋别筛选**：选择要分析的栋别
- **⚡ 系统状态**：显示数据统计和质量

### 主界面标签页
- **📈 概览**：整体数据概况和关键指标
- **⏰ 工时分析**：详细的工时统计和趋势
- **⚖️ 负荷均衡**：栋别间负荷对比分析
- **🚨 预警监控**：异常检测和预警信息
- **🔮 预测分析**：趋势预测和场景模拟

## 📤 数据上传步骤

### 步骤1：点击文件上传
1. 在左侧边栏找到"📁 数据管理"区域
2. 点击"上传Excel文件"按钮
3. 选择你的Excel文件（支持.xlsx和.xls格式）

### 步骤2：加载数据
1. 文件选择后，点击"🔄 加载数据"按钮
2. 系统会显示"正在处理数据..."的加载提示
3. 成功后显示"✅ 数据加载成功！"

### 步骤3：验证数据
加载成功后，左侧边栏会显示：
- 数据记录数量
- 最后更新时间
- 数据质量评分

## 📊 使用各个功能页面

### 📈 概览页面使用

**关键指标卡片**
- 总作业记录：显示数据总量
- 总工时：所有作业的工时总和
- 总加班工时：超过8小时的加班统计
- 活跃栋别数：参与作业的栋别数量

**图表分析**
- **工时趋势图**：查看每日工时变化
- **负荷分布饼图**：各栋别工作量占比
- **作业类型分布**：不同作业类型的数量统计
- **加班分布图**：每日加班情况
- **工时热力图**：直观显示工作强度

**数据质量报告**
- **完整性**：数据缺失情况
- **一致性**：数据格式规范性
- **时效性**：数据更新及时性

**快速洞察**
系统自动分析并提供：
- 加班最多的栋别
- 负荷不均衡提醒
- 效率异常提示
- 作业集中度分析

### ⏰ 工时分析页面使用

**工时统计指标**
- 总工作时长：所有作业的工时总和
- 平均日工时：每天平均工作时间
- 总加班时长：超出正常工时的时间
- 加班天数：发生加班的天数统计

**趋势分析图表**
- **工时趋势图**：
  - 显示各栋别每日工时变化
  - 红色虚线表示8小时正常工时基准
  - 可以看出哪些日期工时异常
  
- **加班分布图**：
  - 柱状图显示每日加班情况
  - 不同颜色代表不同栋别
  - 可以识别加班集中的时间段

**栋别对比分析**
数据表格显示各栋别的：
- 总工时、平均工时、最大工时
- 总加班、平均加班
- 总任务数、平均效率

**效率分析**
- **雷达图**：多维度效率评估
  - 工时效率：单位时间完成任务能力
  - 任务完成率：任务完成情况
  - 负荷均衡度：工作分配合理性
  - 时间利用率：工作时间利用效率
  - 质量指标：工作质量评估

- **箱线图**：工时分布统计
  - 显示各栋别工时分布范围
  - 识别异常值和离群点
  - 对比不同栋别的工时稳定性

**时间段分析**
- 高峰时段：任务开始最集中的时间
- 上午任务数：12点前开始的任务
- 下午任务数：12点后开始的任务
- 时段分布图：各小时任务分布情况

**详细记录查询**
支持多条件筛选：
- 栋别筛选：选择特定栋别
- 日期范围：指定时间段
- 仅显示加班：只看加班记录

## 🎛️ 筛选和配置功能

### 时间范围筛选
1. 在左侧边栏找到"📅 时间范围"
2. 点击日期选择器
3. 选择开始和结束日期
4. 数据会自动更新

### 栋别筛选
1. 在"🏢 栋别筛选"区域
2. 勾选或取消勾选栋别
3. 支持多选和全选
4. 图表会实时更新

### 图表交互
- **缩放**：鼠标滚轮缩放图表
- **平移**：拖拽移动图表视图
- **悬停**：鼠标悬停查看详细数据
- **图例**：点击图例隐藏/显示数据系列

## 🚨 预警信息理解

### 预警级别
- **🔴 高级预警**：需要立即关注
- **🟡 中级预警**：需要注意监控
- **🟢 正常状态**：运行良好

### 预警类型
1. **过度加班预警**
   - 触发条件：单日加班超过10小时
   - 建议：检查作业安排，优化人力配置

2. **负荷不均衡预警**
   - 触发条件：栋别间负荷比例超过2:1
   - 建议：重新分配作业任务

3. **效率异常预警**
   - 触发条件：效率低于0.5任务/小时
   - 建议：分析效率下降原因

4. **连续加班预警**
   - 触发条件：连续3天以上加班
   - 建议：安排休息，避免疲劳作业

## 💡 使用技巧和最佳实践

### 数据准备技巧
1. **统一格式**：确保日期和时间格式一致
2. **完整信息**：尽量填写所有必需字段
3. **规范命名**：栋别使用A、B、C等标准命名
4. **定期更新**：保持数据的时效性

### 分析技巧
1. **对比分析**：使用不同时间段对比
2. **趋势观察**：关注长期趋势变化
3. **异常识别**：重点关注预警信息
4. **深入钻取**：从概览到详细逐步分析

### 报告导出
1. **截图保存**：使用浏览器截图功能
2. **数据导出**：复制表格数据到Excel
3. **图表保存**：右键图表选择保存图片
4. **打印报告**：使用浏览器打印功能

## 🔧 常见问题解决

### 数据加载问题
**问题**：上传文件后显示错误
**解决**：
1. 检查Excel文件是否包含必需列
2. 确认日期和时间格式正确
3. 检查是否有空行或异常数据

**问题**：数据质量评分很低
**解决**：
1. 检查缺失数据并补充
2. 统一数据格式
3. 清理异常值

### 图表显示问题
**问题**：图表显示空白或异常
**解决**：
1. 检查筛选条件是否过于严格
2. 确认数据范围内有有效数据
3. 刷新页面重新加载

**问题**：图表加载缓慢
**解决**：
1. 减少数据量或时间范围
2. 关闭其他浏览器标签页
3. 检查网络连接

### 性能优化
1. **数据量控制**：单次分析建议不超过1000条记录
2. **浏览器选择**：推荐使用Chrome或Edge
3. **内存管理**：定期刷新页面清理缓存
4. **网络环境**：确保稳定的网络连接

## 📞 获取帮助

如果遇到问题：
1. 查看控制台错误信息
2. 检查数据格式是否正确
3. 参考本教程的故障排除部分
4. 联系技术支持团队
