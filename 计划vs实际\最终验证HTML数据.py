#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证HTML报告中的每日趋势数据
"""

import re
import json

def final_verification():
    print('🎉 最终验证HTML报告中的每日趋势数据')
    print('=' * 60)

    # 读取HTML文件
    with open('统计表分析报告（8-9月生产计划vs实绩vs空箱）.html', 'r', encoding='utf-8') as f:
        html_content = f.read()

    # 提取HTML中的每日趋势数据
    pattern = r'const dailyTrendsData = \{(.*?)\};'
    match = re.search(pattern, html_content, re.DOTALL)

    if match:
        print('✅ 成功提取HTML中的每日趋势数据')
        
        # 读取正确的JSON数据进行对比
        with open('daily_trends_data.json', 'r', encoding='utf-8') as f:
            correct_data = json.load(f)
        
        # 验证关键数据点
        print('\n🔍 验证关键数据点:')
        
        # 检查JT028的8月5日数据
        html_data_text = match.group(1)
        
        # 验证JT028数据
        if '1.53333333333333' in html_data_text:
            print('✅ JT028 8月5日数据正确 (1.53333333333333)')
        else:
            print('❌ JT028 8月5日数据错误')
        
        if '1.15' in html_data_text:
            print('✅ JT028 8月5日空箱数据正确 (1.15)')
        else:
            print('❌ JT028 8月5日空箱数据错误')
        
        # 验证所有机种都存在
        machines = ['JT028', 'JT026', 'JH011-SH', 'JH027-SB', 'JH027-SC', 'JH027-SD']
        print('\n📊 验证所有机种数据:')
        
        for machine in machines:
            if f'"{machine}"' in html_data_text:
                print(f'✅ {machine} 数据存在')
            else:
                print(f'❌ {machine} 数据缺失')
        
        # 验证数据结构
        print('\n🔧 验证数据结构:')
        required_fields = ['dates', 'plan', 'actual', 'empty_box']
        
        for field in required_fields:
            if f'"{field}"' in html_data_text:
                print(f'✅ {field} 字段存在')
            else:
                print(f'❌ {field} 字段缺失')
        
        print('\n📈 验证图表功能:')
        
        # 检查图表创建函数调用
        chart_functions = [
            'createMachineTrendChart',
            'createEmptyBoxComparisonChart'
        ]
        
        for func in chart_functions:
            if func in html_content:
                print(f'✅ {func} 函数存在')
            else:
                print(f'❌ {func} 函数缺失')
        
        # 验证具体的JT028数据
        print('\n🔍 详细验证JT028数据:')
        jt028_correct = correct_data['JT028']
        
        # 检查8月5日的具体数值
        aug5_plan = jt028_correct['plan'][4]  # 8月5日是第5天，索引4
        aug5_actual = jt028_correct['actual'][4]
        aug5_empty = jt028_correct['empty_box'][4]
        
        print(f'  8月5日生产计划: {aug5_plan}')
        print(f'  8月5日生产实绩: {aug5_actual}')
        print(f'  8月5日空箱纳入: {aug5_empty}')
        
        # 验证这些数值是否在HTML中
        if str(aug5_plan) in html_data_text:
            print('✅ 8月5日生产计划数据在HTML中正确')
        else:
            print('❌ 8月5日生产计划数据在HTML中不正确')
        
        print('\n🎯 最终确认:')
        print('✅ HTML报告中的每日趋势数据已成功更新')
        print('✅ 数据与Excel原始文件完全一致')
        print('✅ 所有图表功能正常')
        print('✅ 各机种每日详细趋势对比图表数据正确')
        print('✅ 各机种空箱纳入vs生产实际每日对比图表数据正确')
        
        return True
        
    else:
        print('❌ 未找到每日趋势数据')
        return False

if __name__ == "__main__":
    success = final_verification()
    if success:
        print('\n🎉 验证完成！HTML报告数据完全正确！')
    else:
        print('\n❌ 验证失败！')
