import pandas as pd
import numpy as np

# 读取Excel文件
file_path = r'计划vs实际\第一阶段\作业计划表ABC栋20250801~20250830_自动排程.xlsx'
df = pd.read_excel(file_path, sheet_name=0)

# 过滤掉无效的日期行
valid_df = df[df['日期'].notna() & df['日期'].astype(str).str.contains(r'^\d+/\d+$', na=False)]

print('=== 作业计划表数据结构分析 ===')
print(f'总行数: {len(df)}')
print(f'有效数据行数: {len(valid_df)}')
print()

# 按日期统计作业数量
print('每日作业安排数量:')
daily_counts = valid_df['日期'].value_counts().sort_index()
for date, count in daily_counts.items():
    print(f'{date}: {count}个作业')
print()

# 按栋别和作业类型统计
print('栋别和作业类型组合统计:')
building_work_stats = valid_df.groupby(['栋别', '作业类型']).size().unstack(fill_value=0)
print(building_work_stats)
print()

# 分析作业内容的详细分布
print('作业内容详细分布:')
content_counts = valid_df['作业内容'].value_counts()
for content, count in content_counts.items():
    print(f'{content}: {count}次')
print()

# 分析方向分布
print('作业方向分布:')
direction_counts = valid_df['方向'].value_counts()
for direction, count in direction_counts.items():
    print(f'{direction}: {count}次')
print()

# 分析时间段分布
print('起始时间分布:')
start_time_counts = valid_df['起始时间'].value_counts().sort_index()
for time, count in start_time_counts.items():
    print(f'{time}: {count}次')
print()

# 显示数据样本（每个栋别的示例）
print('=== 各栋别作业示例 ===')
for building in ['A', 'B', 'C']:
    building_data = valid_df[valid_df['栋别'] == building]
    if len(building_data) > 0:
        print(f'\n{building}栋示例（前3条）:')
        sample_data = building_data[['日期', '起始时间', '截止时间', '作业类型', '方向', '作业内容', '数量']].head(3)
        print(sample_data.to_string(index=False))

# 分析每日每栋的作业分布
print('\n=== 每日每栋作业分布 ===')
daily_building_stats = valid_df.groupby(['日期', '栋别']).size().unstack(fill_value=0)
print('前10天的每栋作业数量:')
print(daily_building_stats.head(10))

# 分析作业时长
print('\n=== 作业时长分析 ===')
print('作业时长分布:')
duration_counts = valid_df['作业时长'].value_counts().sort_index()
for duration, count in duration_counts.items():
    print(f'{duration}: {count}次')

print('\n数据读取和分析完成！')
