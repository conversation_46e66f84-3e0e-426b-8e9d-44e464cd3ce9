#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取8-9月每日趋势数据，用于生成折线图
"""

import pandas as pd
import numpy as np
import json
from datetime import datetime, <PERSON><PERSON><PERSON>

def extract_daily_trends():
    # 读取Excel文件，使用第2行作为header
    file_path = '出荷统计表7.18（更新版).xlsx'
    df = pd.read_excel(file_path, header=1)

    print('=== 提取8-9月每日趋势数据（修正版）===')

    # 获取日期列
    date_columns = [col for col in df.columns if isinstance(col, datetime)]

    # 筛选8月和9月的列
    aug_sep_columns = []
    for col in date_columns:
        if col.month in [8, 9] and col.year == 2025:
            aug_sep_columns.append(col)

    aug_sep_columns.sort()  # 按日期排序

    if len(aug_sep_columns) != 61:
        print(f'警告：找到{len(aug_sep_columns)}天数据，预期61天')

    print(f'找到8-9月数据列: {len(aug_sep_columns)}天')
    print(f'日期范围: {min(aug_sep_columns).strftime("%Y-%m-%d")} 到 {max(aug_sep_columns).strftime("%Y-%m-%d")}')
    
    # 生成日期标签
    date_labels = [col.strftime('%m-%d') for col in aug_sep_columns]

    # 定义机种信息
    machines = ['JT028', 'JT026', 'JH011-SH', 'JH027-SB', 'JH027-SC', 'JH027-SD']
    categories = ['生产计划', '空箱纳入', '生产实绩', '产品出荷']

    # 提取每日数据
    daily_trends = {}

    for machine in machines:
        machine_data = df[df['机种'] == machine]

        machine_daily = {
            'dates': date_labels,
            'plan': [],
            'empty_box': [],
            'actual': [],
            'shipment': []
        }

        # 提取各类数据
        for category in categories:
            category_data = machine_data[machine_data['类别'] == category]
            if not category_data.empty:
                # 提取8-9月的每日数据
                daily_values = []
                for col in aug_sep_columns:
                    if col in category_data.columns:
                        val = category_data[col].iloc[0]
                        daily_values.append(float(val) if pd.notna(val) else 0.0)
                    else:
                        daily_values.append(0.0)

                if category == '生产计划':
                    machine_daily['plan'] = daily_values
                elif category == '空箱纳入':
                    machine_daily['empty_box'] = daily_values
                elif category == '生产实绩':
                    machine_daily['actual'] = daily_values
                elif category == '产品出荷':
                    machine_daily['shipment'] = daily_values
            else:
                # 如果没有找到数据，填充0
                if category == '生产计划':
                    machine_daily['plan'] = [0.0] * len(aug_sep_columns)
                elif category == '空箱纳入':
                    machine_daily['empty_box'] = [0.0] * len(aug_sep_columns)
                elif category == '生产实绩':
                    machine_daily['actual'] = [0.0] * len(aug_sep_columns)
                elif category == '产品出荷':
                    machine_daily['shipment'] = [0.0] * len(aug_sep_columns)

        daily_trends[machine] = machine_daily

        print(f'{machine}: 提取了{len(machine_daily["plan"])}天的数据')
        print(f'  计划总量: {sum(machine_daily["plan"]):.2f}')
        print(f'  实绩总量: {sum(machine_daily["actual"]):.2f}')
    
    # 保存数据
    with open('daily_trends_data.json', 'w', encoding='utf-8') as f:
        json.dump(daily_trends, f, ensure_ascii=False, indent=2)
    
    print('\n每日趋势数据已保存到 daily_trends_data.json')
    return daily_trends

if __name__ == "__main__":
    extract_daily_trends()
