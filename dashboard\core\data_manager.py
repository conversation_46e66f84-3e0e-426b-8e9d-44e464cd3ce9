#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据管理模块
"""

import pandas as pd
import numpy as np
from datetime import datetime, time, timedelta
import streamlit as st
from typing import Optional, Dict, Any, Tuple
import logging

class DataManager:
    """数据管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    @st.cache_data
    def load_excel_data(_self, uploaded_file) -> pd.DataFrame:
        """加载Excel数据文件"""
        try:
            # 读取Excel文件
            df = pd.read_excel(uploaded_file)
            
            # 数据预处理
            df = _self._preprocess_data(df)
            
            # 数据验证
            _self._validate_data(df)
            
            return df
            
        except Exception as e:
            _self.logger.error(f"数据加载失败: {str(e)}")
            raise Exception(f"数据加载失败: {str(e)}")
    
    def _preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """数据预处理"""
        # 复制数据避免修改原始数据
        processed_df = df.copy()
        
        # 解析日期列
        if '日期' in processed_df.columns:
            processed_df[['年', '月', '日']] = processed_df['日期'].apply(
                lambda x: pd.Series(self._parse_date_column(x))
            )
            
            # 创建标准日期列
            processed_df['标准日期'] = pd.to_datetime(
                processed_df[['年', '月', '日']], errors='coerce'
            )
        
        # 解析时间列
        if '起始时间' in processed_df.columns:
            processed_df['起始时间_parsed'] = processed_df['起始时间'].apply(
                self._parse_time_column
            )
        
        if '截止时间' in processed_df.columns:
            processed_df['截止时间_parsed'] = processed_df['截止时间'].apply(
                self._parse_time_column
            )
        
        # 计算工作时长
        if '起始时间_parsed' in processed_df.columns and '截止时间_parsed' in processed_df.columns:
            processed_df['单任务工时'] = processed_df.apply(
                lambda row: self._calculate_work_hours(
                    row['起始时间_parsed'], 
                    row['截止时间_parsed']
                ), axis=1
            )
        
        # 清理数据
        processed_df = self._clean_data(processed_df)
        
        return processed_df
    
    def _parse_date_column(self, date_val) -> Tuple[Optional[int], Optional[int], Optional[int]]:
        """解析日期列"""
        if pd.isna(date_val):
            return None, None, None
            
        if isinstance(date_val, str):
            try:
                # 处理 "4/2" 这样的格式
                if '/' in date_val:
                    parts = date_val.split('/')
                    if len(parts) == 2:
                        month, day = parts
                        return 2025, int(month), int(day)
            except:
                pass
        elif hasattr(date_val, 'year'):
            # 处理datetime格式
            return date_val.year, date_val.month, date_val.day
            
        return None, None, None
    
    def _parse_time_column(self, time_val) -> Optional[time]:
        """解析时间列"""
        if pd.isna(time_val):
            return None
        
        if isinstance(time_val, str):
            try:
                # 处理 "8:30" 这样的格式
                if ':' in time_val:
                    hour, minute = time_val.split(':')
                    return time(int(hour), int(minute))
            except:
                pass
        elif hasattr(time_val, 'hour'):
            # 处理time格式
            return time_val.time() if hasattr(time_val, 'time') else time_val
            
        return None
    
    def _calculate_work_hours(self, start_time: Optional[time], end_time: Optional[time]) -> float:
        """计算工作时长"""
        if start_time is None or end_time is None:
            return 0
        
        # 转换为datetime对象进行计算
        base_date = datetime(2025, 1, 1)
        start_dt = datetime.combine(base_date.date(), start_time)
        end_dt = datetime.combine(base_date.date(), end_time)
        
        # 如果结束时间小于开始时间，说明跨天了
        if end_dt < start_dt:
            end_dt += timedelta(days=1)
        
        # 计算总工作时间
        total_hours = (end_dt - start_dt).total_seconds() / 3600
        
        return max(0, total_hours)
    
    def _clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """数据清理"""
        # 移除完全空白的行
        df = df.dropna(how='all')
        
        # 填充缺失的栋别信息
        if '栋别' in df.columns:
            df['栋别'] = df['栋别'].fillna('未知')
        
        # 填充缺失的作业类型
        if '作业类型' in df.columns:
            df['作业类型'] = df['作业类型'].fillna('其他')
        
        # 移除异常工时数据（超过24小时或小于0）
        if '单任务工时' in df.columns:
            df = df[(df['单任务工时'] >= 0) & (df['单任务工时'] <= 24)]
        
        return df
    
    def _validate_data(self, df: pd.DataFrame) -> None:
        """数据验证"""
        required_columns = ['日期', '栋别', '作业类型']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            raise ValueError(f"缺少必要的列: {missing_columns}")
        
        if len(df) == 0:
            raise ValueError("数据文件为空")
    
    def calculate_data_quality(self, df: pd.DataFrame) -> float:
        """计算数据质量分数"""
        if df is None or len(df) == 0:
            return 0.0
        
        quality_factors = []
        
        # 完整性检查
        completeness = 1 - (df.isnull().sum().sum() / (len(df) * len(df.columns)))
        quality_factors.append(completeness * 0.4)
        
        # 一致性检查
        consistency = 1.0  # 简化实现
        if '栋别' in df.columns:
            valid_buildings = df['栋别'].isin(['A', 'B', 'C', '未知']).mean()
            consistency *= valid_buildings
        quality_factors.append(consistency * 0.3)
        
        # 准确性检查
        accuracy = 1.0
        if '单任务工时' in df.columns:
            valid_hours = ((df['单任务工时'] >= 0) & (df['单任务工时'] <= 12)).mean()
            accuracy *= valid_hours
        quality_factors.append(accuracy * 0.3)
        
        return sum(quality_factors)
    
    @st.cache_data
    def get_summary_stats(_self, df: pd.DataFrame) -> Dict[str, Any]:
        """获取数据摘要统计"""
        if df is None or len(df) == 0:
            return {}
        
        stats = {
            'total_records': len(df),
            'date_range': {
                'start': df['标准日期'].min() if '标准日期' in df.columns else None,
                'end': df['标准日期'].max() if '标准日期' in df.columns else None
            },
            'buildings': df['栋别'].unique().tolist() if '栋别' in df.columns else [],
            'work_types': df['作业类型'].unique().tolist() if '作业类型' in df.columns else [],
            'total_work_hours': df['单任务工时'].sum() if '单任务工时' in df.columns else 0,
            'avg_work_hours': df['单任务工时'].mean() if '单任务工时' in df.columns else 0
        }
        
        return stats
    
    def calculate_daily_workload(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算每日工作负荷"""
        if df is None or len(df) == 0:
            return pd.DataFrame()
        
        # 按日期和栋别分组计算
        daily_workload = df.groupby(['标准日期', '栋别']).agg({
            '单任务工时': ['sum', 'count', 'mean'],
            '作业类型': lambda x: x.nunique()
        }).round(2)
        
        # 重命名列
        daily_workload.columns = ['总工时', '任务数量', '平均工时', '作业类型数']
        daily_workload = daily_workload.reset_index()
        
        # 计算加班工时
        daily_workload['加班工时'] = (daily_workload['总工时'] - 8).clip(lower=0)
        
        return daily_workload
    
    def detect_anomalies(self, df: pd.DataFrame) -> Dict[str, Any]:
        """检测数据异常"""
        anomalies = {
            'excessive_overtime': [],
            'zero_worktime': [],
            'missing_data': [],
            'unusual_patterns': []
        }
        
        if df is None or len(df) == 0:
            return anomalies
        
        # 检测过度加班
        if '单任务工时' in df.columns:
            excessive = df[df['单任务工时'] > 10]
            anomalies['excessive_overtime'] = excessive.to_dict('records')
        
        # 检测零工时
        if '单任务工时' in df.columns:
            zero_time = df[df['单任务工时'] == 0]
            anomalies['zero_worktime'] = zero_time.to_dict('records')
        
        # 检测缺失数据
        missing_data = df[df.isnull().any(axis=1)]
        anomalies['missing_data'] = missing_data.to_dict('records')
        
        return anomalies
