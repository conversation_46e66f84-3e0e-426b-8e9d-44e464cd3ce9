# 数据准确性验证结果
print('🔍 数据准确性验证结果')
print('=' * 50)

excel_data = {
    'JT028': {'计划': 69.0, '实绩': 69.0, '空箱': 67.47},
    'JT026': {'计划': 86.15, '实绩': 86.15, '空箱': 69.0},
    'JH011-SH': {'计划': 6.49, '实绩': 6.49, '空箱': 6.49},
    'JH027-SB': {'计划': 13.1, '实绩': 15.58, '空箱': 12.15},
    'JH027-SC': {'计划': 48.22, '实绩': 28.25, '空箱': 28.25},
    'JH027-SD': {'计划': 35.15, '实绩': 46.8, '空箱': 43.98}
}

html_data = {
    'JT028': {'计划': 69.00, '实绩': 69.00, '空箱': 67.47},
    'JT026': {'计划': 86.15, '实绩': 86.15, '空箱': 69.00},
    'JH011-SH': {'计划': 6.49, '实绩': 6.49, '空箱': 6.49},
    'JH027-SB': {'计划': 13.10, '实绩': 15.57, '空箱': 12.15},
    'JH027-SC': {'计划': 48.23, '实绩': 28.25, '空箱': 28.25},
    'JH027-SD': {'计划': 35.15, '实绩': 46.80, '空箱': 43.98}
}

print('机种\t\t类别\t\tExcel数据\tHTML数据\t差异\t\t状态')
print('-' * 80)

errors_found = []

for machine in excel_data.keys():
    for category in ['计划', '实绩', '空箱']:
        excel_val = excel_data[machine][category]
        html_val = html_data[machine][category]
        diff = abs(excel_val - html_val)
        
        if diff > 0.001:  # 允许0.001的误差
            status = '❌ 需要修正'
            errors_found.append({
                'machine': machine,
                'category': category,
                'excel': excel_val,
                'html': html_val,
                'diff': diff
            })
        else:
            status = '✅ 正确'
        
        print(f'{machine}\t{category}\t\t{excel_val}\t\t{html_val}\t\t{diff:.3f}\t\t{status}')

print('\n📊 验证总结:')
if errors_found:
    print(f'❌ 发现 {len(errors_found)} 个数据错误需要修正:')
    for error in errors_found:
        print(f'  • {error["machine"]} {error["category"]}: {error["html"]} → {error["excel"]} (差异: {error["diff"]:.3f})')
else:
    print('✅ 所有数据都是准确的！')

# 生成修正建议
if errors_found:
    print('\n🔧 修正建议:')
    print('需要在HTML文件中更新以下数据:')
    for error in errors_found:
        if error['category'] == '计划':
            array_name = 'planData'
        elif error['category'] == '实绩':
            array_name = 'actualData'
        else:
            array_name = 'emptyBoxData'
        
        machines = ['JT028', 'JT026', 'JH011-SH', 'JH027-SB', 'JH027-SC', 'JH027-SD']
        index = machines.index(error['machine'])
        print(f'  {array_name}[{index}]: {error["html"]} → {error["excel"]}')

print('\n📋 具体修正内容:')
if errors_found:
    for error in errors_found:
        machines = ['JT028', 'JT026', 'JH011-SH', 'JH027-SB', 'JH027-SC', 'JH027-SD']
        index = machines.index(error['machine'])
        if error['category'] == '计划':
            print(f'planData[{index}] = {error["excel"]}  // {error["machine"]} 生产计划')
        elif error['category'] == '实绩':
            print(f'actualData[{index}] = {error["excel"]}  // {error["machine"]} 生产实绩')
        else:
            print(f'emptyBoxData[{index}] = {error["excel"]}  // {error["machine"]} 空箱纳入')
