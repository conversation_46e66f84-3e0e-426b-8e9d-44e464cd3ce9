#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图表组件模块
"""

import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from config.themes import AppleTheme

class ChartGenerator:
    """图表生成器"""
    
    def __init__(self):
        self.colors = AppleTheme.get_color_palette()
        self.config = AppleTheme.get_chart_config()
    
    def create_worktime_trend_chart(self, daily_data: pd.DataFrame) -> go.Figure:
        """创建工时趋势图"""
        fig = go.Figure()
        
        # 按栋别分组绘制趋势线
        for i, building in enumerate(daily_data['栋别'].unique()):
            building_data = daily_data[daily_data['栋别'] == building]
            
            fig.add_trace(go.Scatter(
                x=building_data['标准日期'],
                y=building_data['总工时'],
                mode='lines+markers',
                name=f'{building}栋',
                line=dict(color=self.colors[i], width=3),
                marker=dict(size=8, color=self.colors[i]),
                hovertemplate='<b>%{fullData.name}</b><br>' +
                             '日期: %{x}<br>' +
                             '总工时: %{y:.1f}小时<extra></extra>'
            ))
        
        # 添加8小时基准线
        fig.add_hline(
            y=8, 
            line_dash="dash", 
            line_color="red",
            annotation_text="正常工时(8小时)",
            annotation_position="top right"
        )
        
        fig.update_layout(
            title=dict(
                text="📈 每日工时趋势分析",
                font=dict(size=18, color=AppleTheme.COLORS['gray_800'])
            ),
            xaxis_title="日期",
            yaxis_title="工时 (小时)",
            hovermode='x unified',
            showlegend=True,
            height=400,
            plot_bgcolor='white',
            paper_bgcolor='white'
        )
        
        return fig
    
    def create_overtime_distribution_chart(self, daily_data: pd.DataFrame) -> go.Figure:
        """创建加班工时分布图"""
        fig = go.Figure()
        
        buildings = daily_data['栋别'].unique()
        
        for i, building in enumerate(buildings):
            building_data = daily_data[daily_data['栋别'] == building]
            
            fig.add_trace(go.Bar(
                x=building_data['标准日期'],
                y=building_data['加班工时'],
                name=f'{building}栋',
                marker_color=self.colors[i],
                hovertemplate='<b>%{fullData.name}</b><br>' +
                             '日期: %{x}<br>' +
                             '加班工时: %{y:.1f}小时<extra></extra>'
            ))
        
        fig.update_layout(
            title=dict(
                text="⏰ 每日加班工时分布",
                font=dict(size=18, color=AppleTheme.COLORS['gray_800'])
            ),
            xaxis_title="日期",
            yaxis_title="加班工时 (小时)",
            barmode='group',
            hovermode='x unified',
            height=400,
            plot_bgcolor='white',
            paper_bgcolor='white'
        )
        
        return fig
    
    def create_building_workload_pie_chart(self, daily_data: pd.DataFrame) -> go.Figure:
        """创建栋别工作负荷饼图"""
        building_totals = daily_data.groupby('栋别')['总工时'].sum()
        
        fig = go.Figure(data=[go.Pie(
            labels=[f'{building}栋' for building in building_totals.index],
            values=building_totals.values,
            hole=0.4,
            marker=dict(colors=self.colors[:len(building_totals)]),
            textinfo='label+percent+value',
            texttemplate='<b>%{label}</b><br>%{percent}<br>%{value:.1f}小时',
            hovertemplate='<b>%{label}</b><br>' +
                         '工时: %{value:.1f}小时<br>' +
                         '占比: %{percent}<extra></extra>'
        )])
        
        fig.update_layout(
            title=dict(
                text="🏢 各栋别工作负荷分布",
                font=dict(size=18, color=AppleTheme.COLORS['gray_800'])
            ),
            height=400,
            showlegend=True,
            plot_bgcolor='white',
            paper_bgcolor='white'
        )
        
        return fig
    
    def create_worktype_distribution_chart(self, df: pd.DataFrame) -> go.Figure:
        """创建作业类型分布图"""
        worktype_counts = df['作业类型'].value_counts()
        
        fig = go.Figure(data=[go.Bar(
            x=worktype_counts.index,
            y=worktype_counts.values,
            marker=dict(
                color=worktype_counts.values,
                colorscale='Blues',
                showscale=True,
                colorbar=dict(title="任务数量")
            ),
            text=worktype_counts.values,
            textposition='auto',
            hovertemplate='<b>%{x}</b><br>' +
                         '任务数量: %{y}<br>' +
                         '占比: %{y}/%{sum(y):.1%}<extra></extra>'
        )])
        
        fig.update_layout(
            title=dict(
                text="📋 作业类型分布统计",
                font=dict(size=18, color=AppleTheme.COLORS['gray_800'])
            ),
            xaxis_title="作业类型",
            yaxis_title="任务数量",
            height=400,
            plot_bgcolor='white',
            paper_bgcolor='white'
        )
        
        return fig
    
    def create_heatmap_chart(self, daily_data: pd.DataFrame) -> go.Figure:
        """创建工时热力图"""
        # 准备热力图数据
        pivot_data = daily_data.pivot_table(
            values='总工时', 
            index='栋别', 
            columns=daily_data['标准日期'].dt.strftime('%m-%d'),
            fill_value=0
        )
        
        fig = go.Figure(data=go.Heatmap(
            z=pivot_data.values,
            x=pivot_data.columns,
            y=[f'{building}栋' for building in pivot_data.index],
            colorscale='Reds',
            hoverongaps=False,
            hovertemplate='<b>%{y}</b><br>' +
                         '日期: %{x}<br>' +
                         '工时: %{z:.1f}小时<extra></extra>'
        ))
        
        fig.update_layout(
            title=dict(
                text="🔥 工时强度热力图",
                font=dict(size=18, color=AppleTheme.COLORS['gray_800'])
            ),
            xaxis_title="日期",
            yaxis_title="栋别",
            height=300,
            plot_bgcolor='white',
            paper_bgcolor='white'
        )
        
        return fig
    
    def create_efficiency_radar_chart(self, building_stats: pd.DataFrame) -> go.Figure:
        """创建效率雷达图"""
        fig = go.Figure()
        
        # 定义评估维度
        dimensions = ['工时效率', '任务完成率', '负荷均衡度', '时间利用率', '质量指标']
        
        for i, building in enumerate(building_stats.index):
            # 模拟计算各维度得分（实际应用中需要根据具体业务逻辑计算）
            scores = [
                min(100, 800 / building_stats.loc[building, '平均工时'] * 10),  # 工时效率
                min(100, building_stats.loc[building, '任务数量'] / 20 * 100),  # 任务完成率
                100 - abs(building_stats.loc[building, '总工时'] - building_stats['总工时'].mean()) / building_stats['总工时'].mean() * 100,  # 负荷均衡度
                min(100, building_stats.loc[building, '总工时'] / 12 * 100),  # 时间利用率
                85 + np.random.normal(0, 5)  # 质量指标（模拟数据）
            ]
            
            fig.add_trace(go.Scatterpolar(
                r=scores,
                theta=dimensions,
                fill='toself',
                name=f'{building}栋',
                line=dict(color=self.colors[i]),
                fillcolor=self.colors[i],
                opacity=0.3
            ))
        
        fig.update_layout(
            title=dict(
                text="📊 综合效率雷达图",
                font=dict(size=18, color=AppleTheme.COLORS['gray_800'])
            ),
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 100]
                )
            ),
            height=400,
            showlegend=True,
            plot_bgcolor='white',
            paper_bgcolor='white'
        )
        
        return fig
    
    def create_timeline_gantt_chart(self, df: pd.DataFrame, max_tasks: int = 50) -> go.Figure:
        """创建时间线甘特图"""
        # 限制显示的任务数量以提高性能
        sample_df = df.head(max_tasks).copy()
        
        fig = go.Figure()
        
        colors_map = {building: self.colors[i] for i, building in enumerate(df['栋别'].unique())}
        
        for idx, row in sample_df.iterrows():
            if pd.notna(row['起始时间_parsed']) and pd.notna(row['截止时间_parsed']):
                start_datetime = pd.Timestamp.combine(row['标准日期'], row['起始时间_parsed'])
                end_datetime = pd.Timestamp.combine(row['标准日期'], row['截止时间_parsed'])
                
                fig.add_trace(go.Scatter(
                    x=[start_datetime, end_datetime],
                    y=[idx, idx],
                    mode='lines+markers',
                    line=dict(color=colors_map.get(row['栋别'], self.colors[0]), width=8),
                    marker=dict(size=8),
                    name=f"{row['栋别']}栋-{row['作业类型']}",
                    showlegend=False,
                    hovertemplate=f'<b>{row["栋别"]}栋 - {row["作业类型"]}</b><br>' +
                                 f'开始: {row["起始时间"]}<br>' +
                                 f'结束: {row["截止时间"]}<br>' +
                                 f'工时: {row["单任务工时"]:.1f}小时<extra></extra>'
                ))
        
        fig.update_layout(
            title=dict(
                text="📅 作业时间线甘特图",
                font=dict(size=18, color=AppleTheme.COLORS['gray_800'])
            ),
            xaxis_title="时间",
            yaxis_title="任务序号",
            height=600,
            hovermode='closest',
            plot_bgcolor='white',
            paper_bgcolor='white'
        )
        
        return fig
