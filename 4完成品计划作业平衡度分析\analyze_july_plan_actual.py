import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json

def analyze_july_plan_actual():
    try:
        # 读取Excel文件
        df = pd.read_excel('7月计划与实际比对/作业登记表ABC栋7月.xlsx', sheet_name=0)
        
        print("=== 7月计划与实际对比数据分析 ===")
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        
        # 处理日期格式 (7/1 表示7月1日)
        def parse_date(date_str):
            if pd.isna(date_str):
                return None
            try:
                # 处理不同的日期格式
                if isinstance(date_str, datetime):
                    return date_str.date()

                date_str = str(date_str).strip()
                if '/' in date_str and len(date_str.split('/')) == 2:
                    month, day = date_str.split('/')
                    return datetime(2025, int(month), int(day)).date()
                elif '2025' in date_str:
                    # 处理已经是完整日期格式的情况
                    return pd.to_datetime(date_str).date()
                return None
            except:
                return None
        
        # 处理时间格式
        def parse_time(time_str):
            if pd.isna(time_str):
                return None
            try:
                time_str = str(time_str).strip()
                if ':' in time_str:
                    hour, minute = time_str.split(':')
                    return timedelta(hours=int(hour), minutes=int(minute))
                return None
            except:
                return None
        
        # 应用解析函数
        df['日期_解析'] = df['日期'].apply(parse_date)
        df['起始时间_解析'] = df['起始时间'].apply(parse_time)
        df['截止时间_解析'] = df['截止时间'].apply(parse_time)
        df['作业时长_解析'] = df['作业时长'].apply(parse_time)
        df['实际开始时间_解析'] = df['实际开始时间'].apply(parse_time)
        df['实际结束时间_解析'] = df['实际结束时间'].apply(parse_time)
        df['实际作业时长_解析'] = df['实际作业时长'].apply(parse_time)
        
        print("\n=== 日期格式分析 ===")
        unique_dates = df['日期'].unique()
        print(f"唯一日期数量: {len(unique_dates)}")
        print(f"日期样例: {unique_dates[:10]}")
        
        # 解析后的日期统计
        valid_dates = df['日期_解析'].dropna()
        if len(valid_dates) > 0:
            print(f"解析后日期范围: {valid_dates.min()} 到 {valid_dates.max()}")
            print(f"记录天数: {df['日期_解析'].nunique()}")
        
        print("\n=== 基本统计信息 ===")
        print(f"总记录数: {len(df)}")
        print(f"栋别分布:")
        print(df['栋别'].value_counts())
        
        print(f"\n作业类型分布:")
        print(df['作业类型'].value_counts())
        
        print(f"\n方向分布:")
        print(df['方向'].value_counts().head(10))
        
        print("\n=== 计划与实际对比分析 ===")
        
        # 检查有实际时间记录的数据
        has_actual = df[df['实际开始时间'].notna()]
        print(f"有实际时间记录的作业数: {len(has_actual)}")
        print(f"实际执行率: {len(has_actual)/len(df)*100:.1f}%")
        
        # 异常分析
        has_exception = df[df['异常原因'].notna()]
        print(f"有异常记录的作业数: {len(has_exception)}")
        print(f"异常率: {len(has_exception)/len(df)*100:.1f}%")
        
        if len(has_exception) > 0:
            print("异常原因分布:")
            print(has_exception['异常原因'].value_counts())
        
        # 加班分析
        has_overtime = df[df['加班原因'].notna()]
        print(f"\n有加班记录的作业数: {len(has_overtime)}")
        print(f"加班率: {len(has_overtime)/len(df)*100:.1f}%")
        
        if len(has_overtime) > 0:
            print("加班原因分布:")
            print(has_overtime['加班原因'].value_counts())
        
        print("\n=== 作业内容分析 ===")
        content_counts = df['作业内容'].value_counts()
        print("作业内容分布 (前15):")
        print(content_counts.head(15))
        
        print("\n=== 数量分析 ===")
        df['数量_数值'] = pd.to_numeric(df['数量'], errors='coerce')
        quantity_stats = df['数量_数值'].describe()
        print("数量统计:")
        print(quantity_stats)
        
        print("\n=== 缺失值分析 ===")
        missing_values = df.isnull().sum()
        print("各列缺失值数量:")
        for col, missing in missing_values.items():
            if missing > 0:
                print(f"{col}: {missing} ({missing/len(df)*100:.1f}%)")
        
        # 按日期统计
        print("\n=== 每日作业统计 ===")
        daily_stats = df.groupby('日期_解析').agg({
            '数量_数值': lambda x: x.sum(),
            '作业时长_解析': lambda x: sum([t.total_seconds()/3600 for t in x if pd.notna(t)]),
            '栋别': 'count',
            '异常原因': lambda x: x.notna().sum(),
            '加班原因': lambda x: x.notna().sum()
        }).round(2)
        daily_stats.columns = ['总数量', '总作业时长(小时)', '作业次数', '异常次数', '加班次数']
        daily_stats['异常率%'] = (daily_stats['异常次数'] / daily_stats['作业次数'] * 100).round(1)
        daily_stats['加班率%'] = (daily_stats['加班次数'] / daily_stats['作业次数'] * 100).round(1)
        
        print(daily_stats.head(15))
        
        # 按栋别统计
        print("\n=== 栋别统计 ===")
        building_stats = df.groupby('栋别').agg({
            '数量_数值': lambda x: x.sum(),
            '作业时长_解析': lambda x: sum([t.total_seconds()/3600 for t in x if pd.notna(t)]),
            '日期': 'count',
            '异常原因': lambda x: x.notna().sum(),
            '加班原因': lambda x: x.notna().sum()
        }).round(2)
        building_stats.columns = ['总数量', '总作业时长(小时)', '作业次数', '异常次数', '加班次数']
        building_stats['异常率%'] = (building_stats['异常次数'] / building_stats['作业次数'] * 100).round(1)
        building_stats['加班率%'] = (building_stats['加班次数'] / building_stats['作业次数'] * 100).round(1)
        
        print(building_stats)
        
        # 保存处理后的数据
        df.to_csv('7月计划实际对比_分析.csv', index=False, encoding='utf-8-sig')
        print("\n处理后的数据已保存到 '7月计划实际对比_分析.csv'")
        
        return df
        
    except Exception as e:
        print(f'分析数据时出错: {e}')
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    df = analyze_july_plan_actual()
