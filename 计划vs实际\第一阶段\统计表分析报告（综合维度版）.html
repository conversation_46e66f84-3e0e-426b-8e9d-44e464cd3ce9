<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
  <title>
   统计表分析报告（综合维度版）
  </title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js">
  </script>
  <style>
   * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.5em;
            color: #2c3e50;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header .subtitle {
            font-size: 1.2em;
            color: #7f8c8d;
            margin-bottom: 20px;
        }
        
        .header .meta {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .meta-item {
            background: #ecf0f1;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 0.9em;
            color: #34495e;
        }
        
        .card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        
        .card h3 {
            color: #34495e;
            margin: 20px 0 15px 0;
            font-size: 1.3em;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            width: 100%;
            margin: 20px 0;
            background: #fff;
            border-radius: 8px;
            padding: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .insights {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #3498db;
        }
        
        .insights h4 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .insights ul {
            list-style-type: none;
            padding: 0;
        }
        
        .insights li {
            margin: 10px 0;
            padding-left: 20px;
            position: relative;
        }
        
        .insights li:before {
            content: "▶";
            color: #3498db;
            position: absolute;
            left: 0;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        
        .alert-danger {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        
        .comparison-table th {
            background: #3498db;
            color: white;
            font-weight: 600;
        }
        
        .comparison-table tr:hover {
            background: #f8f9fa;
        }
        
        .status-excellent { color: #27ae60; font-weight: bold; }
        .status-good { color: #f39c12; font-weight: bold; }
        .status-warning { color: #e74c3c; font-weight: bold; }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header .meta {
                flex-direction: column;
                align-items: center;
            }
            
            .card {
                padding: 20px;
            }
        }
    

        /* 作业日历热力图样式 */
.chart-container {
min-height: 400px;
margin: 20px 0;
}
.stats-grid {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
gap: 20px;
margin-bottom: 30px;
}
.stat-card {
background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
color: white;
padding: 20px;
border-radius: 10px;
text-align: center;
}
.stat-number {
font-size: 2rem;
font-weight: bold;
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
opacity: 0.9;
}
.overtime-alert {
background-color: #f8d7da;
border: 1px solid #f5c6cb;
color: #721c24;
padding: 15px;
border-radius: 8px;
margin: 15px 0;
}
.chart-caption {
text-align: center;
color: #666;
font-size: 0.9rem;
margin-top: 10px;
font-style: italic;
}
.two-column {
display: grid;
grid-template-columns: 1fr 1fr;
gap: 25px;
}
.two-column {
grid-template-columns: 1fr;
}
  </style>
 </head>
 <body>
  <div class="container">
   <div class="header">
    <h1>
     📊 统计表分析报告（综合维度版）
    </h1>
    <div class="subtitle">
     基于出荷统计表的8-9月数据分析与洞察
    </div>
    <div class="meta">
     <div class="meta-item">
      📅 分析期间：2025年8月-9月
     </div>
     <div class="meta-item">
      🏭 机种数量：6个主力机种
     </div>
     <div class="meta-item">
      📊 分析天数：61天
     </div>
     <div class="meta-item">
      🔍 分析维度：生产计划vs实绩、空箱库存
     </div>
    </div>
   </div>
   <!-- 执行摘要 -->
   <div class="card">
    <h2>
     📋 执行摘要
    </h2>
    <div class="metrics-grid">
     <div class="metric-card">
      <div class="metric-value">
       102.0%
      </div>
      <div class="metric-label">
       平均计划执行率
      </div>
     </div>
     <div class="metric-card">
      <div class="metric-value">
       258
      </div>
      <div class="metric-label">
       总作业量（车辆）
      </div>
     </div>
     <div class="metric-card">
      <div class="metric-value">
       4.2
      </div>
      <div class="metric-label">
       日均作业量（车辆）
      </div>
     </div>
     <div class="metric-card">
      <div class="metric-value">
       61
      </div>
      <div class="metric-label">
       分析天数
      </div>
     </div>
    </div>
    <div class="insights">
     <h4>
      🎯 关键发现
     </h4>
     <ul>
      <li>
       <strong>
        JH027-SD机种表现突出
       </strong>
       ：执行率达133.1%，超额完成生产计划
      </li>
      <li>
       <strong>
        JH027-SC机种需要关注
       </strong>
       ：执行率仅58.6%，存在显著产能不足
      </li>
      <li>
       <strong>
        JT系列机种表现稳定
       </strong>
       ：JT028和JT026执行率均达100%，完美匹配计划
      </li>
      <li>
       <strong>
        空箱库存管理差异明显
       </strong>
       ：JT026空箱不足17.15车辆，需要优化
      </li>
     </ul>
    </div>
   </div>
   <!-- 分析1：生产计划vs实际执行对比（按日维度） -->
   <div class="card">
    <h2>
     📊 生产计划vs实际执行对比分析（按日维度）
    </h2>
    <div class="chart-container">
     <canvas id="planVsActualChart">
     </canvas>
    </div>
    <h3>
     总量对比分析
    </h3>
    <table class="comparison-table">
     <thead>
      <tr>
       <th>
        机种
       </th>
       <th>
        计划总量
       </th>
       <th>
        实绩总量
       </th>
       <th>
        执行率
       </th>
       <th>
        工作天数
       </th>
       <th>
        状态
       </th>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td>
        <strong>
         JH027-SD
        </strong>
       </td>
       <td>
        35.15
       </td>
       <td>
        46.80
       </td>
       <td class="status-excellent">
        133.1%
       </td>
       <td>
        34
       </td>
       <td>
        ✅ 超额完成
       </td>
      </tr>
      <tr>
       <td>
        <strong>
         JH027-SB
        </strong>
       </td>
       <td>
        13.10
       </td>
       <td>
        15.57
       </td>
       <td class="status-excellent">
        118.9%
       </td>
       <td>
        14
       </td>
       <td>
        ✅ 超额完成
       </td>
      </tr>
      <tr>
       <td>
        <strong>
         JT028
        </strong>
       </td>
       <td>
        69.00
       </td>
       <td>
        69.00
       </td>
       <td class="status-excellent">
        100.0%
       </td>
       <td>
        52
       </td>
       <td>
        ✅ 完美执行
       </td>
      </tr>
      <tr>
       <td>
        <strong>
         JT026
        </strong>
       </td>
       <td>
        86.15
       </td>
       <td>
        86.15
       </td>
       <td class="status-excellent">
        100.0%
       </td>
       <td>
        48
       </td>
       <td>
        ✅ 完美执行
       </td>
      </tr>
      <tr>
       <td>
        <strong>
         JH011-SH
        </strong>
       </td>
       <td>
        6.49
       </td>
       <td>
        6.49
       </td>
       <td class="status-excellent">
        100.0%
       </td>
       <td>
        13
       </td>
       <td>
        ✅ 完美执行
       </td>
      </tr>
      <tr>
       <td>
        <strong>
         JH027-SC
        </strong>
       </td>
       <td>
        48.23
       </td>
       <td>
        28.25
       </td>
       <td class="status-warning">
        58.6%
       </td>
       <td>
        39
       </td>
       <td>
        ❌ 需改善
       </td>
      </tr>
     </tbody>
    </table>
    <h3>
     每日执行匹配度分析
    </h3>
    <div class="metrics-grid">
     <div class="metric-card" style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%);">
      <div class="metric-value">
       JT028
      </div>
      <div class="metric-label">
       完美匹配率100%
      </div>
     </div>
     <div class="metric-card" style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);">
      <div class="metric-value">
       JT026
      </div>
      <div class="metric-label">
       完美匹配率100%
      </div>
     </div>
     <div class="metric-card" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);">
      <div class="metric-value">
       JH027-SC
      </div>
      <div class="metric-label">
       匹配率最低35.9%
      </div>
     </div>
    </div>
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
     <div class="alert alert-success">
      <strong>
       ✅ 执行优秀机种
      </strong>
      <br/>
      •
      <strong>
       JT028
      </strong>
      ：100%完美匹配，执行率100%
      <br/>
      •
      <strong>
       JT026
      </strong>
      ：100%完美匹配，执行率100%
      <br/>
      •
      <strong>
       JH011-SH
      </strong>
      ：100%完美匹配，执行率100%
      <br/>
      • 执行稳定性极佳，偏差为零
     </div>
     <div class="alert alert-warning">
      <strong>
       ⚠️ 需要改善机种
      </strong>
      <br/>
      •
      <strong>
       JH027-SC
      </strong>
      ：仅35.9%完美匹配，执行率58.6%
      <br/>
      •
      <strong>
       JH027-SD
      </strong>
      ：44.1%完美匹配，虽超额但不稳定
      <br/>
      • 存在较大的日执行波动和计划偏差
     </div>
    </div>
    <h3>
     每日执行趋势分析
    </h3>
    <div class="chart-container">
     <canvas id="dailyTrendChart">
     </canvas>
    </div>
    <h3>
     各机种每日详细趋势对比
    </h3>
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
     <div class="chart-container" style="height: 300px;">
      <canvas id="jt028TrendChart">
      </canvas>
     </div>
     <div class="chart-container" style="height: 300px;">
      <canvas id="jt026TrendChart">
      </canvas>
     </div>
     <div class="chart-container" style="height: 300px;">
      <canvas id="jh011shTrendChart">
      </canvas>
     </div>
     <div class="chart-container" style="height: 300px;">
      <canvas id="jh027sbTrendChart">
      </canvas>
     </div>
     <div class="chart-container" style="height: 300px;">
      <canvas id="jh027scTrendChart">
      </canvas>
     </div>
     <div class="chart-container" style="height: 300px;">
      <canvas id="jh027sdTrendChart">
      </canvas>
     </div>
    </div>
    <div class="insights">
     <h4>
      📈 深度分析洞察
     </h4>
     <ul>
      <li>
       <strong>
        执行模式分化明显
       </strong>
       ：JT系列执行完美，JH027系列差异巨大
      </li>
      <li>
       <strong>
        稳定性表现优异
       </strong>
       ：JT028、JT026、JH011-SH三个机种实现零偏差
      </li>
      <li>
       <strong>
        产能利用差异
       </strong>
       ：JH027-SC产能严重不足，JH027-SD产能过剩
      </li>
      <li>
       <strong>
        计划准确性
       </strong>
       ：JT系列计划制定精准，执行控制到位
      </li>
      <li>
       <strong>
        日趋势特征
       </strong>
       ：从每日趋势图可以看出工作日和周末的明显差异
      </li>
     </ul>
    </div>
   </div>
   <!-- 分析2：空箱纳入与库存分析（按日维度） -->
   <div class="card">
    <h2>
     📦 空箱纳入与库存分析（按日维度）
    </h2>
    <div class="chart-container">
     <canvas id="emptyBoxChart">
     </canvas>
    </div>
    <h3>
     总量对比分析
    </h3>
    <table class="comparison-table">
     <thead>
      <tr>
       <th>
        机种
       </th>
       <th>
        空箱总量
       </th>
       <th>
        实绩总量
       </th>
       <th>
        差异
       </th>
       <th>
        匹配状态
       </th>
       <th>
        评级
       </th>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td>
        <strong>
         JH011-SH
        </strong>
       </td>
       <td>
        6.49
       </td>
       <td>
        6.49
       </td>
       <td class="status-excellent">
        0.00
       </td>
       <td>
        完美匹配
       </td>
       <td>
        ⭐⭐⭐⭐⭐
       </td>
      </tr>
      <tr>
       <td>
        <strong>
         JH027-SC
        </strong>
       </td>
       <td>
        28.25
       </td>
       <td>
        28.25
       </td>
       <td class="status-excellent">
        0.00
       </td>
       <td>
        完美匹配
       </td>
       <td>
        ⭐⭐⭐⭐⭐
       </td>
      </tr>
      <tr>
       <td>
        <strong>
         JT028
        </strong>
       </td>
       <td>
        67.47
       </td>
       <td>
        69.00
       </td>
       <td class="status-good">
        -1.53
       </td>
       <td>
        轻微不足
       </td>
       <td>
        ⭐⭐⭐⭐
       </td>
      </tr>
      <tr>
       <td>
        <strong>
         JH027-SD
        </strong>
       </td>
       <td>
        43.98
       </td>
       <td>
        46.80
       </td>
       <td class="status-good">
        -2.82
       </td>
       <td>
        轻微不足
       </td>
       <td>
        ⭐⭐⭐⭐
       </td>
      </tr>
      <tr>
       <td>
        <strong>
         JH027-SB
        </strong>
       </td>
       <td>
        12.15
       </td>
       <td>
        15.57
       </td>
       <td class="status-warning">
        -3.42
       </td>
       <td>
        明显不足
       </td>
       <td>
        ⭐⭐⭐
       </td>
      </tr>
      <tr>
       <td>
        <strong>
         JT026
        </strong>
       </td>
       <td>
        69.00
       </td>
       <td>
        86.15
       </td>
       <td class="status-warning">
        -17.15
       </td>
       <td>
        严重不足
       </td>
       <td>
        ⭐⭐
       </td>
      </tr>
     </tbody>
    </table>
    <h3>
     每日匹配度评估
    </h3>
    <div class="metrics-grid">
     <div class="metric-card" style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%);">
      <div class="metric-value">
       JH011-SH
      </div>
      <div class="metric-label">
       完美匹配率100.0%
      </div>
     </div>
     <div class="metric-card" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);">
      <div class="metric-value">
       JT028
      </div>
      <div class="metric-label">
       完美匹配率97.8%
      </div>
     </div>
     <div class="metric-card" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);">
      <div class="metric-value">
       JT026
      </div>
      <div class="metric-label">
       完美匹配率0%
      </div>
     </div>
    </div>
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
     <div class="alert alert-success">
      <strong>
       ✅ 空箱管理优秀
      </strong>
      <br/>
      •
      <strong>
       JH011-SH
      </strong>
      ：总量完美匹配，匹配率100.0%
      <br/>
      •
      <strong>
       JH027-SC
      </strong>
      ：总量完美匹配，匹配率100.0%
      <br/>
      •
      <strong>
       JT028
      </strong>
      ：轻微不足1.53车辆，匹配率97.8%
      <br/>
      •
      <strong>
       JH027-SD
      </strong>
      ：匹配率94.0%，管理良好
      <br/>
      • 空箱供应相对稳定
     </div>
     <div class="alert alert-warning">
      <strong>
       ⚠️ 空箱管理需改善
      </strong>
      <br/>
      •
      <strong>
       JT026
      </strong>
      ：严重不足17.15车辆，匹配率80.1%
      <br/>
      •
      <strong>
       JH027-SB
      </strong>
      ：不足3.43车辆，匹配率78.0%
      <br/>
      • 需要优化空箱供应计划
     </div>
    </div>
    <h3>
     空箱供应波动性分析
    </h3>
    <div class="chart-container">
     <canvas id="emptyBoxVariationChart">
     </canvas>
    </div>
    <h3>
     各机种空箱纳入vs生产实际每日对比（每日差异）
    </h3>
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
     <div class="chart-container" style="height: 300px;">
      <canvas id="jt028EmptyBoxTrendChart">
      </canvas>
     </div>
     <div class="chart-container" style="height: 300px;">
      <canvas id="jt026EmptyBoxTrendChart">
      </canvas>
     </div>
     <div class="chart-container" style="height: 300px;">
      <canvas id="jh011shEmptyBoxTrendChart">
      </canvas>
     </div>
     <div class="chart-container" style="height: 300px;">
      <canvas id="jh027sbEmptyBoxTrendChart">
      </canvas>
     </div>
     <div class="chart-container" style="height: 300px;">
      <canvas id="jh027scEmptyBoxTrendChart">
      </canvas>
     </div>
     <div class="chart-container" style="height: 300px;">
      <canvas id="jh027sdEmptyBoxTrendChart">
      </canvas>
     </div>
    </div>
    <div class="insights">
     <h4>
      📊 空箱管理深度分析
     </h4>
     <ul>
      <li>
       <strong>
        供应精准度差异
       </strong>
       ：JH011-SH和JH027-SC实现完美总量匹配（100.0%）
      </li>
      <li>
       <strong>
        日常管理水平
       </strong>
       ：JH011-SH和JH027-SC匹配率最高（100.0%），管理最优
      </li>
      <li>
       <strong>
        供应相对充足
       </strong>
       ：JT028匹配率97.8%，JH027-SD匹配率94.0%，表现良好
      </li>
      <li>
       <strong>
        需要改善机种
       </strong>
       ：JT026匹配率80.1%，JH027-SB匹配率78.0%，有改善空间
      </li>
      <li>
       <strong>
        每日波动特征
       </strong>
       ：从每日对比图可以看出空箱供应与生产需求的时间差异
      </li>
      <li>
       <strong>
        整体管理水平
       </strong>
       ：平均匹配率91.6%，整体表现良好
      </li>
     </ul>
    </div>
    <div class="alert alert-info">
     <strong>
      📈 改善建议：
     </strong>
     <ul style="margin: 10px 0; padding-left: 20px;">
      <li>
       <strong>
        优化JT026和JH027-SB
       </strong>
       ：提升匹配率至90%以上
      </li>
      <li>
       <strong>
        学习最佳实践
       </strong>
       ：推广JH011-SH和JH027-SC的完美匹配经验
      </li>
      <li>
       <strong>
        保持优秀水平
       </strong>
       ：维持JT028和JH027-SD的高匹配率
      </li>
      <li>
       <strong>
        建立预警机制
       </strong>
       ：设置匹配率低于85%的预警阈值
      </li>
      <li>
       <strong>
        动态调配
       </strong>
       ：建立机种间空箱余缺调剂机制
      </li>
     </ul>
    </div>
   </div>
   <!-- 分析3：综合评估与改善建议 -->
   <div class="card">
    <h2 style="color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; margin-bottom: 30px;">
     📅 作业日历热力图与超时分析
    </h2>
   </div>
   <div class="stats-grid">
    <div class="stat-card">
     <div class="stat-number">
      583
     </div>
     <div class="stat-label">
      总作业任务数
     </div>
    </div>
    <div class="stat-card">
     <div class="stat-number">
      116
     </div>
     <div class="stat-label">
      超时作业任务数
     </div>
    </div>
    <div class="stat-card">
     <div class="stat-number">
      19.9%
     </div>
     <div class="stat-label">
      超时作业比例
     </div>
    </div>
    <div class="stat-card">
     <div class="stat-number">
      61
     </div>
     <div class="stat-label">
      作业天数
     </div>
    </div>
   </div>
   <div class="card">
    <div class="card-header">
     <i class="fas fa-calendar-alt">
     </i>
     <h3 class="card-title">
      作业日历热力图
     </h3>
    </div>
    <div class="card-body">
     <div class="chart-container">
      <div id="calendarHeatmap" style="width: 100%; height: 500px;">
      </div>
      <p class="chart-caption">
       2025年8-9月作业数量日历热力图，颜色越深表示作业强度越高
      </p>
     </div>
     <div class="overtime-alert">
      <i class="fas fa-exclamation-triangle me-2">
      </i>
      <strong>
       作业强度分析：
      </strong>
      从热力图可以看出，作业分布不均匀。
                    最高强度达到
      <span class="highlight">
       19个任务/天
      </span>
      ，
                    超时作业占比
      <span class="highlight">
       19.9%
      </span>
      ，
                    需要关注作业负荷平衡和超时管理。
     </div>
    </div>
   </div>
   <div class="card">
    <div class="card-header">
     <i class="fas fa-building">
     </i>
     <h3 class="card-title">
      各栋别作业日历热力图
     </h3>
    </div>
    <div class="card-body">
     <div class="row">
      <div class="col-md-4">
       <h5 class="text-center mb-3">
        A栋作业热力图
       </h5>
       <div class="chart-container" style="height: 300px;">
        <div id="calendarHeatmapA" style="width: 100%; height: 100%;">
        </div>
       </div>
      </div>
      <div class="col-md-4">
       <h5 class="text-center mb-3">
        B栋作业热力图
       </h5>
       <div class="chart-container" style="height: 300px;">
        <div id="calendarHeatmapB" style="width: 100%; height: 100%;">
        </div>
       </div>
      </div>
      <div class="col-md-4">
       <h5 class="text-center mb-3">
        C栋作业热力图
       </h5>
       <div class="chart-container" style="height: 300px;">
        <div id="calendarHeatmapC" style="width: 100%; height: 100%;">
        </div>
       </div>
      </div>
     </div>
     <p class="chart-caption">
      各栋别8-9月作业分布热力图，可对比不同栋别的作业强度差异
     </p>
     <div class="overtime-alert">
      <i class="fas fa-info-circle me-2">
      </i>
      <strong>
       栋别对比分析：
      </strong>
      从分栋别热力图可以清晰看出，
      <span class="highlight">
       B栋作业密度最高且分布最广
      </span>
      ，
      <span class="highlight">
       A栋作业相对较少且集中
      </span>
      ，
      <span class="highlight">
       C栋作业强度中等但超时较多
      </span>
      。
                    建议重新平衡各栋别的作业分配。
     </div>
    </div>
   </div>
   <div class="card">
    <div class="card-header">
     <i class="fas fa-building">
     </i>
     <h3 class="card-title">
      各栋别作业分布
     </h3>
    </div>
    <div class="card-body">
     <div class="chart-container">
      <div id="buildingDistribution" style="width: 100%; height: 400px;">
      </div>
      <p class="chart-caption">
       A、B、C三栋作业量分布情况
      </p>
     </div>
    </div>
   </div>
   <div class="card">
    <div class="card-header">
     <i class="fas fa-clock">
     </i>
     <h3 class="card-title">
      各栋别超时作业分布
     </h3>
    </div>
    <div class="card-body">
     <div class="chart-container">
      <div id="overtimeBuildingDistribution" style="width: 100%; height: 400px;">
      </div>
      <p class="chart-caption">
       各栋别超过17:30标准工时的作业数量
      </p>
     </div>
    </div>
   </div>
   <div class="card">
    <div class="card-header">
     <i class="fas fa-chart-line">
     </i>
     <h3 class="card-title">
      超时作业趋势分析
     </h3>
    </div>
    <div class="card-body">
     <div class="chart-container">
      <div id="overtimeTrend" style="width: 100%; height: 400px;">
      </div>
      <p class="chart-caption">
       每日超时作业数量变化趋势
      </p>
     </div>
     <div class="overtime-alert">
      <i class="fas fa-info-circle me-2">
      </i>
      <strong>
       超时分析结论：
      </strong>
      总计
      <span class="highlight">
       116个超时作业
      </span>
      ，
                    占总作业量的
      <span class="highlight">
       19.9%
      </span>
      。
                    超时作业主要集中在B栋，建议优化作业安排，合理分配工作负荷。
     </div>
    </div>
   </div>
   <div class="card">
    <div class="card-header">
     <i class="fas fa-table">
     </i>
     <h3 class="card-title">
      超时作业详细列表
     </h3>
    </div>
    <div class="card-body">
     <div class="table-responsive">
      <table class="table table-striped table-hover">
       <thead class="table-dark">
        <tr>
         <th>
          日期
         </th>
         <th>
          栋别
         </th>
         <th>
          起始时间
         </th>
         <th>
          截止时间
         </th>
         <th>
          作业内容
         </th>
         <th>
          作业时长
         </th>
         <th>
          数量
         </th>
        </tr>
       </thead>
       <tbody>
        <tr>
         <td>
          8/1
         </td>
         <td>
          B
         </td>
         <td>
          17:00
         </td>
         <td class="text-danger">
          <strong>
           19:30
          </strong>
         </td>
         <td>
          JT026-24电池
         </td>
         <td>
          2:30
         </td>
         <td>
          36.0
         </td>
        </tr>
        <tr>
         <td>
          8/1
         </td>
         <td>
          B
         </td>
         <td>
          19:30
         </td>
         <td class="text-danger">
          <strong>
           22:00
          </strong>
         </td>
         <td>
          JT026-24电池
         </td>
         <td>
          2:30
         </td>
         <td>
          36.0
         </td>
        </tr>
        <tr>
         <td>
          8/1
         </td>
         <td>
          B
         </td>
         <td>
          22:00
         </td>
         <td class="text-danger">
          <strong>
           00:30
          </strong>
         </td>
         <td>
          JT026-24电池
         </td>
         <td>
          2:30
         </td>
         <td>
          36.0
         </td>
        </tr>
        <tr>
         <td>
          8/1
         </td>
         <td>
          B
         </td>
         <td>
          16:00
         </td>
         <td class="text-danger">
          <strong>
           18:00
          </strong>
         </td>
         <td>
          JH027-SB周转箱
         </td>
         <td>
          2:00
         </td>
         <td>
          40.0
         </td>
        </tr>
        <tr>
         <td>
          8/1
         </td>
         <td>
          B
         </td>
         <td>
          00:30
         </td>
         <td class="text-danger">
          <strong>
           02:30
          </strong>
         </td>
         <td>
          JH027-SC待检品
         </td>
         <td>
          2:00
         </td>
         <td>
          40.0
         </td>
        </tr>
        <tr>
         <td>
          8/1
         </td>
         <td>
          B
         </td>
         <td>
          02:30
         </td>
         <td class="text-danger">
          <strong>
           04:30
          </strong>
         </td>
         <td>
          JH027-SC待检品
         </td>
         <td>
          2:00
         </td>
         <td>
          40.0
         </td>
        </tr>
        <tr>
         <td>
          8/4
         </td>
         <td>
          C
         </td>
         <td>
          16:30
         </td>
         <td class="text-danger">
          <strong>
           19:30
          </strong>
         </td>
         <td>
          JT028完成品
         </td>
         <td>
          3:00
         </td>
         <td>
          44.0
         </td>
        </tr>
        <tr>
         <td>
          8/5
         </td>
         <td>
          B
         </td>
         <td>
          17:30
         </td>
         <td class="text-danger">
          <strong>
           19:30
          </strong>
         </td>
         <td>
          JH027-SD周转箱
         </td>
         <td>
          2:00
         </td>
         <td>
          40.0
         </td>
        </tr>
        <tr>
         <td>
          8/5
         </td>
         <td>
          C
         </td>
         <td>
          16:30
         </td>
         <td class="text-danger">
          <strong>
           19:30
          </strong>
         </td>
         <td>
          JT028完成品
         </td>
         <td>
          3:00
         </td>
         <td>
          44.0
         </td>
        </tr>
        <tr>
         <td>
          8/5
         </td>
         <td>
          C
         </td>
         <td>
          19:30
         </td>
         <td class="text-danger">
          <strong>
           22:30
          </strong>
         </td>
         <td>
          JT028完成品
         </td>
         <td>
          3:00
         </td>
         <td>
          44.0
         </td>
        </tr>
        <tr>
         <td>
          8/5
         </td>
         <td>
          C
         </td>
         <td>
          22:30
         </td>
         <td class="text-danger">
          <strong>
           00:30
          </strong>
         </td>
         <td>
          US001
         </td>
         <td>
          2:00
         </td>
         <td>
          40.0
         </td>
        </tr>
        <tr>
         <td>
          8/5
         </td>
         <td>
          C
         </td>
         <td>
          00:30
         </td>
         <td class="text-danger">
          <strong>
           02:30
          </strong>
         </td>
         <td>
          US001
         </td>
         <td>
          2:00
         </td>
         <td>
          40.0
         </td>
        </tr>
        <tr>
         <td>
          8/5
         </td>
         <td>
          C
         </td>
         <td>
          02:30
         </td>
         <td class="text-danger">
          <strong>
           04:30
          </strong>
         </td>
         <td>
          US001
         </td>
         <td>
          2:00
         </td>
         <td>
          40.0
         </td>
        </tr>
        <tr>
         <td>
          8/6
         </td>
         <td>
          B
         </td>
         <td>
          16:30
         </td>
         <td class="text-danger">
          <strong>
           18:30
          </strong>
         </td>
         <td>
          JH027-SD待检品
         </td>
         <td>
          2:00
         </td>
         <td>
          40.0
         </td>
        </tr>
        <tr>
         <td>
          8/6
         </td>
         <td>
          C
         </td>
         <td>
          16:30
         </td>
         <td class="text-danger">
          <strong>
           19:30
          </strong>
         </td>
         <td>
          JT028完成品
         </td>
         <td>
          3:00
         </td>
         <td>
          44.0
         </td>
        </tr>
        <tr>
         <td>
          8/6
         </td>
         <td>
          C
         </td>
         <td>
          19:30
         </td>
         <td class="text-danger">
          <strong>
           22:30
          </strong>
         </td>
         <td>
          JT028完成品
         </td>
         <td>
          3:00
         </td>
         <td>
          44.0
         </td>
        </tr>
        <tr>
         <td>
          8/8
         </td>
         <td>
          B
         </td>
         <td>
          17:00
         </td>
         <td class="text-danger">
          <strong>
           19:30
          </strong>
         </td>
         <td>
          JT026-24电池
         </td>
         <td>
          2:30
         </td>
         <td>
          36.0
         </td>
        </tr>
        <tr>
         <td>
          8/8
         </td>
         <td>
          B
         </td>
         <td>
          19:30
         </td>
         <td class="text-danger">
          <strong>
           21:30
          </strong>
         </td>
         <td>
          JH027-SD待检品
         </td>
         <td>
          2:00
         </td>
         <td>
          40.0
         </td>
        </tr>
        <tr>
         <td>
          8/8
         </td>
         <td>
          B
         </td>
         <td>
          21:30
         </td>
         <td class="text-danger">
          <strong>
           23:30
          </strong>
         </td>
         <td>
          JH027-SD待检品
         </td>
         <td>
          2:00
         </td>
         <td>
          40.0
         </td>
        </tr>
        <tr>
         <td>
          8/12
         </td>
         <td>
          C
         </td>
         <td>
          17:30
         </td>
         <td class="text-danger">
          <strong>
           19:30
          </strong>
         </td>
         <td>
          US001
         </td>
         <td>
          2:00
         </td>
         <td>
          40.0
         </td>
        </tr>
        <tr>
         <td class="text-center text-muted" colspan="7">
          ... 还有 96 条超时记录
         </td>
        </tr>
       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="card">
    <h2>
     💡 综合评估与改善建议
    </h2>
    <h3>
     机种综合表现排名
    </h3>
    <div class="metrics-grid">
     <div class="metric-card" style="background: linear-gradient(135deg, #f1c40f 0%, #f39c12 100%);">
      <div class="metric-value">
       🥇
      </div>
      <div class="metric-label">
       JT028 &amp; JT026
       <br/>
       完美执行型
      </div>
     </div>
     <div class="metric-card" style="background: linear-gradient(135deg, #c0392b 0%, #e74c3c 100%);">
      <div class="metric-value">
       🥈
      </div>
      <div class="metric-label">
       JH011-SH
       <br/>
       稳定优秀型
      </div>
     </div>
     <div class="metric-card" style="background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);">
      <div class="metric-value">
       🥉
      </div>
      <div class="metric-label">
       JH027-SB
       <br/>
       超额完成型
      </div>
     </div>
    </div>
    <h3>
     短期改善措施（1个月内）
    </h3>
    <div class="alert alert-warning">
     <strong>
      🚨 紧急处理项目
     </strong>
     <ul style="margin: 10px 0; padding-left: 20px;">
      <li>
       <strong>
        JH027-SC产能提升
       </strong>
       ：执行率仅58.6%，需立即分析瓶颈原因
      </li>
      <li>
       <strong>
        JT026空箱供应
       </strong>
       ：不足17.15车辆，影响正常生产
      </li>
      <li>
       <strong>
        JH027-SD稳定性
       </strong>
       ：虽超额但波动大，需优化计划制定
      </li>
     </ul>
    </div>
    <h3>
     中期优化策略（3个月内）
    </h3>
    <div class="alert alert-info">
     <strong>
      📈 系统性改善
     </strong>
     <ul style="margin: 10px 0; padding-left: 20px;">
      <li>
       <strong>
        标杆学习
       </strong>
       ：推广JT028、JT026的完美执行经验
      </li>
      <li>
       <strong>
        预测模型
       </strong>
       ：建立基于历史数据的需求预测系统
      </li>
      <li>
       <strong>
        动态调配
       </strong>
       ：实现空箱库存的智能调度
      </li>
      <li>
       <strong>
        监控体系
       </strong>
       ：建立实时监控和预警机制
      </li>
     </ul>
    </div>
    <h3>
     预期效果
    </h3>
    <div class="metrics-grid">
     <div class="metric-card" style="background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);">
      <div class="metric-value">
       95%+
      </div>
      <div class="metric-label">
       目标执行率
      </div>
     </div>
     <div class="metric-card" style="background: linear-gradient(135deg, #3498db 0%, #5dade2 100%);">
      <div class="metric-value">
       80%+
      </div>
      <div class="metric-label">
       空箱匹配率
      </div>
     </div>
     <div class="metric-card" style="background: linear-gradient(135deg, #e67e22 0%, #f39c12 100%);">
      <div class="metric-value">
       ±0.3
      </div>
      <div class="metric-label">
       目标日偏差
      </div>
     </div>
    </div>
   </div>
   <!-- 数据来源说明 -->
   <div class="card">
    <h2>
     📊 数据来源与说明
    </h2>
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
     <div>
      <h4>
       📁 数据来源
      </h4>
      <ul>
       <li>
        <strong>
         主要数据文件
        </strong>
        ：出荷统计表7.18（更新版).xlsx
       </li>
       <li>
        <strong>
         分析期间
        </strong>
        ：2025年8月1日 - 9月30日
       </li>
       <li>
        <strong>
         数据天数
        </strong>
        ：61天完整数据
       </li>
       <li>
        <strong>
         机种范围
        </strong>
        ：6个主力生产机种
       </li>
      </ul>
     </div>
     <div>
      <h4>
       🔍 分析方法
      </h4>
      <ul>
       <li>
        <strong>
         完美匹配
        </strong>
        ：日偏差≤5%
       </li>
       <li>
        <strong>
         良好匹配
        </strong>
        ：日偏差≤15%
       </li>
       <li>
        <strong>
         空箱完美匹配
        </strong>
        ：日偏差≤0.1车辆
       </li>
       <li>
        <strong>
         空箱良好匹配
        </strong>
        ：日偏差≤0.5车辆
       </li>
      </ul>
     </div>
    </div>
    <div class="alert alert-success" style="margin-top: 20px;">
     <strong>
      ✅ 数据准确性保证
     </strong>
     ：本报告基于Excel原始数据直接提取，确保数据准确性和分析可靠性。所有计算均基于实际工作日数据，排除了无生产计划的机种和日期。
    </div>
   </div>
  </div>
  <script>
   // 8-9月分析数据
        const analysisData = {
            machines: ['JT028', 'JT026', 'JH011-SH', 'JH027-SB', 'JH027-SC', 'JH027-SD'],
            planData: [69.00, 86.15, 6.49, 13.10, 48.23, 35.15],
            actualData: [69.00, 86.15, 6.49, 15.58, 28.25, 46.80],
            emptyBoxData: [67.47, 69.00, 6.49, 12.15, 28.25, 43.98],
            executionRates: [100.0, 100.0, 100.0, 118.9, 58.6, 133.1],
            emptyBoxMatchRates: [97.8, 80.1, 100.0, 78.0, 100.0, 94.0]
        };

        // 每日趋势数据（8-9月61天）
                        const dailyTrendsData = {
            "JT028": {
                "dates": ["08-01", "08-02", "08-03", "08-04", "08-05", "08-06", "08-07", "08-08", "08-09", "08-10", "08-11", "08-12", "08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19", "08-20", "08-21", "08-22", "08-23", "08-24", "08-25", "08-26", "08-27", "08-28", "08-29", "08-30", "08-31", "09-01", "09-02", "09-03", "09-04", "09-05", "09-06", "09-07", "09-08", "09-09", "09-10", "09-11", "09-12", "09-13", "09-14", "09-15", "09-16", "09-17", "09-18", "09-19", "09-20", "09-21", "09-22", "09-23", "09-24", "09-25", "09-26", "09-27", "09-28", "09-29", "09-30"],
                "plan": [1.53333333333333, 0.0, 0.0, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.53333333333333, 0.0, 0.0, 1.53333333333333, 1.53333333333333, 1.15, 0.383333333333333, 0.0, 0.0, 0.0, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 0.0, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.15, 0.766666666666667, 0.766666666666667, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.15, 0.766666666666667, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.53333333333333, 0.766666666666667, 1.53333333333333, 1.53333333333333, 0.0],
                "actual": [1.53333333333333, 0.0, 0.0, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.53333333333333, 0.0, 0.0, 1.53333333333333, 1.53333333333333, 1.15, 0.383333333333333, 0.0, 0.0, 0.0, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 0.0, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.15, 0.766666666666667, 0.766666666666667, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.15, 0.766666666666667, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.53333333333333, 0.766666666666667, 1.53333333333333, 1.53333333333333, 0.0],
                "empty_box": [0.0, 0.0, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.53333333333333, 0.0, 0.0, 1.53333333333333, 1.53333333333333, 1.15, 0.383333333333333, 0.0, 0.0, 0.0, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 0.0, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.15, 0.766666666666667, 0.766666666666667, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.15, 0.766666666666667, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.53333333333333, 0.766666666666667, 1.53333333333333, 1.53333333333333, 0.0, 0.0]
            },
            "JT026": {
                "dates": ["08-01", "08-02", "08-03", "08-04", "08-05", "08-06", "08-07", "08-08", "08-09", "08-10", "08-11", "08-12", "08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19", "08-20", "08-21", "08-22", "08-23", "08-24", "08-25", "08-26", "08-27", "08-28", "08-29", "08-30", "08-31", "09-01", "09-02", "09-03", "09-04", "09-05", "09-06", "09-07", "09-08", "09-09", "09-10", "09-11", "09-12", "09-13", "09-14", "09-15", "09-16", "09-17", "09-18", "09-19", "09-20", "09-21", "09-22", "09-23", "09-24", "09-25", "09-26", "09-27", "09-28", "09-29", "09-30"],
                "plan": [0.95, 0.0, 0.0, 1.7, 1.9, 1.9, 1.9, 0.0, 0.0, 0.0, 1.7, 1.9, 1.9, 0.0, 0.0, 0.0, 0.0, 1.7, 1.9, 1.9, 1.9, 0.0, 1.9, 1.9, 1.9, 1.9, 1.575, 1.575, 0.0, 1.9, 1.9, 1.9, 1.9, 1.9, 1.9, 0.0, 1.9, 1.9, 1.9, 1.9, 1.9, 1.9, 0.95, 1.9, 1.9, 1.9, 1.9, 1.9, 1.9, 0.95, 1.9, 1.9, 1.9, 1.9, 1.9, 1.9, 0.95, 1.9, 1.9, 1.9, 0.0],
                "actual": [0.95, 0.0, 0.0, 1.7, 1.9, 1.9, 1.9, 0.0, 0.0, 0.0, 1.7, 1.9, 1.9, 0.0, 0.0, 0.0, 0.0, 1.7, 1.9, 1.9, 1.9, 0.0, 1.9, 1.9, 1.9, 1.9, 1.575, 1.575, 0.0, 1.9, 1.9, 1.9, 1.9, 1.9, 1.9, 0.0, 1.9, 1.9, 1.9, 1.9, 1.9, 1.9, 0.95, 1.9, 1.9, 1.9, 1.9, 1.9, 1.9, 0.95, 1.9, 1.9, 1.9, 1.9, 1.9, 1.9, 0.95, 1.9, 1.9, 1.9, 0.0],
                "empty_box": [2.0, 0.0, 2.0, 3.0, 3.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.0, 0.0, 3.0, 2.0, 2.0, 0.0, 2.0, 2.0, 0.0, 2.0, 2.0, 0.0, 0.0, 2.0, 2.0, 0.0, 2.0, 2.0, 0.0, 0.0, 2.0, 3.0, 0.0, 2.0, 2.0, 0.0, 0.0, 3.0, 2.0, 0.0, 2.0, 2.0, 0.0, 0.0, 2.0, 2.0, 0.0, 2.0, 2.0, 0.0, 2.0, 2.0, 3.0, 0.0, 0.0, 0.0, 0.0]
            },
            "JH011-SH": {
                "dates": ["08-01", "08-02", "08-03", "08-04", "08-05", "08-06", "08-07", "08-08", "08-09", "08-10", "08-11", "08-12", "08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19", "08-20", "08-21", "08-22", "08-23", "08-24", "08-25", "08-26", "08-27", "08-28", "08-29", "08-30", "08-31", "09-01", "09-02", "09-03", "09-04", "09-05", "09-06", "09-07", "09-08", "09-09", "09-10", "09-11", "09-12", "09-13", "09-14", "09-15", "09-16", "09-17", "09-18", "09-19", "09-20", "09-21", "09-22", "09-23", "09-24", "09-25", "09-26", "09-27", "09-28", "09-29", "09-30"],
                "plan": [0.0, 0.0, 0.0, 0.0, 0.525, 0.525, 0.525, 0.525, 0.0, 0.0, 0.525, 0.525, 0.525, 0.0, 0.0, 0.0, 0.0, 0.525, 0.525, 0.525, 0.525, 0.0, 0.0, 0.0, 0.525, 0.189583333333333, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
                "actual": [0.0, 0.0, 0.0, 0.0, 0.525, 0.525, 0.525, 0.525, 0.0, 0.0, 0.525, 0.525, 0.525, 0.0, 0.0, 0.0, 0.0, 0.525, 0.525, 0.525, 0.525, 0.0, 0.0, 0.0, 0.525, 0.189583333333333, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
                "empty_box": [0.0, 0.0, 0.0, 0.525, 0.525, 0.525, 0.525, 0.0, 0.0, 0.525, 0.525, 0.525, 0.0, 0.0, 0.0, 0.0, 0.525, 0.525, 0.525, 0.525, 0.0, 0.0, 0.0, 0.525, 0.189583333333333, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
            },
            "JH027-SB": {
                "dates": ["08-01", "08-02", "08-03", "08-04", "08-05", "08-06", "08-07", "08-08", "08-09", "08-10", "08-11", "08-12", "08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19", "08-20", "08-21", "08-22", "08-23", "08-24", "08-25", "08-26", "08-27", "08-28", "08-29", "08-30", "08-31", "09-01", "09-02", "09-03", "09-04", "09-05", "09-06", "09-07", "09-08", "09-09", "09-10", "09-11", "09-12", "09-13", "09-14", "09-15", "09-16", "09-17", "09-18", "09-19", "09-20", "09-21", "09-22", "09-23", "09-24", "09-25", "09-26", "09-27", "09-28", "09-29", "09-30"],
                "plan": [0.95, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.95, 0.95, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.0, 0.0],
                "actual": [0.95, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.65, 0.925, 0.0, 0.45, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.95, 0.95, 0.925, 0.0, 0.0, 0.425, 0.0, 0.0, 0.0, 1.85, 0.925, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.0, 0.0],
                "empty_box": [0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.95, 0.95, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
            },
            "JH027-SC": {
                "dates": ["08-01", "08-02", "08-03", "08-04", "08-05", "08-06", "08-07", "08-08", "08-09", "08-10", "08-11", "08-12", "08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19", "08-20", "08-21", "08-22", "08-23", "08-24", "08-25", "08-26", "08-27", "08-28", "08-29", "08-30", "08-31", "09-01", "09-02", "09-03", "09-04", "09-05", "09-06", "09-07", "09-08", "09-09", "09-10", "09-11", "09-12", "09-13", "09-14", "09-15", "09-16", "09-17", "09-18", "09-19", "09-20", "09-21", "09-22", "09-23", "09-24", "09-25", "09-26", "09-27", "09-28", "09-29", "09-30"],
                "plan": [0.0, 0.925, 1.85, 0.95, 0.425, 0.95, 0.95, 0.0, 0.0, 1.85, 1.9, 0.0, 1.9, 0.95, 0.0, 0.0, 0.0, 0.95, 0.0, 0.95, 0.95, 0.95, 0.0, 0.0, 0.95, 0.425, 0.95, 0.0, 0.0, 0.925, 0.925, 0.95, 0.0, 0.95, 0.95, 0.95, 0.925, 0.925, 1.9, 0.0, 1.9, 0.0, 0.0, 0.0, 1.85, 1.9, 0.0, 1.9, 0.95, 0.95, 0.0, 1.85, 1.9, 0.25, 1.9, 0.95, 1.9, 1.85, 0.0, 0.0, 0.0],
                "actual": [0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.95, 0.0, 0.0, 0.0, 0.0, 0.925, 0.925, 0.95, 0.0, 0.95, 0.95, 0.95, 0.0, 0.0, 1.875, 0.0, 1.875, 0.95, 0.95, 0.0, 0.925, 1.9, 0.0, 0.95, 0.95, 0.95, 0.0, 0.925, 0.95, 0.0, 0.95, 0.0, 0.95, 0.925, 0.95, 0.0, 0.0],
                "empty_box": [0.0, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.95, 0.0, 0.0, 0.0, 0.0, 0.925, 0.925, 0.95, 0.0, 0.95, 0.95, 0.95, 0.0, 0.0, 1.875, 0.0, 1.875, 0.95, 0.95, 0.0, 0.925, 1.9, 0.0, 0.95, 0.95, 0.95, 0.0, 0.925, 0.95, 0.0, 0.95, 0.0, 0.95, 0.925, 0.95, 0.0, 0.0, 0.0]
            },
            "JH027-SD": {
                "dates": ["08-01", "08-02", "08-03", "08-04", "08-05", "08-06", "08-07", "08-08", "08-09", "08-10", "08-11", "08-12", "08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19", "08-20", "08-21", "08-22", "08-23", "08-24", "08-25", "08-26", "08-27", "08-28", "08-29", "08-30", "08-31", "09-01", "09-02", "09-03", "09-04", "09-05", "09-06", "09-07", "09-08", "09-09", "09-10", "09-11", "09-12", "09-13", "09-14", "09-15", "09-16", "09-17", "09-18", "09-19", "09-20", "09-21", "09-22", "09-23", "09-24", "09-25", "09-26", "09-27", "09-28", "09-29", "09-30"],
                "plan": [0.95, 0.0, 0.0, 0.95, 0.425, 0.95, 0.95, 1.9, 0.925, 0.0, 0.0, 0.0, 0.0, 0.95, 1.85, 0.925, 0.925, 0.95, 0.0, 0.95, 0.95, 0.95, 0.925, 1.85, 0.95, 0.0, 0.95, 0.95, 0.0, 0.925, 0.925, 0.0, 0.0, 0.95, 0.95, 0.95, 0.925, 0.0, 0.0, 0.0, 0.0, 0.95, 0.95, 0.925, 0.0, 0.0, 0.0, 0.0, 0.95, 0.95, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.9, 0.85, 0.0],
                "actual": [0.975, 0.0, 0.0, 1.9, 0.85, 1.9, 1.9, 1.9, 0.0, 0.0, 1.9, 0.0, 1.9, 0.95, 0.925, 0.0, 0.0, 0.95, 0.0, 1.9, 1.9, 1.9, 0.0, 0.0, 0.95, 0.425, 1.9, 0.95, 0.0, 0.925, 0.925, 0.0, 0.425, 0.95, 0.95, 0.95, 1.85, 0.925, 0.0, 0.85, 0.0, 0.0, 0.0, 0.925, 0.925, 0.0, 0.425, 0.95, 0.95, 0.95, 0.0, 0.0, 0.95, 0.425, 0.95, 0.95, 0.95, 0.925, 0.95, 0.85, 0.0],
                "empty_box": [0.0, 0.0, 1.9, 0.85, 1.9, 1.9, 1.9, 0.0, 0.0, 1.9, 0.0, 1.9, 0.95, 0.925, 0.0, 0.0, 0.95, 0.0, 1.9, 1.9, 1.9, 0.0, 0.0, 0.95, 0.425, 1.9, 0.95, 0.0, 0.925, 0.925, 0.0, 0.425, 0.95, 0.95, 0.95, 0.0, 0.925, 0.0, 0.85, 0.0, 0.0, 0.0, 0.925, 0.925, 0.0, 0.425, 0.95, 0.95, 0.95, 0.0, 0.0, 0.95, 0.425, 0.95, 0.95, 0.95, 0.925, 0.95, 0.85, 0.0, 0.0]
            }
        };

        // 生产计划vs实际执行对比图
        const planVsActualCtx = document.getElementById('planVsActualChart').getContext('2d');
        new Chart(planVsActualCtx, {
            type: 'bar',
            data: {
                labels: analysisData.machines,
                datasets: [{
                    label: '生产计划',
                    data: analysisData.planData,
                    backgroundColor: 'rgba(52, 152, 219, 0.8)',
                    borderColor: 'rgba(52, 152, 219, 1)',
                    borderWidth: 2
                }, {
                    label: '生产实绩',
                    data: analysisData.actualData,
                    backgroundColor: 'rgba(231, 76, 60, 0.8)',
                    borderColor: 'rgba(231, 76, 60, 1)',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '8-9月生产计划vs实际执行对比',
                        font: { size: 16, weight: 'bold' }
                    },
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '数量（车辆）'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '机种'
                        }
                    }
                }
            }
        });

        // 空箱纳入分析图
        const emptyBoxCtx = document.getElementById('emptyBoxChart').getContext('2d');
        new Chart(emptyBoxCtx, {
            type: 'bar',
            data: {
                labels: analysisData.machines,
                datasets: [{
                    label: '空箱纳入',
                    data: analysisData.emptyBoxData,
                    backgroundColor: 'rgba(46, 204, 113, 0.8)',
                    borderColor: 'rgba(46, 204, 113, 1)',
                    borderWidth: 2
                }, {
                    label: '生产实绩',
                    data: analysisData.actualData,
                    backgroundColor: 'rgba(231, 76, 60, 0.8)',
                    borderColor: 'rgba(231, 76, 60, 1)',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '8-9月空箱纳入vs生产实绩对比',
                        font: { size: 16, weight: 'bold' }
                    },
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '数量（车辆）'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '机种'
                        }
                    }
                }
            }
        });

        // 空箱匹配度波动分析图
        const emptyBoxVariationCtx = document.getElementById('emptyBoxVariationChart').getContext('2d');
        new Chart(emptyBoxVariationCtx, {
            type: 'radar',
            data: {
                labels: analysisData.machines,
                datasets: [{
                    label: '空箱完美匹配率(%)',
                    data: analysisData.emptyBoxMatchRates,
                    backgroundColor: 'rgba(155, 89, 182, 0.2)',
                    borderColor: 'rgba(155, 89, 182, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(155, 89, 182, 1)',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: 'rgba(155, 89, 182, 1)'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '各机种空箱匹配度雷达图',
                        font: { size: 16, weight: 'bold' }
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            stepSize: 20
                        }
                    }
                }
            }
        });

        // 每日趋势综合图
        const dailyTrendCtx = document.getElementById('dailyTrendChart').getContext('2d');
        new Chart(dailyTrendCtx, {
            type: 'line',
            data: {
                labels: dailyTrendsData.JT028.dates,
                datasets: [
                    {
                        label: 'JT028-计划',
                        data: dailyTrendsData.JT028.plan,
                        borderColor: 'rgba(52, 152, 219, 1)',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        borderWidth: 2,
                        fill: false
                    },
                    {
                        label: 'JT028-实绩',
                        data: dailyTrendsData.JT028.actual,
                        borderColor: 'rgba(231, 76, 60, 1)',
                        backgroundColor: 'rgba(231, 76, 60, 0.1)',
                        borderWidth: 2,
                        fill: false
                    },
                    {
                        label: 'JT026-计划',
                        data: dailyTrendsData.JT026.plan,
                        borderColor: 'rgba(46, 204, 113, 1)',
                        backgroundColor: 'rgba(46, 204, 113, 0.1)',
                        borderWidth: 2,
                        fill: false
                    },
                    {
                        label: 'JT026-实绩',
                        data: dailyTrendsData.JT026.actual,
                        borderColor: 'rgba(155, 89, 182, 1)',
                        backgroundColor: 'rgba(155, 89, 182, 0.1)',
                        borderWidth: 2,
                        fill: false
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '主要机种每日执行趋势对比（8-9月）',
                        font: { size: 16, weight: 'bold' }
                    },
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '数量（车辆）'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '日期'
                        },
                        ticks: {
                            maxTicksLimit: 15
                        }
                    }
                }
            }
        });

        // 创建各机种单独趋势图的函数
        function createMachineTrendChart(canvasId, machineKey, machineName) {
            const ctx = document.getElementById(canvasId).getContext('2d');
            const machineData = dailyTrendsData[machineKey];

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: machineData.dates,
                    datasets: [
                        {
                            label: '生产计划',
                            data: machineData.plan,
                            borderColor: 'rgba(52, 152, 219, 1)',
                            backgroundColor: 'rgba(52, 152, 219, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.1
                        },
                        {
                            label: '生产实绩',
                            data: machineData.actual,
                            borderColor: 'rgba(231, 76, 60, 1)',
                            backgroundColor: 'rgba(231, 76, 60, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.1
                        },
                        {
                            label: '空箱纳入',
                            data: machineData.empty_box,
                            borderColor: 'rgba(46, 204, 113, 1)',
                            backgroundColor: 'rgba(46, 204, 113, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: machineName + ' 每日趋势',
                            font: { size: 14, weight: 'bold' }
                        },
                        legend: {
                            position: 'top',
                            labels: {
                                boxWidth: 12,
                                font: { size: 10 }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '数量',
                                font: { size: 10 }
                            },
                            ticks: {
                                font: { size: 9 }
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '日期',
                                font: { size: 10 }
                            },
                            ticks: {
                                maxTicksLimit: 8,
                                font: { size: 8 }
                            }
                        }
                    }
                }
            });
        }

        // 创建空箱纳入vs生产实际对比图的函数
        function createEmptyBoxComparisonChart(canvasId, machineKey, machineName) {
            const ctx = document.getElementById(canvasId).getContext('2d');
            const machineData = dailyTrendsData[machineKey];

            // 计算每日差异（空箱纳入 - 生产实际）
            const dailyDifference = machineData.empty_box.map((empty, index) => {
                return empty - machineData.actual[index];
            });

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: machineData.dates,
                    datasets: [
                        {
                            label: '生产实际',
                            data: machineData.actual,
                            borderColor: 'rgba(52, 152, 219, 1)',
                            backgroundColor: 'rgba(52, 152, 219, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.1,
                            yAxisID: 'y'
                        },
                        {
                            label: '空箱纳入',
                            data: machineData.empty_box,
                            borderColor: 'rgba(255, 193, 7, 1)',
                            backgroundColor: 'rgba(255, 193, 7, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.1,
                            yAxisID: 'y'
                        },
                        {
                            label: '每日差异',
                            data: dailyDifference,
                            borderColor: 'rgba(231, 76, 60, 1)',
                            backgroundColor: 'rgba(231, 76, 60, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.1,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: machineName + ' 空箱vs实际对比',
                            font: { size: 14, weight: 'bold' }
                        },
                        legend: {
                            position: 'top',
                            labels: {
                                boxWidth: 12,
                                font: { size: 10 }
                            }
                        }
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '数量（车辆）',
                                font: { size: 10 }
                            },
                            ticks: {
                                font: { size: 9 }
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: '差异',
                                font: { size: 10 }
                            },
                            ticks: {
                                font: { size: 9 }
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        },
                        x: {
                            title: {
                                display: true,
                                text: '日期',
                                font: { size: 10 }
                            },
                            ticks: {
                                maxTicksLimit: 8,
                                font: { size: 8 }
                            }
                        }
                    }
                }
            });
        }

        // 添加页面加载完成提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('8-9月统计表分析报告加载完成');

            // 创建各机种趋势图
            createMachineTrendChart('jt028TrendChart', 'JT028', 'JT028');
            createMachineTrendChart('jt026TrendChart', 'JT026', 'JT026');
            createMachineTrendChart('jh011shTrendChart', 'JH011-SH', 'JH011-SH');
            createMachineTrendChart('jh027sbTrendChart', 'JH027-SB', 'JH027-SB');
            createMachineTrendChart('jh027scTrendChart', 'JH027-SC', 'JH027-SC');
            createMachineTrendChart('jh027sdTrendChart', 'JH027-SD', 'JH027-SD');

            // 创建空箱对比图
            createEmptyBoxComparisonChart('jt028EmptyBoxTrendChart', 'JT028', 'JT028');
            createEmptyBoxComparisonChart('jt026EmptyBoxTrendChart', 'JT026', 'JT026');
            createEmptyBoxComparisonChart('jh011shEmptyBoxTrendChart', 'JH011-SH', 'JH011-SH');
            createEmptyBoxComparisonChart('jh027sbEmptyBoxTrendChart', 'JH027-SB', 'JH027-SB');
            createEmptyBoxComparisonChart('jh027scEmptyBoxTrendChart', 'JH027-SC', 'JH027-SC');
            createEmptyBoxComparisonChart('jh027sdEmptyBoxTrendChart', 'JH027-SD', 'JH027-SD');

            // 添加卡片悬停效果
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
  </script>
  <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js">
  </script>
  <script>
   // 日历热力图数据
        const calendarData = [['2025-08-01', 16], ['2025-08-02', 5], ['2025-08-03', 6], ['2025-08-04', 12], ['2025-08-05', 19], ['2025-08-06', 12], ['2025-08-07', 8], ['2025-08-08', 7], ['2025-08-09', 2], ['2025-08-10', 5], ['2025-08-11', 7], ['2025-08-12', 13], ['2025-08-13', 5], ['2025-08-14', 7], ['2025-08-15', 9], ['2025-08-16', 3], ['2025-08-17', 8], ['2025-08-18', 13], ['2025-08-19', 16], ['2025-08-20', 8], ['2025-08-21', 10], ['2025-08-22', 15], ['2025-08-23', 5], ['2025-08-24', 7], ['2025-08-25', 11], ['2025-08-26', 15], ['2025-08-27', 7], ['2025-08-28', 15], ['2025-08-29', 7], ['2025-08-30', 6], ['2025-08-31', 6], ['2025-09-01', 12], ['2025-09-02', 17], ['2025-09-03', 6], ['2025-09-04', 11], ['2025-09-05', 16], ['2025-09-06', 7], ['2025-09-07', 6], ['2025-09-08', 10], ['2025-09-09', 16], ['2025-09-10', 6], ['2025-09-11', 14], ['2025-09-12', 15], ['2025-09-13', 5], ['2025-09-14', 8], ['2025-09-15', 9], ['2025-09-16', 15], ['2025-09-17', 7], ['2025-09-18', 11], ['2025-09-19', 13], ['2025-09-20', 6], ['2025-09-21', 6], ['2025-09-22', 10], ['2025-09-23', 18], ['2025-09-24', 9], ['2025-09-25', 17], ['2025-09-26', 12], ['2025-09-27', 6], ['2025-09-28', 6], ['2025-09-29', 3], ['2025-09-30', 1]];

        // 各栋别热力图数据
        const calendarDataA = [['2025-08-01', 2], ['2025-08-02', 0], ['2025-08-03', 0], ['2025-08-04', 1], ['2025-08-05', 4], ['2025-08-06', 1], ['2025-08-07', 3], ['2025-08-08', 0], ['2025-08-09', 0], ['2025-08-10', 1], ['2025-08-11', 1], ['2025-08-12', 4], ['2025-08-13', 0], ['2025-08-14', 2], ['2025-08-15', 0], ['2025-08-16', 0], ['2025-08-17', 1], ['2025-08-18', 2], ['2025-08-19', 2], ['2025-08-20', 1], ['2025-08-21', 2], ['2025-08-22', 1], ['2025-08-23', 0], ['2025-08-24', 1], ['2025-08-25', 1], ['2025-08-26', 3], ['2025-08-27', 0], ['2025-08-28', 2], ['2025-08-29', 0], ['2025-08-30', 0], ['2025-08-31', 0], ['2025-09-01', 0], ['2025-09-02', 3], ['2025-09-03', 0], ['2025-09-04', 1], ['2025-09-05', 0], ['2025-09-06', 0], ['2025-09-07', 0], ['2025-09-08', 0], ['2025-09-09', 2], ['2025-09-10', 0], ['2025-09-11', 2], ['2025-09-12', 0], ['2025-09-13', 0], ['2025-09-14', 0], ['2025-09-15', 0], ['2025-09-16', 1], ['2025-09-17', 0], ['2025-09-18', 2], ['2025-09-19', 0], ['2025-09-20', 0], ['2025-09-21', 0], ['2025-09-22', 0], ['2025-09-23', 2], ['2025-09-24', 0], ['2025-09-25', 1], ['2025-09-26', 0], ['2025-09-27', 0], ['2025-09-28', 0], ['2025-09-29', 0], ['2025-09-30', 0]];
        const calendarDataB = [['2025-08-01', 12], ['2025-08-02', 3], ['2025-08-03', 4], ['2025-08-04', 7], ['2025-08-05', 7], ['2025-08-06', 6], ['2025-08-07', 3], ['2025-08-08', 7], ['2025-08-09', 2], ['2025-08-10', 2], ['2025-08-11', 4], ['2025-08-12', 4], ['2025-08-13', 4], ['2025-08-14', 5], ['2025-08-15', 8], ['2025-08-16', 1], ['2025-08-17', 5], ['2025-08-18', 6], ['2025-08-19', 7], ['2025-08-20', 2], ['2025-08-21', 7], ['2025-08-22', 10], ['2025-08-23', 1], ['2025-08-24', 4], ['2025-08-25', 6], ['2025-08-26', 5], ['2025-08-27', 5], ['2025-08-28', 12], ['2025-08-29', 4], ['2025-08-30', 2], ['2025-08-31', 4], ['2025-09-01', 8], ['2025-09-02', 6], ['2025-09-03', 4], ['2025-09-04', 8], ['2025-09-05', 12], ['2025-09-06', 2], ['2025-09-07', 4], ['2025-09-08', 6], ['2025-09-09', 7], ['2025-09-10', 4], ['2025-09-11', 10], ['2025-09-12', 11], ['2025-09-13', 2], ['2025-09-14', 5], ['2025-09-15', 6], ['2025-09-16', 6], ['2025-09-17', 6], ['2025-09-18', 7], ['2025-09-19', 10], ['2025-09-20', 2], ['2025-09-21', 4], ['2025-09-22', 6], ['2025-09-23', 5], ['2025-09-24', 7], ['2025-09-25', 12], ['2025-09-26', 9], ['2025-09-27', 2], ['2025-09-28', 4], ['2025-09-29', 3], ['2025-09-30', 1]];
        const calendarDataC = [['2025-08-01', 2], ['2025-08-02', 2], ['2025-08-03', 2], ['2025-08-04', 4], ['2025-08-05', 8], ['2025-08-06', 5], ['2025-08-07', 2], ['2025-08-08', 0], ['2025-08-09', 0], ['2025-08-10', 2], ['2025-08-11', 2], ['2025-08-12', 5], ['2025-08-13', 1], ['2025-08-14', 0], ['2025-08-15', 1], ['2025-08-16', 2], ['2025-08-17', 2], ['2025-08-18', 5], ['2025-08-19', 7], ['2025-08-20', 5], ['2025-08-21', 1], ['2025-08-22', 4], ['2025-08-23', 4], ['2025-08-24', 2], ['2025-08-25', 4], ['2025-08-26', 7], ['2025-08-27', 2], ['2025-08-28', 1], ['2025-08-29', 3], ['2025-08-30', 4], ['2025-08-31', 2], ['2025-09-01', 4], ['2025-09-02', 8], ['2025-09-03', 2], ['2025-09-04', 2], ['2025-09-05', 4], ['2025-09-06', 5], ['2025-09-07', 2], ['2025-09-08', 4], ['2025-09-09', 7], ['2025-09-10', 2], ['2025-09-11', 2], ['2025-09-12', 4], ['2025-09-13', 3], ['2025-09-14', 3], ['2025-09-15', 3], ['2025-09-16', 8], ['2025-09-17', 1], ['2025-09-18', 2], ['2025-09-19', 3], ['2025-09-20', 4], ['2025-09-21', 2], ['2025-09-22', 4], ['2025-09-23', 11], ['2025-09-24', 2], ['2025-09-25', 4], ['2025-09-26', 3], ['2025-09-27', 4], ['2025-09-28', 2], ['2025-09-29', 0], ['2025-09-30', 0]];

        // 栋别分布数据
        const buildingData = [{'name': 'A栋', 'value': 49}, {'name': 'B栋', 'value': 338}, {'name': 'C栋', 'value': 196}];

        // 超时栋别分布数据
        const overtimeBuildingData = [{'name': 'A栋', 'value': 0}, {'name': 'B栋', 'value': 60}, {'name': 'C栋', 'value': 56}];

        // 超时趋势数据
        const overtimeTrendData = [['2025-08-01', 6], ['2025-08-02', 0], ['2025-08-03', 0], ['2025-08-04', 1], ['2025-08-05', 6], ['2025-08-06', 3], ['2025-08-07', 0], ['2025-08-08', 3], ['2025-08-09', 0], ['2025-08-10', 0], ['2025-08-11', 0], ['2025-08-12', 1], ['2025-08-13', 0], ['2025-08-14', 1], ['2025-08-15', 1], ['2025-08-16', 0], ['2025-08-17', 1], ['2025-08-18', 2], ['2025-08-19', 4], ['2025-08-20', 2], ['2025-08-21', 0], ['2025-08-22', 5], ['2025-08-23', 1], ['2025-08-24', 0], ['2025-08-25', 1], ['2025-08-26', 4], ['2025-08-27', 0], ['2025-08-28', 6], ['2025-08-29', 0], ['2025-08-30', 1], ['2025-08-31', 0], ['2025-09-01', 2], ['2025-09-02', 6], ['2025-09-03', 0], ['2025-09-04', 1], ['2025-09-05', 7], ['2025-09-06', 2], ['2025-09-07', 0], ['2025-09-08', 1], ['2025-09-09', 5], ['2025-09-10', 0], ['2025-09-11', 3], ['2025-09-12', 6], ['2025-09-13', 0], ['2025-09-14', 0], ['2025-09-15', 0], ['2025-09-16', 6], ['2025-09-17', 1], ['2025-09-18', 2], ['2025-09-19', 6], ['2025-09-20', 1], ['2025-09-21', 0], ['2025-09-22', 1], ['2025-09-23', 7], ['2025-09-24', 1], ['2025-09-25', 6], ['2025-09-26', 2], ['2025-09-27', 1], ['2025-09-28', 0], ['2025-09-29', 0], ['2025-09-30', 0]];

        // 初始化日历热力图
        const calendarChart = echarts.init(document.getElementById('calendarHeatmap'));
        const calendarOption = {
            title: {
                text: '2025年8-9月作业日历热力图',
                left: 'center',
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'normal'
                }
            },
            tooltip: {
                position: 'top',
                formatter: function (params) {
                    const date = echarts.time.format(params.data[0], '{yyyy}-{MM}-{dd}', false);
                    return date + '<br/>作业数量: ' + params.data[1] + '个';
                }
            },
            visualMap: {
                min: 0,
                max: 19,
                calculable: true,
                orient: 'horizontal',
                left: 'center',
                bottom: '5%',
                inRange: {
                    color: ['#fff5f5', '#fed7d7', '#feb2b2', '#fc8181', '#f56565', '#e53e3e', '#c53030', '#9b2c2c']
                },
                text: ['高', '低'],
                textStyle: {
                    color: '#666'
                }
            },
            calendar: [
                {
                    top: '12%',
                    left: '1%',
                    right: '1%',
                    bottom: '20%',
                    cellSize: [20, 20],
                    range: ['2025-08-01', '2025-09-30'],
                    orient: 'horizontal',
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: '#ddd',
                            width: 1,
                            type: 'solid'
                        }
                    },
                    itemStyle: {
                        borderWidth: 1,
                        borderColor: '#ddd'
                    },
                    yearLabel: {
                        show: false
                    },
                    monthLabel: {
                        nameMap: 'cn',
                        fontSize: 14,
                        color: '#333',
                        margin: 15
                    },
                    dayLabel: {
                        nameMap: 'cn',
                        fontSize: 12,
                        color: '#666',
                        margin: 10
                    }
                }
            ],
            series: [
                {
                    type: 'heatmap',
                    coordinateSystem: 'calendar',
                    data: calendarData
                }
            ]
        };
        calendarChart.setOption(calendarOption);

        // 初始化各栋别热力图
        function createBuildingHeatmap(containerId, data, buildingName, maxValue) {
            const chart = echarts.init(document.getElementById(containerId));
            const option = {
                tooltip: {
                    position: 'top',
                    formatter: function (params) {
                        const date = echarts.time.format(params.data[0], '{yyyy}-{MM}-{dd}', false);
                        return date + '<br/>' + buildingName + '栋作业: ' + params.data[1] + '个';
                    }
                },
                visualMap: {
                    min: 0,
                    max: maxValue,
                    calculable: true,
                    orient: 'horizontal',
                    left: 'center',
                    bottom: '5%',
                    inRange: {
                        color: ['#fff5f5', '#fed7d7', '#feb2b2', '#fc8181', '#f56565', '#e53e3e']
                    },
                    show: false
                },
                calendar: [
                    {
                        top: '5%',
                        left: '3%',
                        right: '3%',
                        bottom: '15%',
                        cellSize: [12, 12],
                        range: ['2025-08-01', '2025-09-30'],
                        orient: 'horizontal',
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: '#ddd',
                                width: 1,
                                type: 'solid'
                            }
                        },
                        itemStyle: {
                            borderWidth: 1,
                            borderColor: '#ddd'
                        },
                        yearLabel: {
                            show: false
                        },
                        monthLabel: {
                            nameMap: 'cn',
                            fontSize: 10,
                            color: '#333',
                            margin: 8
                        },
                        dayLabel: {
                            nameMap: 'cn',
                            fontSize: 8,
                            color: '#666',
                            margin: 5
                        }
                    }
                ],
                series: [
                    {
                        type: 'heatmap',
                        coordinateSystem: 'calendar',
                        data: data
                    }
                ]
            };
            chart.setOption(option);
            return chart;
        }

        // 计算各栋别的最大值
        const maxA = Math.max(...calendarDataA.map(item => item[1]));
        const maxB = Math.max(...calendarDataB.map(item => item[1]));
        const maxC = Math.max(...calendarDataC.map(item => item[1]));

        // 创建各栋别热力图
        const chartA = createBuildingHeatmap('calendarHeatmapA', calendarDataA, 'A', maxA);
        const chartB = createBuildingHeatmap('calendarHeatmapB', calendarDataB, 'B', maxB);
        const chartC = createBuildingHeatmap('calendarHeatmapC', calendarDataC, 'C', maxC);

        // 初始化栋别分布图
        const buildingChart = echarts.init(document.getElementById('buildingDistribution'));
        const buildingOption = {
            title: {
                text: '各栋别作业分布',
                left: 'center',
                textStyle: {
                    fontSize: 14,
                    fontWeight: 'normal'
                }
            },
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'vertical',
                left: 'left'
            },
            series: [
                {
                    name: '作业分布',
                    type: 'pie',
                    radius: '50%',
                    data: buildingData,
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }
            ]
        };
        buildingChart.setOption(buildingOption);

        // 初始化超时栋别分布图
        const overtimeBuildingChart = echarts.init(document.getElementById('overtimeBuildingDistribution'));
        const overtimeBuildingOption = {
            title: {
                text: '各栋别超时作业分布',
                left: 'center',
                textStyle: {
                    fontSize: 14,
                    fontWeight: 'normal'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            xAxis: {
                type: 'category',
                data: overtimeBuildingData.map(item => item.name)
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '超时作业数',
                    type: 'bar',
                    data: overtimeBuildingData.map(item => item.value),
                    itemStyle: {
                        color: '#e53e3e'
                    }
                }
            ]
        };
        overtimeBuildingChart.setOption(overtimeBuildingOption);

        // 初始化超时趋势图
        const overtimeTrendChart = echarts.init(document.getElementById('overtimeTrend'));
        const overtimeTrendOption = {
            title: {
                text: '超时作业趋势',
                left: 'center',
                textStyle: {
                    fontSize: 14,
                    fontWeight: 'normal'
                }
            },
            tooltip: {
                trigger: 'axis'
            },
            xAxis: {
                type: 'time',
                boundaryGap: false
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '超时作业数',
                    type: 'line',
                    data: overtimeTrendData,
                    smooth: true,
                    lineStyle: {
                        color: '#e53e3e'
                    },
                    areaStyle: {
                        color: 'rgba(229, 62, 62, 0.1)'
                    }
                }
            ]
        };
        overtimeTrendChart.setOption(overtimeTrendOption);

        // 响应式调整
        window.addEventListener('resize', function() {
            calendarChart.resize();
            chartA.resize();
            chartB.resize();
            chartC.resize();
            buildingChart.resize();
            overtimeBuildingChart.resize();
            overtimeTrendChart.resize();
        });
  </script>
 </body>
</html>
