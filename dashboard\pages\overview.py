#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
概览页面
"""

import streamlit as st
import pandas as pd
import plotly.express as px
from datetime import datetime, timedelta
from components.charts import ChartGenerator
from components.metrics import MetricsCard
from config.themes import AppleTheme

def render_overview_page(data: pd.DataFrame, worktime_analyzer, balance_analyzer):
    """渲染概览页面"""
    
    # 页面标题
    st.markdown("""
        <div class="chart-container">
            <div class="chart-title">📊 数据概览仪表板</div>
        </div>
    """, unsafe_allow_html=True)
    
    if data is None or len(data) == 0:
        st.warning("⚠️ 暂无数据，请先上传数据文件")
        return
    
    # 计算关键指标
    total_records = len(data)
    total_work_hours = data['单任务工时'].sum() if '单任务工时' in data.columns else 0
    avg_work_hours = data['单任务工时'].mean() if '单任务工时' in data.columns else 0
    unique_buildings = data['栋别'].nunique() if '栋别' in data.columns else 0
    
    # 计算每日工作负荷
    daily_workload = worktime_analyzer.calculate_daily_workload(data)
    total_overtime = daily_workload['加班工时'].sum() if not daily_workload.empty else 0
    overtime_days = len(daily_workload[daily_workload['加班工时'] > 0]) if not daily_workload.empty else 0
    
    # 关键指标卡片
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.markdown(f"""
            <div class="metric-card">
                <div class="metric-value">{total_records:,}</div>
                <div class="metric-label">总作业记录</div>
                <div class="metric-delta positive">📈 数据完整</div>
            </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown(f"""
            <div class="metric-card">
                <div class="metric-value">{total_work_hours:.1f}</div>
                <div class="metric-label">总工时 (小时)</div>
                <div class="metric-delta {'positive' if avg_work_hours <= 8 else 'negative'}">
                    平均 {avg_work_hours:.1f}h/天
                </div>
            </div>
        """, unsafe_allow_html=True)
    
    with col3:
        st.markdown(f"""
            <div class="metric-card">
                <div class="metric-value">{total_overtime:.1f}</div>
                <div class="metric-label">总加班工时</div>
                <div class="metric-delta {'negative' if total_overtime > 0 else 'positive'}">
                    {overtime_days} 个加班日
                </div>
            </div>
        """, unsafe_allow_html=True)
    
    with col4:
        st.markdown(f"""
            <div class="metric-card">
                <div class="metric-value">{unique_buildings}</div>
                <div class="metric-label">活跃栋别数</div>
                <div class="metric-delta positive">🏢 全覆盖</div>
            </div>
        """, unsafe_allow_html=True)
    
    # 图表区域
    chart_generator = ChartGenerator()
    
    # 第一行图表
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown('<div class="chart-container">', unsafe_allow_html=True)
        if not daily_workload.empty:
            worktime_trend_fig = chart_generator.create_worktime_trend_chart(daily_workload)
            st.plotly_chart(worktime_trend_fig, use_container_width=True, config=chart_generator.config)
        else:
            st.info("暂无工时趋势数据")
        st.markdown('</div>', unsafe_allow_html=True)
    
    with col2:
        st.markdown('<div class="chart-container">', unsafe_allow_html=True)
        if not daily_workload.empty:
            building_pie_fig = chart_generator.create_building_workload_pie_chart(daily_workload)
            st.plotly_chart(building_pie_fig, use_container_width=True, config=chart_generator.config)
        else:
            st.info("暂无负荷分布数据")
        st.markdown('</div>', unsafe_allow_html=True)
    
    # 第二行图表
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown('<div class="chart-container">', unsafe_allow_html=True)
        if '作业类型' in data.columns:
            worktype_fig = chart_generator.create_worktype_distribution_chart(data)
            st.plotly_chart(worktype_fig, use_container_width=True, config=chart_generator.config)
        else:
            st.info("暂无作业类型数据")
        st.markdown('</div>', unsafe_allow_html=True)
    
    with col2:
        st.markdown('<div class="chart-container">', unsafe_allow_html=True)
        if not daily_workload.empty:
            overtime_fig = chart_generator.create_overtime_distribution_chart(daily_workload)
            st.plotly_chart(overtime_fig, use_container_width=True, config=chart_generator.config)
        else:
            st.info("暂无加班数据")
        st.markdown('</div>', unsafe_allow_html=True)
    
    # 热力图
    st.markdown('<div class="chart-container">', unsafe_allow_html=True)
    if not daily_workload.empty and len(daily_workload) > 1:
        heatmap_fig = chart_generator.create_heatmap_chart(daily_workload)
        st.plotly_chart(heatmap_fig, use_container_width=True, config=chart_generator.config)
    else:
        st.info("数据量不足，无法生成热力图")
    st.markdown('</div>', unsafe_allow_html=True)
    
    # 数据质量报告
    st.markdown("---")
    st.subheader("📋 数据质量报告")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        # 完整性检查
        missing_data = data.isnull().sum()
        completeness = 1 - (missing_data.sum() / (len(data) * len(data.columns)))
        
        st.markdown(f"""
            <div class="metric-card">
                <div class="metric-value">{completeness:.1%}</div>
                <div class="metric-label">数据完整性</div>
                <div class="metric-delta {'positive' if completeness > 0.9 else 'negative'}">
                    {'✅ 优秀' if completeness > 0.9 else '⚠️ 需改进'}
                </div>
            </div>
        """, unsafe_allow_html=True)
    
    with col2:
        # 一致性检查
        if '栋别' in data.columns:
            valid_buildings = data['栋别'].isin(['A', 'B', 'C']).mean()
        else:
            valid_buildings = 1.0
            
        st.markdown(f"""
            <div class="metric-card">
                <div class="metric-value">{valid_buildings:.1%}</div>
                <div class="metric-label">数据一致性</div>
                <div class="metric-delta {'positive' if valid_buildings > 0.95 else 'negative'}">
                    {'✅ 规范' if valid_buildings > 0.95 else '⚠️ 有异常'}
                </div>
            </div>
        """, unsafe_allow_html=True)
    
    with col3:
        # 时效性检查
        if '标准日期' in data.columns:
            latest_date = data['标准日期'].max()
            days_ago = (datetime.now().date() - latest_date.date()).days
            timeliness = max(0, 1 - days_ago / 30)  # 30天内为满分
        else:
            timeliness = 0
            days_ago = 999
            
        st.markdown(f"""
            <div class="metric-card">
                <div class="metric-value">{timeliness:.1%}</div>
                <div class="metric-label">数据时效性</div>
                <div class="metric-delta {'positive' if days_ago < 7 else 'negative'}">
                    {days_ago} 天前更新
                </div>
            </div>
        """, unsafe_allow_html=True)
    
    # 快速洞察
    st.markdown("---")
    st.subheader("💡 快速洞察")
    
    insights = []
    
    # 工时洞察
    if not daily_workload.empty:
        max_overtime_building = daily_workload.groupby('栋别')['加班工时'].sum().idxmax()
        max_overtime_hours = daily_workload.groupby('栋别')['加班工时'].sum().max()
        
        if max_overtime_hours > 0:
            insights.append(f"🔴 **{max_overtime_building}栋** 加班最多，累计 **{max_overtime_hours:.1f}小时**，建议优化作业安排")
    
    # 负荷均衡洞察
    if '栋别' in data.columns:
        building_counts = data['栋别'].value_counts()
        max_building = building_counts.idxmax()
        min_building = building_counts.idxmin()
        imbalance_ratio = building_counts.max() / building_counts.min()
        
        if imbalance_ratio > 2:
            insights.append(f"⚖️ **{max_building}栋** 作业量是 **{min_building}栋** 的 **{imbalance_ratio:.1f}倍**，存在负荷不均衡")
    
    # 效率洞察
    if '单任务工时' in data.columns:
        avg_task_time = data['单任务工时'].mean()
        if avg_task_time > 3:
            insights.append(f"⏱️ 平均单任务工时 **{avg_task_time:.1f}小时**，可能存在效率提升空间")
    
    # 时间分布洞察
    if '作业类型' in data.columns:
        top_worktype = data['作业类型'].value_counts().index[0]
        top_count = data['作业类型'].value_counts().iloc[0]
        total_count = len(data)
        
        if top_count / total_count > 0.4:
            insights.append(f"📊 **{top_worktype}** 占总作业的 **{top_count/total_count:.1%}**，建议关注资源配置")
    
    if insights:
        for insight in insights:
            st.markdown(f"- {insight}")
    else:
        st.info("暂无特殊洞察，数据分布相对均衡")
    
    # 建议操作
    st.markdown("---")
    st.subheader("🎯 建议操作")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("📊 查看详细工时分析", type="secondary"):
            st.info("请切换到 '⏰ 工时分析' 标签页")
    
    with col2:
        if st.button("⚖️ 查看负荷均衡分析", type="secondary"):
            st.info("请切换到 '⚖️ 负荷均衡' 标签页")
    
    with col3:
        if st.button("🚨 查看预警监控", type="secondary"):
            st.info("请切换到 '🚨 预警监控' 标签页")
