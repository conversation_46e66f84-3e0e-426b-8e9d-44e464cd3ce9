<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完成品计划作业平衡度分析报告</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.2/dist/echarts.min.js"></script>
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #4f46e5;
            --accent-color: #8b5cf6;
            --light-color: #f3f4f6;
            --dark-color: #1f2937;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --text-color: #374151;
            --border-radius: 0.5rem;
            --box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: #f9fafb;
            position: relative;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 3rem 0;
            border-radius: var(--border-radius);
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: var(--box-shadow);
            position: relative;
            overflow: hidden;
        }

        header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNTAiIGhlaWdodD0iMTUwIiB2aWV3Qm94PSIwIDAgMTUgMTUiPjxwYXRoIGQ9Ik0wLDBIMTVWMTVIMFYwWk0xLDFWMTRIMTRWMUgxWiIgb3BhY2l0eT0iMC4xIiBmaWxsPSIjZmZmIi8+PC9zdmc+');
            opacity: 0.1;
        }

        h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: white;
        }

        header p {
            font-size: 1.25rem;
            opacity: 0.9;
            margin-bottom: 0;
            color: white;
        }

        section {
            margin-bottom: 3rem;
        }

        h2 {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--dark-color);
            margin: 2rem 0 1.5rem;
            position: relative;
            padding-left: 1rem;
            border-left: 4px solid var(--primary-color);
        }

        h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--dark-color);
            margin: 1.5rem 0 1rem;
        }

        p {
            margin-bottom: 1rem;
            font-size: 1rem;
            color: var(--text-color);
            line-height: 1.7;
        }

        .card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 1.5rem;
            overflow: hidden;
            transition: var(--transition);
            border: none;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .card-header {
            padding: 1rem 1.5rem;
            background-color: var(--light-color);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            display: flex;
            align-items: center;
        }

        .card-header i {
            margin-right: 0.75rem;
            font-size: 1.25rem;
            color: var(--primary-color);
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
            color: var(--dark-color);
        }

        .card-body {
            padding: 1.5rem;
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin: 1.5rem 0;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .chart-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: inherit;
        }

        .chart-caption {
            color: #6b7280;
            font-size: 0.875rem;
            text-align: center;
            margin: 0.75rem 0 0;
            font-style: italic;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1.5rem 0;
        }

        .stat-card {
            background-color: white;
            padding: 1.5rem;
            border-radius: var(--border-radius);
            text-align: center;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: var(--transition);
            border-top: 3px solid var(--primary-color);
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            line-height: 1;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #6b7280;
            font-weight: 500;
        }

        .conclusion {
            background: linear-gradient(to right, #ffffff, #f3f4f6);
            padding: 2rem;
            border-radius: var(--border-radius);
            margin-top: 2rem;
            position: relative;
            overflow: hidden;
            box-shadow: var(--box-shadow);
        }

        .conclusion::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
        }

        .highlight {
            background-color: rgba(255, 251, 227, 0.6);
            padding: 0 0.25rem;
            border-radius: 3px;
            font-weight: 500;
            color: #92400e;
        }

        .improvement-item {
            margin-bottom: 1.5rem;
            padding-left: 1.5rem;
            position: relative;
        }

        .improvement-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: 0.6rem;
            width: 0.5rem;
            height: 0.5rem;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        }

        .improvement-item h4 {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--dark-color);
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
        }

        @media (max-width: 768px) {
            .two-column {
                grid-template-columns: 1fr;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            header p {
                font-size: 1rem;
            }
        }

        footer {
            text-align: center;
            padding: 2rem;
            margin-top: 3rem;
            color: #6b7280;
            font-size: 0.875rem;
            background-color: var(--light-color);
            border-radius: var(--border-radius);
        }

        ul, ol {
            margin-left: 1.5rem;
            margin-bottom: 1rem;
        }

        li {
            margin-bottom: 0.5rem;
            position: relative;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1.5rem;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        table, th, td {
            border: none;
        }

        th, td {
            padding: 1rem;
            text-align: left;
        }

        th {
            background-color: var(--primary-color);
            color: white;
            font-weight: 600;
        }

        tr:nth-child(even) {
            background-color: #f9fafb;
        }

        tr:hover {
            background-color: #f3f4f6;
        }

        .badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            font-weight: 600;
            border-radius: 9999px;
            color: white;
            background-color: var(--primary-color);
            margin-left: 0.5rem;
        }

        .scrolltop {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.25rem;
            cursor: pointer;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transform: translateY(20px);
            transition: var(--transition);
            z-index: 100;
        }

        .scrolltop.active {
            opacity: 1;
            transform: translateY(0);
        }

        .scrolltop:hover {
            background: var(--secondary-color);
        }

        /* 动画效果 */
        .animate {
            animation: fadeInUp 0.5s ease forwards;
            opacity: 0;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 10px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #c7d2fe;
            border-radius: 5px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <header class="animate">
            <h1>完成品计划作业平衡度分析报告</h1>
            <p>基于2025年7月作业登记表数据</p>
        </header>

        <div class="scrolltop" id="scrollTop">
            <i class="fas fa-arrow-up"></i>
        </div>

        <nav class="mb-4 animate">
            <div class="card">
                <div class="card-body">
                    <h3 class="mb-3">目录</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-2"><a href="#section-overview" class="text-decoration-none"><i class="fas fa-chart-pie me-2 text-primary"></i>1. 分析概述</a></li>
                                <li class="mb-2"><a href="#section-distribution" class="text-decoration-none"><i class="fas fa-chart-line me-2 text-primary"></i>2. 作业计划分布分析</a></li>
                                <li class="mb-2"><a href="#section-evaluation" class="text-decoration-none"><i class="fas fa-clipboard-check me-2 text-primary"></i>3. 作业计划合理性评估</a></li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-2"><a href="#section-plan-actual" class="text-decoration-none"><i class="fas fa-clock me-2 text-primary"></i>4. 7月作业计划与实际对比分析</a></li>
                                <li class="mb-2"><a href="#section-improvement" class="text-decoration-none"><i class="fas fa-lightbulb me-2 text-primary"></i>5. 改善方案</a></li>
                                <li class="mb-2"><a href="#section-conclusion" class="text-decoration-none"><i class="fas fa-flag-checkered me-2 text-primary"></i>6. 总结与建议</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <section id="section-overview" class="animate">
            <h2><i class="fas fa-chart-pie me-2"></i>1. 分析概述</h2>
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-database"></i>
                    <h3 class="card-title">数据基本情况</h3>
                </div>
                <div class="card-body">
                    <p>本分析基于"作业登记表ABC栋+捷通202507"数据，涵盖2025年7月1日至7月31日期间的完成品计划作业安排。数据包含333条作业记录，涉及A、B、C三个栋别的各类作业安排，包括出库、入库、备货和装车等作业类型。</p>
                    
                    <div class="stats-grid">
                        <div class="stat-card">
                            <i class="fas fa-tasks mb-3 text-primary" style="font-size: 1.5rem;"></i>
                            <div class="stat-number">333</div>
                            <div class="stat-label">总作业数量</div>
                        </div>
                        <div class="stat-card">
                            <i class="fas fa-calendar-alt mb-3 text-primary" style="font-size: 1.5rem;"></i>
                            <div class="stat-number">29</div>
                            <div class="stat-label">记录天数</div>
                        </div>
                        <div class="stat-card">
                            <i class="fas fa-calculator mb-3 text-primary" style="font-size: 1.5rem;"></i>
                            <div class="stat-number">11.48</div>
                            <div class="stat-label">日均作业数量</div>
                        </div>
                        <div class="stat-card">
                            <i class="fas fa-chart-bar mb-3 text-primary" style="font-size: 1.5rem;"></i>
                            <div class="stat-number">3.44</div>
                            <div class="stat-label">日均作业标准差</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="section-distribution" class="animate">
            <h2><i class="fas fa-chart-line me-2"></i>2. 作业计划分布分析</h2>
            
            <div class="two-column">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-calendar-day"></i>
                        <h3 class="card-title">日期-作业数量分布</h3>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <div id="dateWorkDistribution" style="width: 100%; height: 100%;"></div>
                            <p class="chart-caption">分析时段内各日期作业数量分布情况</p>
                        </div>
                        <p><i class="fas fa-exclamation-circle text-warning me-2"></i>从图表可以看出，7月份作业数量在不同日期间波动较大，最少的日期(07/13、07/27)仅有<span class="highlight">3个作业</span>，而最多的日期(07/14、07/16)有<span class="highlight">16个作业</span>，偏差率高达<span class="highlight">433%</span>。平均每日作业数量为11.48个，但分布不均匀，不符合正态分布特征。</p>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-building"></i>
                        <h3 class="card-title">栋别作业量分布</h3>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <div id="buildingWorkBarChart" style="width: 100%; height: 100%;"></div>
                            <p class="chart-caption">A、B、C三栋作业量分布情况（横向柱状图）</p>
                        </div>
                        <p><i class="fas fa-exclamation-circle text-warning me-2"></i>B栋的作业明显多于A栋和C栋，总计<span class="highlight">186个作业占比56%</span>，而C栋84个作业占比25%，A栋63个作业占比19%。这表明资源分配严重不均衡，B栋负担过重，而A栋和C栋资源可能未被充分利用。</p>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <i class="fas fa-calendar-alt"></i>
                    <h3 class="card-title">作业日历热力图</h3>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="height: 500px;">
                        <div id="calendarHeatmap" style="width: 100%; height: 100%;"></div>
                        <p class="chart-caption">2025年7月作业数量日历热力图，红色越深表示作业强度越高</p>
                    </div>
                    <p><i class="fas fa-info-circle text-primary me-2"></i>日历热力图直观显示了7月期间每日的作业强度分布。可以清晰看到：<span class="highlight">7月14日和7月16日作业最繁忙(16个)</span>，<span class="highlight">7月13日和7月27日作业最少(3个)</span>。工作日作业相对集中，周末和节假日作业较少。</p>
                </div>
            </div>

            <div class="two-column">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-boxes"></i>
                        <h3 class="card-title">作业内容分布</h3>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <div id="workContentDistribution" style="width: 100%; height: 100%;"></div>
                            <p class="chart-caption">各类作业内容的数量分布</p>
                        </div>
                        <p><i class="fas fa-info-circle text-primary me-2"></i>从作业内容来看，"JT026-24周转箱"和"JT026-24电池"(<span class="highlight">各49次</span>)的频次最高，"60"(<span class="highlight">35次</span>)和"JT028完成品"(<span class="highlight">31次</span>)也较多。作业内容分布不均衡，集中在少数几种类型上，可能导致这些类型的资源(人力、设备)过度使用而其他资源闲置。</p>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-tags"></i>
                        <h3 class="card-title">作业类型分布</h3>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <div id="workTypePolarChart" style="width: 100%; height: 100%;"></div>
                            <p class="chart-caption">各类作业类型的数量分布（极坐标图）</p>
                        </div>
                        <p><i class="fas fa-info-circle text-primary me-2"></i>作业类型主要包括入库(<span class="highlight">132次</span>)和出库(<span class="highlight">127次</span>)，占总数的78%，备货(21次)、C栋仓库(18次)和常熟(13次)相对较少。入库作业略多于出库作业，但两者并未在各日期内保持平衡，部分日期出库多于入库，部分日期则相反，未体现良好的物流平衡性。</p>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <i class="fas fa-clock"></i>
                    <h3 class="card-title">作业时长分布</h3>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <div id="workDurationDistribution" style="width: 100%; height: 100%;"></div>
                        <p class="chart-caption">不同时长作业的数量分布</p>
                    </div>
                    <p><i class="fas fa-clock text-info me-2"></i>作业时长从0.5小时到3.5小时不等，其中<span class="highlight">2小时的作业最多，有170次，占比41%</span>。3小时作业有95次，占比23%。作业时长分布不均衡，过多集中在2小时，可能导致资源调度的不灵活。此外，较长时间(3小时以上)的作业往往在相邻时段安排，增加了连续工作的压力。</p>
                </div>
            </div>
        </section>

        <section id="section-evaluation" class="animate">
            <h2><i class="fas fa-clipboard-check me-2"></i>3. 作业计划合理性评估</h2>
            
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-chart-area"></i>
                    <h3 class="card-title">正态分布与偏差分析</h3>
                </div>
                <div class="card-body">
                    <p><i class="fas fa-balance-scale text-primary me-2"></i>理想的作业计划应当呈现相对均衡的分布，各日期作业数量波动不宜过大，各栋别负荷应当相对平衡。通过分析发现，当前作业计划存在显著偏离正态分布的情况：</p>
                    
                    <div class="alert alert-warning mt-3">
                        <ul class="mb-0">
                            <li><strong>日均作业数量偏差：</strong> 标准差为5.14，最大偏差率130%，表明日间作业分布极不均衡</li>
                            <li><strong>栋别负荷差异：</strong> B栋与C栋作业量差异达238个，B栋作业量是C栋的3.8倍，资源分配严重失衡</li>
                            <li><strong>日内时间分布：</strong> 作业集中在特定时段(10:30-11:30和14:30-16:30)，其他时段利用率低</li>
                            <li><strong>作业类型集中：</strong> JT026-24相关(137次)和JH027相关(141次)作业过于集中，占总量的67%，可能导致资源瓶颈</li>
                        </ul>
                    </div>
                    
                    <p class="mt-3"><i class="fas fa-exclamation-triangle text-danger me-2"></i>这些偏差表明当前作业计划的平衡度不足，可能导致资源利用效率低下，增加作业风险。</p>
                </div>
            </div>

        </section>

        <section id="section-plan-actual" class="animate">
            <h2><i class="fas fa-clock me-2"></i>4. 7月作业计划与实际对比分析</h2>

            <div class="card">
                <div class="card-header">
                    <i class="fas fa-chart-bar"></i>
                    <h3 class="card-title">计划执行情况概览</h3>
                </div>
                <div class="card-body">
                    <p><i class="fas fa-info-circle text-primary me-2"></i>基于7月份作业登记表数据，对比分析计划与实际执行情况，识别异常原因和加班原因，为优化作业计划提供数据支撑。</p>

                    <div class="stats-grid">
                        <div class="stat-card">
                            <i class="fas fa-tasks mb-3 text-primary" style="font-size: 1.5rem;"></i>
                            <div class="stat-number">337</div>
                            <div class="stat-label">7月总作业数</div>
                        </div>
                        <div class="stat-card">
                            <i class="fas fa-exclamation-triangle mb-3 text-warning" style="font-size: 1.5rem;"></i>
                            <div class="stat-number">41</div>
                            <div class="stat-label">异常作业数</div>
                        </div>
                        <div class="stat-card">
                            <i class="fas fa-clock mb-3 text-danger" style="font-size: 1.5rem;"></i>
                            <div class="stat-number">12</div>
                            <div class="stat-label">加班作业数</div>
                        </div>
                        <div class="stat-card">
                            <i class="fas fa-percentage mb-3 text-info" style="font-size: 1.5rem;"></i>
                            <div class="stat-number">12.2%</div>
                            <div class="stat-label">异常率</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="two-column">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-exclamation-circle"></i>
                        <h3 class="card-title">异常原因分析</h3>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <div id="abnormalReasonsChart" style="width: 100%; height: 100%;"></div>
                            <p class="chart-caption">实际作业时间晚于计划时间的异常原因分布</p>
                        </div>
                        <p><i class="fas fa-exclamation-triangle text-warning me-2"></i>主要异常原因包括：<span class="highlight">车辆延迟进厂</span>、<span class="highlight">自动仓库下货慢</span>、<span class="highlight">作业顺序调整</span>等。其中车辆相关问题占比43.9%，需要加强与运输方的协调。</p>

                        <div class="alert alert-warning mt-3">
                            <h6><i class="fas fa-list me-2"></i>主要异常原因：</h6>
                            <ul class="mb-0">
                                <li><strong>车辆相关异常：</strong> 18次 (43.9%) - 车辆迟到、提前到达、进厂时间不当等</li>
                                <li><strong>设备相关异常：</strong> 4次 (9.8%) - 自动仓库下货慢等设备问题</li>
                                <li><strong>流程相关异常：</strong> 5次 (12.2%) - 等待、库存不足、拣货单延迟等</li>
                                <li><strong>其他因素：</strong> 14次 (34.1%) - 作业顺序调整、天气影响、封车问题等</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-clock"></i>
                        <h3 class="card-title">加班原因分析</h3>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <div id="overtimeReasonsChart" style="width: 100%; height: 100%;"></div>
                            <p class="chart-caption">导致加班的主要原因分布</p>
                        </div>
                        <p><i class="fas fa-clock text-danger me-2"></i>主要加班原因为：<span class="highlight">超过当天最大作业量</span>、<span class="highlight">提前入周转箱</span>等。作业量超负荷是最主要的加班原因，占91.7%，需要优化作业安排。</p>

                        <div class="alert alert-danger mt-3">
                            <h6><i class="fas fa-list me-2"></i>主要加班原因：</h6>
                            <ul class="mb-0">
                                <li><strong>超过当天最大作业量：</strong> 11次 (91.7%) - 作业量超出正常处理能力</li>
                                <li><strong>提前入周转箱：</strong> 1次 (8.3%) - 为第二天停机点检做准备</li>
                                <li><strong>栋别分布：</strong> B栋8次，C栋4次 - B栋加班压力较大</li>
                                <li><strong>加班率：</strong> 总体3.6%，B栋4.3%，C栋4.5%</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <i class="fas fa-building"></i>
                    <h3 class="card-title">各栋别异常和加班情况对比</h3>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <div id="buildingComparisonChart" style="width: 100%; height: 100%;"></div>
                        <p class="chart-caption">A、B、C三栋的异常率和加班率对比</p>
                    </div>

                    <div class="alert alert-info mt-3">
                        <h5><i class="fas fa-info-circle me-2"></i>关键发现：</h5>
                        <ul class="mb-0">
                            <li><strong>A栋异常率最高：</strong> 达到52.5%，主要原因是车辆进厂时间和作业顺序调整问题</li>
                            <li><strong>B栋作业量最大：</strong> 186个作业，异常率4.8%，加班率4.3%</li>
                            <li><strong>C栋表现最佳：</strong> 异常率仅1.1%，加班率4.5%，作业执行相对稳定</li>
                        </ul>
                    </div>

                    <div class="table-responsive mt-3">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>栋别</th>
                                    <th>总作业数</th>
                                    <th>异常作业数</th>
                                    <th>加班作业数</th>
                                    <th>异常率</th>
                                    <th>加班率</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>A栋</strong></td>
                                    <td>59</td>
                                    <td>31</td>
                                    <td>0</td>
                                    <td class="text-danger">52.5%</td>
                                    <td class="text-success">0.0%</td>
                                </tr>
                                <tr>
                                    <td><strong>B栋</strong></td>
                                    <td>186</td>
                                    <td>9</td>
                                    <td>8</td>
                                    <td class="text-warning">4.8%</td>
                                    <td class="text-info">4.3%</td>
                                </tr>
                                <tr>
                                    <td><strong>C栋</strong></td>
                                    <td>88</td>
                                    <td>1</td>
                                    <td>4</td>
                                    <td class="text-success">1.1%</td>
                                    <td class="text-info">4.5%</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <i class="fas fa-chart-line"></i>
                    <h3 class="card-title">改进建议与措施</h3>
                </div>
                <div class="card-body">
                    <div class="improvement-item">
                        <h4><i class="fas fa-truck text-success me-2"></i>1. 车辆管理优化</h4>
                        <p>建立车辆进厂时间管控机制，与运输方签订准时到达协议，设置车辆进厂缓冲时间，减少因车辆延迟导致的异常。建议在计划时间基础上预留<span class="highlight">15-30分钟</span>的缓冲时间。</p>
                    </div>

                    <div class="improvement-item">
                        <h4><i class="fas fa-cogs text-success me-2"></i>2. 设备维护计划</h4>
                        <p>制定预防性维护计划，避免设备在作业高峰期故障。特别关注5#堆垛机和自动仓库的维护保养，建议在非作业时间进行设备检查和清扫。</p>
                    </div>

                    <div class="improvement-item">
                        <h4><i class="fas fa-balance-scale text-success me-2"></i>3. 作业负荷平衡</h4>
                        <p>控制单日出库作业车次，避免超过3车的高负荷作业。合理分配B栋作业量，将部分出库作业转移至A栋或C栋，减轻B栋加班压力。</p>
                    </div>

                    <div class="improvement-item">
                        <h4><i class="fas fa-calendar-check text-success me-2"></i>4. 计划灵活性提升</h4>
                        <p>为临时增加的作业预留时间缓冲，建立应急作业处理机制，减少对正常作业计划的冲击。建议设置<span class="highlight">10%的计划缓冲时间</span>。</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="section-improvement" class="animate">
            <h2><i class="fas fa-lightbulb me-2"></i>5. 改善方案</h2>
            
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-tools"></i>
                    <h3 class="card-title">改善建议</h3>
                </div>
                <div class="card-body">
                    <div class="improvement-item">
                        <h4><i class="fas fa-balance-scale-right text-success me-2"></i>1. 作业数量均衡化</h4>
                        <p>将作业数量较多日期(如7/14、7/16等)的部分非紧急作业移至作业较少的日期(如7/13、7/27等)，目标是使每日作业数量控制在<span class="highlight">10-13个之间</span>，减少极值偏差。具体可将7/14、7/16的部分作业(如入库作业)分散至7/13或7/27。</p>
                    </div>
                    
                    <div class="improvement-item">
                        <h4><i class="fas fa-clock text-success me-2"></i>2. 作业时间优化</h4>
                        <p>避免在同一时段安排多个资源密集型作业，合理利用中午时段，可安排一些短时间或低强度作业。减少晚间作业，将其移至次日早晨或前一日的低峰时段。具体可将7月高峰期的晚间作业调整至次日早晨。</p>
                    </div>
                    
                    <div class="improvement-item">
                        <h4><i class="fas fa-building text-success me-2"></i>3. 栋别负荷平衡</h4>
                        <p>合理分配各栋作业，减轻B栋负担，增加A栋和C栋的作业量，充分利用这些区域的资源。具体可将部分B栋的入库/出库作业转移至A栋或C栋。</p>
                    </div>
                    
                    <div class="improvement-item">
                        <h4><i class="fas fa-random text-success me-2"></i>4. 作业类型分散化</h4>
                        <p>避免同类型作业过于集中在特定日期，将大型出库作业(如日本出库)更均匀地分布，调整部分大型出库作业的日期，使其分布更加均匀。</p>
                    </div>
                    
                    <div class="improvement-item">
                        <h4><i class="fas fa-exclamation-triangle text-success me-2"></i>5. 紧急性作业处理</h4>
                        <p>为紧急作业预留缓冲时间，备货作业与出库作业间留有充足准备时间，在备货和出库之间增加至少<span class="highlight">0.5-1小时</span>的缓冲时间。</p>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <i class="fas fa-chart-line"></i>
                    <h3 class="card-title">改进效果预期</h3>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <div id="improvementRadarChart" style="width: 100%; height: 100%;"></div>
                        <p class="chart-caption">改进前后各指标对比</p>
                    </div>
                    
                    <p><i class="fas fa-arrow-circle-up text-success me-2"></i>通过实施上述改进措施，预期可实现以下效果：</p>
                    
                    <div class="table-responsive mt-3">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>改进指标</th>
                                    <th>当前状态</th>
                                    <th>目标状态</th>
                                    <th>提升空间</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>日均作业平衡性</strong></td>
                                    <td>标准差3.44，偏差率30%</td>
                                    <td>标准差1.5以内，偏差率20%以下</td>
                                    <td class="text-success">↑ 29%</td>
                                </tr>
                                <tr>
                                    <td><strong>栋别负荷均衡</strong></td>
                                    <td>最大差异123个，比例3.0:1</td>
                                    <td>差异20%以内，比例≤2:1</td>
                                    <td class="text-success">↑ 50%+</td>
                                </tr>
                                <tr>
                                    <td><strong>时间利用率</strong></td>
                                    <td>60%</td>
                                    <td>85%</td>
                                    <td class="text-success">↑ 25%</td>
                                </tr>
                                <tr>
                                    <td><strong>资源利用均衡性</strong></td>
                                    <td>50%</td>
                                    <td>80%</td>
                                    <td class="text-success">↑ 30%</td>
                                </tr>
                                <tr>
                                    <td><strong>计划可靠性</strong></td>
                                    <td>65%</td>
                                    <td>90%</td>
                                    <td class="text-success">↑ 25%</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </section>

        <section id="section-conclusion" class="conclusion animate">
            <h2><i class="fas fa-flag-checkered me-2"></i>6. 总结与建议</h2>
            <p><i class="fas fa-info-circle text-primary me-2"></i>本次分析表明，当前的完成品计划作业安排存在较大的不平衡性，主要表现为日间作业数量波动大、栋别负荷严重不均、作业时间分布不合理等问题。这些问题可能导致资源利用效率低下、作业压力不均衡、计划实施风险增加等负面影响。</p>
            
            <p class="mt-3"><i class="fas fa-lightbulb text-warning me-2"></i>我们建议通过作业数量均衡化、时间优化、栋别负荷平衡、作业类型分散化和紧急作业合理安排等措施，提高作业计划的平衡度和合理性。这将有助于：</p>
            
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-body">
                            <h4 class="card-title"><i class="fas fa-check-circle text-success me-2"></i>预期收益</h4>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item"><i class="fas fa-arrow-up text-success me-2"></i>提高资源利用效率，减少资源浪费</li>
                                <li class="list-group-item"><i class="fas fa-arrow-up text-success me-2"></i>降低高峰期工作压力，减少员工疲劳风险</li>
                                <li class="list-group-item"><i class="fas fa-arrow-up text-success me-2"></i>提高计划执行的可靠性和稳定性</li>
                                <li class="list-group-item"><i class="fas fa-arrow-up text-success me-2"></i>降低运营成本，提高整体效益</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-body">
                            <h4 class="card-title"><i class="fas fa-tasks text-primary me-2"></i>实施步骤</h4>
                            <ol class="list-group list-group-flush list-group-numbered">
                                <li class="list-group-item">制定新的作业平衡度标准和指标</li>
                                <li class="list-group-item">重新安排高峰期作业，分散至低谷期</li>
                                <li class="list-group-item">优化栋别作业分配，均衡各栋负荷</li>
                                <li class="list-group-item">建立作业计划评估和反馈机制</li>
                                <li class="list-group-item">定期监控和调整，维持长期平衡</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            
            <p class="mt-4"><i class="fas fa-chart-line text-success me-2"></i>通过建立更加科学、均衡的作业计划，可以在保证作业高效完成的同时，降低资源消耗，创造更加可持续的运营模式。</p>
        </section>

        <footer class="animate">
            <div class="row">
                <div class="col-md-6 text-center text-md-start">
                    <p><i class="far fa-calendar-alt me-2"></i>完成品计划作业平衡度分析报告 | 生成日期: 2025年7月10日</p>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <p><i class="fas fa-code me-2"></i>Powered by PLCN团队</p>
                </div>
            </div>
        </footer>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 滚动顶部按钮
        const scrollTopBtn = document.getElementById('scrollTop');
        
        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                scrollTopBtn.classList.add('active');
            } else {
                scrollTopBtn.classList.remove('active');
            }
        });
        
        scrollTopBtn.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
        
        // 添加动画效果
        const animateElements = document.querySelectorAll('.animate');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animationDelay = '0.1s';
                    entry.target.style.animationDuration = '0.5s';
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, { threshold: 0.1 });
        
        animateElements.forEach(element => {
            observer.observe(element);
        });
        
        // 日期-作业数量分布图表
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化ECharts实例
            var chartDom = document.getElementById('dateWorkDistribution');
            var myChart = echarts.init(chartDom);
            var option;
            
            // 数据定义 - 7月份数据
            const dates = ["07/01", "07/02", "07/03", "07/04", "07/05", "07/07", "07/08", "07/09", "07/10", "07/11", "07/12", "07/13", "07/14", "07/15", "07/16", "07/17", "07/18", "07/19", "07/21", "07/22", "07/23", "07/24", "07/25", "07/26", "07/27", "07/28", "07/29", "07/30", "07/31"];
            const workCounts = [14, 12, 15, 6, 10, 15, 14, 11, 15, 10, 9, 3, 16, 15, 16, 14, 12, 9, 12, 11, 13, 15, 13, 10, 3, 11, 10, 9, 10];
            const avgValue = 11.48; // 平均值
            
            // 柱状图配置
            const barOption = {
                title: {
                    text: '各日期作业数量分布',
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 'normal'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function(params) {
                        return params[0].name + ': ' + params[0].value + '个作业';
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '8%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: dates,
                    axisLabel: {
                        interval: 0,
                        rotate: 45
                    },
                    axisTick: {
                        alignWithLabel: true
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '作业数量',
                    min: 0,
                    max: 20,
                    interval: 2
                },
                series: [
                    {
                        name: '作业数量',
                        type: 'bar',
                        barWidth: '60%',
                        data: workCounts,
                        itemStyle: {
                            color: function(params) {
                                // 高于平均值显示蓝色，低于平均值显示橙色
                                return params.value > avgValue ? '#4f46e5' : '#f59e0b';
                            }
                        },
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        },
                        markLine: {
                            data: [
                                {
                                    type: 'average',
                                    name: '平均值',
                                    lineStyle: {
                                        color: '#10b981',
                                        type: 'dashed',
                                        width: 2
                                    },
                                    label: {
                                        formatter: '平均值: {c}',
                                        position: 'end'
                                    }
                                }
                            ]
                        },
                        animationDelay: function (idx) {
                            return idx * 50;
                        }
                    }
                ],
                animationEasing: 'elasticOut',
                animationDelayUpdate: function (idx) {
                    return idx * 5;
                }
            };
            
            // 折线图配置
            const lineOption = {
                title: {
                    text: '作业数量波动趋势',
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 'normal'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        return params[0].name + ': ' + params[0].value + '个作业';
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '8%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: dates,
                    axisLabel: {
                        interval: 0,
                        rotate: 45
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '作业数量',
                    min: 0,
                    max: 20,
                    interval: 2
                },
                series: [
                    {
                        name: '作业数量',
                        type: 'line',
                        data: workCounts,
                        smooth: true,
                        symbolSize: 8,
                        lineStyle: {
                            width: 3,
                            color: '#2563eb'
                        },
                        itemStyle: {
                            color: function(params) {
                                // 高于平均值显示蓝色，低于平均值显示橙色
                                return params.value > avgValue ? '#4f46e5' : '#f59e0b';
                            }
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {
                                    offset: 0,
                                    color: 'rgba(37, 99, 235, 0.6)'
                                },
                                {
                                    offset: 1,
                                    color: 'rgba(37, 99, 235, 0.1)'
                                }
                            ])
                        },
                        markLine: {
                            data: [
                                {
                                    type: 'average',
                                    name: '平均值',
                                    lineStyle: {
                                        color: '#10b981',
                                        type: 'dashed',
                                        width: 2
                                    },
                                    label: {
                                        formatter: '平均值: {c}',
                                        position: 'end'
                                    }
                                }
                            ]
                        },
                        animationDelay: function (idx) {
                            return idx * 50;
                        }
                    }
                ],
                animationEasing: 'elasticOut',
                animationDelayUpdate: function (idx) {
                    return idx * 5;
                }
            };
            
            // 设置初始图表
            myChart.setOption(barOption);
            
            // 每3秒切换图表类型
            let currentOption = barOption;
            setInterval(function () {
                currentOption = currentOption === barOption ? lineOption : barOption;
                myChart.setOption(currentOption, true);
            }, 3000);
            
            // 响应窗口大小变化
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        });

        // 日历热力图
        document.addEventListener('DOMContentLoaded', function() {
            var calendarChart = document.getElementById('calendarHeatmap');
            var myCalendarChart = echarts.init(calendarChart);

            // 真实的作业数据 - 7月份
            const calendarData = [
                ["2025-07-01", 14], ["2025-07-02", 12], ["2025-07-03", 15], ["2025-07-04", 6],
                ["2025-07-05", 10], ["2025-07-07", 15], ["2025-07-08", 14], ["2025-07-09", 11],
                ["2025-07-10", 15], ["2025-07-11", 10], ["2025-07-12", 9], ["2025-07-13", 3],
                ["2025-07-14", 16], ["2025-07-15", 15], ["2025-07-16", 16], ["2025-07-17", 14],
                ["2025-07-18", 12], ["2025-07-19", 9], ["2025-07-21", 12], ["2025-07-22", 11],
                ["2025-07-23", 13], ["2025-07-24", 15], ["2025-07-25", 13], ["2025-07-26", 10],
                ["2025-07-27", 3], ["2025-07-28", 11], ["2025-07-29", 10], ["2025-07-30", 9],
                ["2025-07-31", 10]
            ];

            const calendarOption = {
                title: {
                    text: '2025年7月作业日历热力图',
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 'normal'
                    }
                },
                tooltip: {
                    position: 'top',
                    formatter: function (params) {
                        const date = echarts.time.format(params.data[0], '{yyyy}-{MM}-{dd}', false);
                        return date + '<br/>作业数量: ' + params.data[1] + '个';
                    }
                },
                visualMap: {
                    min: 0,
                    max: 16,
                    calculable: true,
                    orient: 'horizontal',
                    left: 'center',
                    bottom: '5%',
                    inRange: {
                        color: ['#fff5f5', '#fed7d7', '#feb2b2', '#fc8181', '#f56565', '#e53e3e', '#c53030', '#9b2c2c']
                    },
                    text: ['高', '低'],
                    textStyle: {
                        color: '#666'
                    }
                },
                calendar: [
                    {
                        top: '12%',
                        left: '1%',
                        right: '1%',
                        bottom: '20%',
                        cellSize: [20, 20],
                        range: ['2025-07-01', '2025-07-31'],
                        orient: 'horizontal',
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: '#ddd',
                                width: 1,
                                type: 'solid'
                            }
                        },
                        itemStyle: {
                            borderWidth: 1,
                            borderColor: '#ddd'
                        },
                        yearLabel: {
                            show: false
                        },
                        monthLabel: {
                            nameMap: 'cn',
                            fontSize: 14,
                            color: '#333',
                            margin: 15
                        },
                        dayLabel: {
                            nameMap: 'cn',
                            fontSize: 12,
                            color: '#666',
                            margin: 10
                        }
                    }
                ],
                series: [
                    {
                        type: 'heatmap',
                        coordinateSystem: 'calendar',
                        data: calendarData,
                        itemStyle: {
                            borderRadius: 3
                        },
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 20,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }
                ]
            };

            myCalendarChart.setOption(calendarOption);

            // 响应窗口大小变化
            window.addEventListener('resize', function() {
                myCalendarChart.resize();
            });
        });

        // 作业内容分布图表
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化ECharts实例
            var workContentChart = document.getElementById('workContentDistribution');
            var myPieChart = echarts.init(workContentChart);
            
            // 数据定义
            const workContentData = [
                { value: 49, name: 'JT026-24周转箱' },
                { value: 49, name: 'JT026-24电池' },
                { value: 35, name: '60' },
                { value: 31, name: 'JT028完成品' },
                { value: 25, name: 'JH027-SC待检品' },
                { value: 24, name: 'JH027-SC周转箱' },
                { value: 20, name: 'JT028周转箱' },
                { value: 18, name: 'JH027-SD待检品' },
                { value: 17, name: 'JH027-SD周转箱' },
                { value: 16, name: 'JT028' }
            ];
            
            // 饼图配色方案
            const colors = [
                '#ff9f7f', // JT026-24电池
                '#ffdb5c', // JT026-24周转箱
                '#fb7293', // JH027-SC待检品
                '#8378ea', // JH027-SD待检品
                '#b05080', // JT028周转箱
                '#bda29a', // JH027-SC周转箱
                '#9a60b4', // JH027-SD周转箱
                '#e96453', // JT028完成品
                '#737373', // UF009完成品
                '#9d87f7', // JH027-SC/SD周转箱拼
                '#73c0de', // JH027-SB周转箱
                '#fbd07c', // JH027-SB电池
                '#2f4554', // JT027-SB周转箱
                '#ec6d71', // JH011-SH完成品
                '#5470c6', // JH011-SH空箱
                '#07a2a4'  // US001
            ];
            
            // 饼图配置
            const pieOption = {
                title: {
                    text: '作业内容分布',
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 'normal'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    type: 'scroll',
                    orient: 'horizontal',
                    top: 'bottom',
                    left: 'center',
                    data: workContentData.map(item => item.name)
                },
                color: colors,
                series: [
                    {
                        name: '作业内容',
                        type: 'pie',
                        radius: ['35%', '70%'],
                        center: ['50%', '50%'],
                        avoidLabelOverlap: true,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: true,
                            position: 'outside',
                            formatter: '{b}\n{c}'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '14',
                                fontWeight: 'bold'
                            },
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        },
                        labelLine: {
                            show: true
                        },
                        data: workContentData,
                        animationType: 'scale',
                        animationEasing: 'elasticOut',
                        animationDelay: function (idx) {
                            return Math.random() * 200;
                        }
                    }
                ]
            };
            
            // 带渐变效果的玫瑰图配置
            const roseOption = {
                title: {
                    text: '作业内容分布(玫瑰图)',
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 'normal'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    type: 'scroll',
                    orient: 'horizontal',
                    top: 'bottom',
                    left: 'center',
                    data: workContentData.map(item => item.name)
                },
                color: colors,
                series: [
                    {
                        name: '作业内容',
                        type: 'pie',
                        radius: ['20%', '70%'],
                        center: ['50%', '50%'],
                        roseType: 'area',
                        itemStyle: {
                            borderRadius: 8,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: true,
                            formatter: '{b}\n{c}'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '14',
                                fontWeight: 'bold'
                            }
                        },
                        data: workContentData,
                        animationType: 'scale',
                        animationEasing: 'elasticOut',
                        animationDelay: function (idx) {
                            return Math.random() * 200;
                        }
                    }
                ]
            };
            
            // 设置初始图表
            myPieChart.setOption(pieOption);
            
            // 每4秒切换图表类型
            let currentPieOption = pieOption;
            setInterval(function () {
                currentPieOption = currentPieOption === pieOption ? roseOption : pieOption;
                myPieChart.setOption(currentPieOption, true);
            }, 4000);
            
            // 响应窗口大小变化
            window.addEventListener('resize', function() {
                myPieChart.resize();
            });
        });
        
        // 作业时长分布图表
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化ECharts实例
            var durationChart = document.getElementById('workDurationDistribution');
            var myDurationChart = echarts.init(durationChart);
            
            // 数据定义 - 基于图片中的数据
            const durations = ['0.5小时', '1小时', '1.5小时', '2小时', '2.5小时', '3小时', '3.5小时'];
            const counts = [10, 59, 17, 170, 29, 95, 39];
            const total = counts.reduce((sum, count) => sum + count, 0);
            const percentages = counts.map(count => ((count / total) * 100).toFixed(1));
            
            // 柱状图配置
            const barOption = {
                title: {
                    text: '作业时长分布',
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 'normal'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function(params) {
                        return `${params[0].name}: ${params[0].value}个作业 (${percentages[params[0].dataIndex]}%)`;
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '10%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: durations,
                    axisTick: {
                        alignWithLabel: true
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '作业数量',
                    minInterval: 1,
                    axisLabel: {
                        formatter: '{value}'
                    }
                },
                series: [
                    {
                        name: '作业数量',
                        type: 'bar',
                        barWidth: '60%',
                        data: counts,
                        itemStyle: {
                            color: function(params) {
                                // 2小时和3小时突出显示
                                if (params.dataIndex === 3) return '#4f46e5'; // 2小时
                                if (params.dataIndex === 5) return '#8b5cf6'; // 3小时
                                return '#73c0de';
                            },
                            borderRadius: [4, 4, 0, 0]
                        },
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        },
                        label: {
                            show: true,
                            position: 'top',
                            formatter: function(params) {
                                return params.value + '个';
                            }
                        },
                        animationDelay: function (idx) {
                            return idx * 100;
                        }
                    }
                ],
                animationEasing: 'elasticOut',
                animationDelayUpdate: function (idx) {
                    return idx * 5;
                }
            };
            
            // 堆叠图配置 - 展示占比情况
            const stackedOption = {
                title: {
                    text: '作业时长占比分布',
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 'normal'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function(params) {
                        return `${params[0].name}: ${params[0].value}个作业 (${percentages[params[0].dataIndex]}%)`;
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '10%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    name: '作业数量',
                    max: total,
                    axisLabel: {
                        formatter: '{value}'
                    }
                },
                yAxis: {
                    type: 'category',
                    data: ['作业时长分布'],
                    axisLabel: {
                        show: false
                    }
                },
                series: durations.map((duration, index) => {
                    return {
                        name: duration,
                        type: 'bar',
                        stack: 'total',
                        label: {
                            show: index === 3 || index === 5, // 只显示2小时和3小时的标签
                            position: 'inside',
                            formatter: function(params) {
                                if (percentages[index] > 5) {
                                    return `${duration}: ${params.value}个 (${percentages[index]}%)`;
                                }
                                return '';
                            },
                            color: '#fff',
                            fontSize: 12,
                            fontWeight: 'bold'
                        },
                        data: [counts[index]],
                        itemStyle: {
                            color: function() {
                                // 颜色方案
                                const colors = [
                                    '#5470c6', // 0.5小时
                                    '#73c0de', // 1小时
                                    '#91cc75', // 1.5小时
                                    '#4f46e5', // 2小时
                                    '#fac858', // 2.5小时
                                    '#8b5cf6', // 3小时
                                    '#ee6666'  // 3.5小时
                                ];
                                return colors[index];
                            }
                        }
                    };
                }),
                animationEasing: 'elasticOut',
                animationDelay: function (idx) {
                    return idx * 100;
                }
            };
            
            // 折线图配置
            const lineOption = {
                title: {
                    text: '作业时长分布趋势',
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 'normal'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        return `${params[0].name}: ${params[0].value}个作业 (${percentages[params[0].dataIndex]}%)`;
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '10%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: durations,
                    boundaryGap: false
                },
                yAxis: {
                    type: 'value',
                    name: '作业数量',
                    minInterval: 1,
                    axisLabel: {
                        formatter: '{value}'
                    }
                },
                series: [
                    {
                        name: '作业数量',
                        type: 'line',
                        data: counts,
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 8,
                        itemStyle: {
                            color: '#2563eb'
                        },
                        lineStyle: {
                            width: 3,
                            color: '#2563eb'
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {
                                    offset: 0,
                                    color: 'rgba(37, 99, 235, 0.6)'
                                },
                                {
                                    offset: 1,
                                    color: 'rgba(37, 99, 235, 0.1)'
                                }
                            ])
                        },
                        markPoint: {
                            data: [
                                { type: 'max', name: '最大值' },
                                { type: 'min', name: '最小值' }
                            ],
                            symbolSize: 60
                        },
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '{c}个',
                            fontSize: 12
                        },
                        animationDelay: function (idx) {
                            return idx * 100;
                        }
                    }
                ],
                animationEasing: 'elasticOut',
                animationDelayUpdate: function (idx) {
                    return idx * 5;
                }
            };
            
            // 设置初始图表
            myDurationChart.setOption(barOption);
            
            // 每5秒切换图表类型
            let currentDurationOption = barOption;
            let optionIndex = 0;
            const options = [barOption, stackedOption, lineOption];
            
            setInterval(function () {
                optionIndex = (optionIndex + 1) % options.length;
                currentDurationOption = options[optionIndex];
                myDurationChart.setOption(currentDurationOption, true);
            }, 5000);
            
            // 响应窗口大小变化
            window.addEventListener('resize', function() {
                myDurationChart.resize();
            });
        });
        
        // 改进效果预期雷达图
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化ECharts实例
            var radarChart = document.getElementById('improvementRadarChart');
            var myRadarChart = echarts.init(radarChart);
            
            // 数据定义 - 基于图片中的数据
            const indicators = [
                { name: '日均作业标准差', max: 100 },
                { name: '栋别负荷差异', max: 100 },
                { name: '日内时间利用率', max: 100 },
                { name: '资源利用均衡性', max: 100 },
                { name: '计划可靠性', max: 100 }
            ];
            
            // 当前状态和改进后预期的数据（从图片估算）
            const currentStatus = [85, 90, 60, 50, 65]; // 当前状态 - 红色
            const improvedStatus = [30, 40, 80, 85, 90]; // 改进后预期 - 蓝色
            
            // 雷达图配置
            const radarOption = {
                title: {
                    text: '作业计划改进效果预期',
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 'normal'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        const indicator = indicators[params.dataIndex];
                        if (indicator) {
                            return `${indicator.name}: ${params.value}%`;
                        }
                        return params.name;
                    }
                },
                legend: {
                    data: ['当前状态', '改进后预期'],
                    bottom: '0%',
                    itemWidth: 12,
                    itemHeight: 12,
                    textStyle: {
                        fontSize: 12
                    }
                },
                radar: {
                    // 形状设置为圆形
                    shape: 'polygon',
                    // 雷达图中的指示器，用于指定雷达图中的多个变量
                    indicator: indicators,
                    // 雷达图的中心点和半径
                    center: ['50%', '50%'],
                    radius: '65%',
                    // 指示器轴的分割段数
                    splitNumber: 5,
                    // 指示器轴线样式
                    axisLine: {
                        lineStyle: {
                            color: '#ddd'
                        }
                    },
                    // 分割线样式
                    splitLine: {
                        lineStyle: {
                            color: '#eee'
                        }
                    },
                    // 分割区域样式
                    splitArea: {
                        show: true,
                        areaStyle: {
                            color: ['rgba(250, 250, 250, 0.3)', 'rgba(200, 200, 200, 0.1)']
                        }
                    },
                    // 指示器名称样式
                    axisName: {
                        color: '#333',
                        fontSize: 12,
                        fontWeight: 'bold',
                        backgroundColor: 'rgba(242, 242, 242, 0.8)',
                        padding: [4, 8],
                        borderRadius: 4
                    }
                },
                series: [
                    {
                        name: '作业计划改进对比',
                        type: 'radar',
                        // 选中状态样式
                        emphasis: {
                            lineStyle: {
                                width: 4
                            }
                        },
                        data: [
                            {
                                value: currentStatus,
                                name: '当前状态',
                                symbol: 'circle',
                                symbolSize: 6,
                                itemStyle: {
                                    color: '#ef4444'
                                },
                                lineStyle: {
                                    color: '#ef4444',
                                    width: 2
                                },
                                areaStyle: {
                                    color: 'rgba(239, 68, 68, 0.5)'
                                }
                            },
                            {
                                value: improvedStatus,
                                name: '改进后预期',
                                symbol: 'circle',
                                symbolSize: 6,
                                itemStyle: {
                                    color: '#2563eb'
                                },
                                lineStyle: {
                                    color: '#2563eb',
                                    width: 2
                                },
                                areaStyle: {
                                    color: 'rgba(37, 99, 235, 0.5)'
                                }
                            }
                        ]
                    }
                ],
                animationDuration: 1500,
                animationEasing: 'elasticOut'
            };
            
            // 增强版雷达图配置 - 添加标签和渐变效果
            const enhancedRadarOption = {
                title: {
                    text: '作业计划改进效果预期',
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 'normal'
                    }
                },
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    data: ['当前状态', '改进后预期'],
                    bottom: '0%',
                    itemWidth: 12,
                    itemHeight: 12,
                    textStyle: {
                        fontSize: 12
                    }
                },
                radar: {
                    shape: 'polygon',
                    indicator: indicators,
                    center: ['50%', '50%'],
                    radius: '65%',
                    splitNumber: 5,
                    axisLine: {
                        lineStyle: {
                            color: 'rgba(238, 197, 102, 0.3)'
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(238, 197, 102, 0.3)'
                        }
                    },
                    splitArea: {
                        show: true,
                        areaStyle: {
                            color: ['rgba(255, 255, 255, 0.0)', 'rgba(238, 197, 102, 0.05)']
                        }
                    },
                    axisName: {
                        color: '#333',
                        fontSize: 12,
                        fontWeight: 'bold'
                    }
                },
                series: [
                    {
                        name: '作业计划改进对比',
                        type: 'radar',
                        emphasis: {
                            lineStyle: {
                                width: 4
                            }
                        },
                        data: [
                            {
                                value: currentStatus,
                                name: '当前状态',
                                symbol: 'circle',
                                symbolSize: 8,
                                itemStyle: {
                                    color: '#ef4444'
                                },
                                lineStyle: {
                                    color: '#ef4444',
                                    width: 2
                                },
                                areaStyle: {
                                    color: new echarts.graphic.RadialGradient(0.5, 0.5, 1, [
                                        {
                                            color: 'rgba(239, 68, 68, 0.8)',
                                            offset: 0
                                        },
                                        {
                                            color: 'rgba(239, 68, 68, 0.3)',
                                            offset: 0.5
                                        },
                                        {
                                            color: 'rgba(239, 68, 68, 0.1)',
                                            offset: 1
                                        }
                                    ])
                                },
                                label: {
                                    show: true,
                                    formatter: function(params) {
                                        return params.value;
                                    },
                                    color: '#ef4444',
                                    fontSize: 12
                                }
                            },
                            {
                                value: improvedStatus,
                                name: '改进后预期',
                                symbol: 'circle',
                                symbolSize: 8,
                                itemStyle: {
                                    color: '#2563eb'
                                },
                                lineStyle: {
                                    color: '#2563eb',
                                    width: 2
                                },
                                areaStyle: {
                                    color: new echarts.graphic.RadialGradient(0.5, 0.5, 1, [
                                        {
                                            color: 'rgba(37, 99, 235, 0.8)',
                                            offset: 0
                                        },
                                        {
                                            color: 'rgba(37, 99, 235, 0.3)',
                                            offset: 0.5
                                        },
                                        {
                                            color: 'rgba(37, 99, 235, 0.1)',
                                            offset: 1
                                        }
                                    ])
                                },
                                label: {
                                    show: true,
                                    formatter: function(params) {
                                        return params.value;
                                    },
                                    color: '#2563eb',
                                    fontSize: 12
                                }
                            }
                        ]
                    }
                ],
                animationDuration: 1500,
                animationEasing: 'elasticOut'
            };
            
            // 设置初始图表
            myRadarChart.setOption(radarOption);
            
            // 每6秒切换图表样式
            let currentRadarOption = radarOption;
            setInterval(function () {
                currentRadarOption = currentRadarOption === radarOption ? enhancedRadarOption : radarOption;
                myRadarChart.setOption(currentRadarOption, true);
            }, 6000);
            
            // 响应窗口大小变化
            window.addEventListener('resize', function() {
                myRadarChart.resize();
            });
        });

        // 作业类型分布极坐标图
        document.addEventListener('DOMContentLoaded', function() {
            var chartDom = document.getElementById('workTypePolarChart');
            if (!chartDom) return;
            var myChart = echarts.init(chartDom);
            var option = {
                title: [
                    {
                        text: '作业类型分布（极坐标）',
                        left: 'center'
                    }
                ],
                polar: {
                    radius: [30, '80%']
                },
                angleAxis: {
                    max: 150, // 最大值略大于最大数据
                    startAngle: 75
                },
                radiusAxis: {
                    type: 'category',
                    data: ['入库', '出库', '备货', 'C栋仓库', '常熟']
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{b}: {c}次'
                },
                series: {
                    type: 'bar',
                    data: [132, 127, 21, 18, 13],
                    coordinateSystem: 'polar',
                    label: {
                        show: true,
                        position: 'middle',
                        formatter: '{b}: {c}次'
                    },
                    itemStyle: {
                        color: function(params) {
                            // 让出库和入库颜色突出
                            const colors = ['#4f46e5', '#a78bfa', '#f59e0b', '#10b981', '#ef4444'];
                            return colors[params.dataIndex];
                        }
                    }
                }
            };
            myChart.setOption(option);
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        });

        // 栋别作业量分布横向柱状图
        document.addEventListener('DOMContentLoaded', function() {
            var chartDom = document.getElementById('buildingWorkBarChart');
            if (!chartDom) return;
            var myChart = echarts.init(chartDom);
            var option = {
                title: {
                    text: '栋别作业量分布',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['作业数量'],
                    top: 30
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    min: 0,
                    max: 200
                },
                yAxis: {
                    type: 'category',
                    data: ['A栋', 'B栋', 'C栋']
                },
                series: [
                    {
                        name: '作业数量',
                        type: 'bar',
                        data: [63, 186, 84],
                        itemStyle: {
                            color: function(params) {
                                // B栋高亮，A栋粉色，C栋黄色
                                const colors = ['#ffb6c1', '#6ec6ff', '#ffe082'];
                                return colors[params.dataIndex];
                            }
                        },
                        barWidth: 40,
                        label: {
                            show: true,
                            position: 'right',
                            formatter: '{c}'
                        }
                    }
                ]
            };
            myChart.setOption(option);
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        });
    </script>

    <script>
        // 计划与实际对比分析图表
        document.addEventListener('DOMContentLoaded', function() {
            // 异常原因饼图
            var abnormalChart = echarts.init(document.getElementById('abnormalReasonsChart'));
            var abnormalOption = {
                title: {
                    text: '异常原因分布',
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 'normal'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    type: 'scroll',
                    orient: 'horizontal',
                    top: 'bottom',
                    left: 'center'
                },
                series: [{
                    name: '异常原因',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    center: ['50%', '50%'],
                    data: [
                        {value: 18, name: '车辆相关异常'},
                        {value: 4, name: '设备相关异常'},
                        {value: 5, name: '流程相关异常'},
                        {value: 3, name: '车辆8:40到厂'},
                        {value: 2, name: '自动仓库下货慢'},
                        {value: 2, name: '分三车转运调整'},
                        {value: 7, name: '其他原因'}
                    ],
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }]
            };
            abnormalChart.setOption(abnormalOption);

            // 加班原因饼图
            var overtimeChart = echarts.init(document.getElementById('overtimeReasonsChart'));
            var overtimeOption = {
                title: {
                    text: '加班原因分布',
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 'normal'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    type: 'scroll',
                    orient: 'horizontal',
                    top: 'bottom',
                    left: 'center'
                },
                series: [{
                    name: '加班原因',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    center: ['50%', '50%'],
                    data: [
                        {value: 11, name: '超过当天最大作业量'},
                        {value: 1, name: '提前入周转箱'}
                    ],
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }]
            };
            overtimeChart.setOption(overtimeOption);

            // 栋别对比柱状图
            var buildingChart = echarts.init(document.getElementById('buildingComparisonChart'));
            var buildingOption = {
                title: {
                    text: '各栋别异常率和加班率对比',
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 'normal'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['异常率', '加班率'],
                    top: 'bottom'
                },
                xAxis: {
                    type: 'category',
                    data: ['A栋', 'B栋', 'C栋']
                },
                yAxis: {
                    type: 'value',
                    name: '比率 (%)',
                    axisLabel: {
                        formatter: '{value}%'
                    }
                },
                series: [
                    {
                        name: '异常率',
                        type: 'bar',
                        data: [52.5, 4.8, 1.1],
                        itemStyle: {
                            color: '#ff7f7f'
                        }
                    },
                    {
                        name: '加班率',
                        type: 'bar',
                        data: [0.0, 4.3, 4.5],
                        itemStyle: {
                            color: '#7fbfff'
                        }
                    }
                ]
            };
            buildingChart.setOption(buildingOption);

            // 响应窗口大小变化
            window.addEventListener('resize', function() {
                abnormalChart.resize();
                overtimeChart.resize();
                buildingChart.resize();
            });
        });
    </script>

    <script>
    // 调试信息
    console.log('图表调试信息:');
    console.log('ECharts版本:', echarts.version);
    console.log('日期数据长度:', dates ? dates.length : '未定义');
    console.log('作业数量数据长度:', workCounts ? workCounts.length : '未定义');
    console.log('作业内容数据长度:', workContentData ? workContentData.length : '未定义');
    
    // 检查图表容器
    const containers = ['dateWorkDistribution', 'buildingWorkBarChart', 'workContentDistribution'];
    containers.forEach(id => {
        const element = document.getElementById(id);
        console.log(`容器 ${id}:`, element ? '存在' : '不存在');
    });
    </script>
    
</body>
</html>