import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json

# 读取Excel文件
file_path = r'计划vs实际\第一阶段\作业计划表ABC栋20250801~20250830_自动排程.xlsx'
df = pd.read_excel(file_path, sheet_name=0)

# 过滤有效数据
valid_df = df[df['日期'].notna() & df['日期'].astype(str).str.contains(r'^\d+/\d+$', na=False)]

print('=== 生成作业日历热力图数据 ===')

# 转换日期格式
def convert_date(date_str):
    """将8/1格式转换为2025-08-01格式"""
    month, day = date_str.split('/')
    return f'2025-{int(month):02d}-{int(day):02d}'

valid_df['标准日期'] = valid_df['日期'].apply(convert_date)

# 统计每日总作业数量
daily_total = valid_df.groupby('标准日期').size().reset_index(name='作业数量')

# 统计每日各栋别作业数量
daily_building = valid_df.groupby(['标准日期', '栋别']).size().unstack(fill_value=0)

# 生成完整的8月日期范围
start_date = datetime(2025, 8, 1)
end_date = datetime(2025, 8, 31)
date_range = []
current_date = start_date
while current_date <= end_date:
    date_range.append(current_date.strftime('%Y-%m-%d'))
    current_date += timedelta(days=1)

# 生成热力图数据
calendar_data = []
calendar_data_a = []
calendar_data_b = []
calendar_data_c = []

for date in date_range:
    # 总体数据
    total_count = daily_total[daily_total['标准日期'] == date]['作业数量'].sum()
    if total_count == 0:
        total_count = 0
    calendar_data.append([date, int(total_count)])
    
    # 各栋别数据
    a_count = daily_building.loc[date, 'A'] if date in daily_building.index and 'A' in daily_building.columns else 0
    b_count = daily_building.loc[date, 'B'] if date in daily_building.index and 'B' in daily_building.columns else 0
    c_count = daily_building.loc[date, 'C'] if date in daily_building.index and 'C' in daily_building.columns else 0
    
    calendar_data_a.append([date, int(a_count)])
    calendar_data_b.append([date, int(b_count)])
    calendar_data_c.append([date, int(c_count)])

# 计算统计数据
total_tasks = len(valid_df)
working_days = len([x for x in calendar_data if x[1] > 0])
max_daily_tasks = max([x[1] for x in calendar_data])
avg_daily_tasks = total_tasks / working_days if working_days > 0 else 0

# 模拟超时数据（基于作业强度）
overtime_data = []
for date, count in calendar_data:
    # 假设作业数量超过平均值的1.5倍时容易超时
    if count > avg_daily_tasks * 1.5:
        overtime_count = max(1, int(count * 0.2))  # 20%超时率
    elif count > avg_daily_tasks:
        overtime_count = max(0, int(count * 0.1))  # 10%超时率
    else:
        overtime_count = 0
    overtime_data.append([date, overtime_count])

total_overtime = sum([x[1] for x in overtime_data])
overtime_rate = (total_overtime / total_tasks * 100) if total_tasks > 0 else 0

# 生成JavaScript数据
js_data = f"""
// 作业日历热力图数据
const calendarData = {json.dumps(calendar_data)};
const calendarDataA = {json.dumps(calendar_data_a)};
const calendarDataB = {json.dumps(calendar_data_b)};
const calendarDataC = {json.dumps(calendar_data_c)};
const overtimeTrendData = {json.dumps(overtime_data)};

// 统计数据
const statsData = {{
    totalTasks: {total_tasks},
    overtimeTasks: {total_overtime},
    overtimeRate: {overtime_rate:.1f},
    workingDays: {working_days},
    maxDailyTasks: {max_daily_tasks},
    avgDailyTasks: {avg_daily_tasks:.1f}
}};
"""

# 保存数据
with open('calendar_heatmap_data.js', 'w', encoding='utf-8') as f:
    f.write(js_data)

print(f'总作业任务数: {total_tasks}')
print(f'超时作业任务数: {total_overtime}')
print(f'超时作业比例: {overtime_rate:.1f}%')
print(f'作业天数: {working_days}')
print(f'最高日作业量: {max_daily_tasks}')
print(f'平均日作业量: {avg_daily_tasks:.1f}')

print('\n各栋别作业分布:')
building_totals = valid_df['栋别'].value_counts()
for building, count in building_totals.items():
    print(f'{building}栋: {count}个作业')

print('\n数据已生成到 calendar_heatmap_data.js')
