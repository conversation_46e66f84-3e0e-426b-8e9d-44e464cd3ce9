# 🚀 部署指南

本文档详细介绍了作业平衡度分析仪表板的各种部署方式。

## 📋 部署前准备

### 系统要求
- Python 3.8 或更高版本
- 至少 2GB 内存
- 1GB 可用磁盘空间

### 依赖检查
```bash
python --version
pip --version
```

## 🖥️ 本地部署

### 方式一：直接运行
```bash
# 1. 克隆或下载项目
cd dashboard

# 2. 安装依赖
pip install -r requirements.txt

# 3. 运行应用
streamlit run app.py
```

### 方式二：使用启动脚本
```bash
# 1. 使用Python启动脚本
python run.py

# 2. 指定端口和主机
python run.py --host 0.0.0.0 --port 8502

# 3. 启用调试模式
python run.py --debug

# 4. 仅检查环境
python run.py --check
```

### 访问应用
打开浏览器访问：http://localhost:8501

## 🐳 Docker 部署

### 方式一：使用 Dockerfile
```bash
# 1. 构建镜像
docker build -t workload-dashboard .

# 2. 运行容器
docker run -p 8501:8501 workload-dashboard
```

### 方式二：使用 Docker Compose
```bash
# 1. 启动服务
docker-compose up -d

# 2. 查看日志
docker-compose logs -f

# 3. 停止服务
docker-compose down
```

### Docker 环境变量
```bash
# 设置环境变量
docker run -p 8501:8501 \
  -e DEBUG=false \
  -e LOG_LEVEL=INFO \
  -v $(pwd)/data:/app/data \
  workload-dashboard
```

## ☁️ 云端部署

### Streamlit Cloud
1. 将代码推送到 GitHub 仓库
2. 访问 [share.streamlit.io](https://share.streamlit.io)
3. 连接 GitHub 仓库
4. 选择主分支和 `app.py` 文件
5. 点击 "Deploy" 部署

### Heroku 部署
```bash
# 1. 安装 Heroku CLI
# 2. 登录 Heroku
heroku login

# 3. 创建应用
heroku create your-app-name

# 4. 设置构建包
heroku buildpacks:set heroku/python

# 5. 部署应用
git push heroku main
```

需要创建 `Procfile` 文件：
```
web: streamlit run app.py --server.port=$PORT --server.address=0.0.0.0
```

### AWS EC2 部署
```bash
# 1. 连接到 EC2 实例
ssh -i your-key.pem ubuntu@your-ec2-ip

# 2. 更新系统
sudo apt update && sudo apt upgrade -y

# 3. 安装 Python 和 pip
sudo apt install python3 python3-pip -y

# 4. 克隆项目
git clone your-repo-url
cd dashboard

# 5. 安装依赖
pip3 install -r requirements.txt

# 6. 使用 screen 运行应用
screen -S dashboard
python3 run.py --host 0.0.0.0 --port 8501

# 7. 分离 screen 会话
# 按 Ctrl+A 然后按 D
```

## 🔧 生产环境配置

### 反向代理 (Nginx)
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:8501;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### SSL 证书 (Let's Encrypt)
```bash
# 1. 安装 Certbot
sudo apt install certbot python3-certbot-nginx

# 2. 获取证书
sudo certbot --nginx -d your-domain.com

# 3. 自动续期
sudo crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

### 系统服务 (systemd)
创建 `/etc/systemd/system/dashboard.service`：
```ini
[Unit]
Description=Workload Dashboard
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/home/<USER>/dashboard
ExecStart=/usr/bin/python3 run.py --host 0.0.0.0 --port 8501
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启用服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable dashboard
sudo systemctl start dashboard
sudo systemctl status dashboard
```

## 📊 性能优化

### 缓存配置
```python
# 在 config/settings.py 中调整
CACHE_TTL_SECONDS = 3600  # 1小时缓存
```

### 内存优化
```bash
# 设置环境变量限制内存使用
export STREAMLIT_SERVER_MAX_UPLOAD_SIZE=200
export STREAMLIT_SERVER_MAX_MESSAGE_SIZE=200
```

### 数据库连接池
如果使用数据库，配置连接池：
```python
# 示例配置
DATABASE_CONFIG = {
    'pool_size': 10,
    'max_overflow': 20,
    'pool_timeout': 30,
    'pool_recycle': 3600
}
```

## 🔒 安全配置

### 访问控制
```python
# 在应用中添加身份验证
import streamlit_authenticator as stauth

# 配置用户认证
authenticator = stauth.Authenticate(
    credentials,
    'cookie_name',
    'signature_key',
    cookie_expiry_days=30
)
```

### 防火墙设置
```bash
# Ubuntu/Debian
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### 环境变量安全
```bash
# 使用 .env 文件存储敏感信息
echo "SECRET_KEY=your-secret-key" > .env
echo "DATABASE_URL=your-db-url" >> .env

# 在 .gitignore 中排除
echo ".env" >> .gitignore
```

## 📈 监控和日志

### 应用监控
```python
# 添加健康检查端点
@st.cache_data
def health_check():
    return {"status": "healthy", "timestamp": datetime.now()}
```

### 日志配置
```python
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log'),
        logging.StreamHandler()
    ]
)
```

### 性能监控
```bash
# 使用 htop 监控系统资源
sudo apt install htop
htop

# 使用 docker stats 监控容器
docker stats
```

## 🔄 自动化部署

### GitHub Actions
创建 `.github/workflows/deploy.yml`：
```yaml
name: Deploy Dashboard

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.9
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
    
    - name: Run tests
      run: |
        python -m pytest tests/
    
    - name: Deploy to production
      run: |
        # 部署脚本
        ./deploy.sh
```

## 🆘 故障排除

### 常见问题

1. **端口被占用**
```bash
# 查找占用端口的进程
lsof -i :8501
# 杀死进程
kill -9 PID
```

2. **内存不足**
```bash
# 检查内存使用
free -h
# 清理缓存
sudo sync && sudo sysctl vm.drop_caches=3
```

3. **依赖冲突**
```bash
# 使用虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows
```

4. **权限问题**
```bash
# 修改文件权限
chmod +x run.py
# 修改目录权限
chmod -R 755 dashboard/
```

### 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 查看系统日志
sudo journalctl -u dashboard -f

# 查看 Docker 日志
docker logs -f container_name
```

## 📞 技术支持

如果遇到部署问题，请：
1. 检查系统要求和依赖
2. 查看错误日志
3. 参考故障排除指南
4. 提交 Issue 到项目仓库
