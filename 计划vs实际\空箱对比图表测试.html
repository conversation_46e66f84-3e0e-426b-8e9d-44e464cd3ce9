<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>空箱对比图表测试</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .info {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #3498db;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 空箱纳入vs生产实际对比图表测试</h1>
        
        <div class="info">
            <strong>📊 测试说明：</strong>
            <p>本页面测试JT026机种的空箱纳入vs生产实际对比图表功能，验证双Y轴设计和三线对比效果。</p>
        </div>

        <h2>JT026 空箱纳入vs生产实际对比（8-9月）</h2>
        <div class="chart-container">
            <canvas id="testChart"></canvas>
        </div>

        <div class="info">
            <strong>🎯 图表解读：</strong>
            <ul>
                <li><strong>蓝色线（生产实际）</strong>：每个工作日稳定在1.8车辆</li>
                <li><strong>黄色线（空箱纳入）</strong>：每个工作日稳定在1.0车辆</li>
                <li><strong>红色线（每日差异）</strong>：显示-0.8的固定差异，表明空箱供应不足</li>
                <li><strong>周末模式</strong>：所有数值为0，符合实际生产安排</li>
            </ul>
        </div>
    </div>

    <script>
        // JT026测试数据（简化版，前20天）
        const testData = {
            dates: ["08-01","08-02","08-03","08-04","08-05","08-06","08-07","08-08","08-09","08-10","08-11","08-12","08-13","08-14","08-15","08-16","08-17","08-18","08-19","08-20"],
            actual: [1.8,1.8,0,0,1.8,1.8,1.8,1.8,1.8,0,0,1.8,1.8,1.8,1.8,1.8,0,0,1.8,1.8],
            empty_box: [1.0,1.0,0,0,1.0,1.0,1.0,1.0,1.0,0,0,1.0,1.0,1.0,1.0,1.0,0,0,1.0,1.0]
        };

        // 计算每日差异
        const dailyDifference = testData.empty_box.map((empty, index) => {
            return empty - testData.actual[index];
        });

        // 创建图表
        const ctx = document.getElementById('testChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: testData.dates,
                datasets: [
                    {
                        label: '生产实际',
                        data: testData.actual,
                        borderColor: 'rgba(52, 152, 219, 1)',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        borderWidth: 3,
                        fill: false,
                        tension: 0.1,
                        yAxisID: 'y'
                    },
                    {
                        label: '空箱纳入',
                        data: testData.empty_box,
                        borderColor: 'rgba(255, 193, 7, 1)',
                        backgroundColor: 'rgba(255, 193, 7, 0.1)',
                        borderWidth: 3,
                        fill: false,
                        tension: 0.1,
                        yAxisID: 'y'
                    },
                    {
                        label: '每日差异',
                        data: dailyDifference,
                        borderColor: 'rgba(231, 76, 60, 1)',
                        backgroundColor: 'rgba(231, 76, 60, 0.1)',
                        borderWidth: 3,
                        fill: false,
                        tension: 0.1,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'JT026 空箱纳入vs生产实际对比测试',
                        font: { size: 16, weight: 'bold' }
                    },
                    legend: {
                        position: 'top',
                        labels: {
                            boxWidth: 15,
                            font: { size: 12 }
                        }
                    }
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '数量（车辆）',
                            font: { size: 12 }
                        },
                        ticks: {
                            font: { size: 11 }
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: '差异（车辆）',
                            font: { size: 12 }
                        },
                        ticks: {
                            font: { size: 11 }
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    },
                    x: {
                        title: {
                            display: true,
                            text: '日期',
                            font: { size: 12 }
                        },
                        ticks: {
                            font: { size: 10 }
                        }
                    }
                }
            }
        });

        console.log('空箱对比图表测试加载完成');
        console.log('测试数据:', testData);
        console.log('每日差异:', dailyDifference);
    </script>
</body>
</html>
