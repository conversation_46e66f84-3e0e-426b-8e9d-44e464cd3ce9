#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终数据验证报告 - 确认HTML报告中的数据准确性
"""

import pandas as pd
from datetime import datetime

def final_verification():
    print('🔍 最终数据验证报告')
    print('=' * 60)
    
    # 读取Excel原始数据
    df = pd.read_excel('出荷统计表7.18（更新版).xlsx', header=1)
    
    # 获取8-9月列
    date_columns = [col for col in df.columns if isinstance(col, datetime)]
    aug_sep_columns = [col for col in date_columns if col.month in [8, 9] and col.year == 2025]
    aug_sep_columns.sort()
    
    print(f'📅 验证期间: 2025年8月1日 - 9月30日 ({len(aug_sep_columns)}天)')
    
    # Excel中的实际数据
    excel_data = {}
    machines = ['JT028', 'JT026', 'JH011-SH', 'JH027-SB', 'JH027-SC', 'JH027-SD']
    categories = ['生产计划', '生产实绩', '空箱纳入']
    
    for machine in machines:
        excel_data[machine] = {}
        for category in categories:
            data = df[(df['机种'] == machine) & (df['类别'] == category)]
            if not data.empty:
                total = 0
                for col in aug_sep_columns:
                    if col in data.columns:
                        val = data[col].iloc[0]
                        if pd.notna(val) and isinstance(val, (int, float)):
                            total += val
                excel_data[machine][category] = round(total, 2)
            else:
                excel_data[machine][category] = 0.0
    
    # HTML中修正后的数据
    html_data = {
        'JT028': {'生产计划': 69.00, '生产实绩': 69.00, '空箱纳入': 67.47},
        'JT026': {'生产计划': 86.15, '生产实绩': 86.15, '空箱纳入': 69.00},
        'JH011-SH': {'生产计划': 6.49, '生产实绩': 6.49, '空箱纳入': 6.49},
        'JH027-SB': {'生产计划': 13.10, '生产实绩': 15.58, '空箱纳入': 12.15},
        'JH027-SC': {'生产计划': 48.23, '生产实绩': 28.25, '空箱纳入': 28.25},
        'JH027-SD': {'生产计划': 35.15, '生产实绩': 46.80, '空箱纳入': 43.98}
    }
    
    print('\n📊 数据准确性验证结果:')
    print('-' * 60)
    print('机种\t\t类别\t\tExcel\t\tHTML\t\t状态')
    print('-' * 60)
    
    all_correct = True
    errors = []
    
    for machine in machines:
        for category in categories:
            excel_val = excel_data[machine][category]
            html_val = html_data[machine][category]
            diff = abs(excel_val - html_val)
            
            if diff > 0.01:  # 允许0.01的误差
                status = '❌ 错误'
                all_correct = False
                errors.append({
                    'machine': machine,
                    'category': category,
                    'excel': excel_val,
                    'html': html_val,
                    'diff': diff
                })
            else:
                status = '✅ 正确'
            
            print(f'{machine}\t{category}\t{excel_val}\t\t{html_val}\t\t{status}')
    
    print('\n📋 验证总结:')
    if all_correct:
        print('🎉 所有数据验证通过！HTML报告中的数据与Excel原始数据完全一致。')
        print('\n✅ 数据准确性确认:')
        print('  • 6个机种的生产计划数据 ✅')
        print('  • 6个机种的生产实绩数据 ✅')
        print('  • 6个机种的空箱纳入数据 ✅')
        print('  • 总计18个数据点全部准确 ✅')
    else:
        print(f'❌ 发现 {len(errors)} 个数据错误:')
        for error in errors:
            print(f'  • {error["machine"]} {error["category"]}: {error["html"]} → {error["excel"]} (差异: {error["diff"]:.3f})')
    
    print('\n📈 关键指标验证:')
    
    # 验证执行率计算
    print('执行率验证:')
    for machine in machines:
        plan = excel_data[machine]['生产计划']
        actual = excel_data[machine]['生产实绩']
        if plan > 0:
            execution_rate = (actual / plan) * 100
            print(f'  {machine}: {actual}/{plan} = {execution_rate:.1f}%')
        else:
            print(f'  {machine}: 无生产计划')
    
    # 验证空箱匹配度
    print('\n空箱匹配度验证:')
    for machine in machines:
        actual = excel_data[machine]['生产实绩']
        empty = excel_data[machine]['空箱纳入']
        if actual > 0:
            match_rate = min(empty / actual, 1.0) * 100
            print(f'  {machine}: {empty}/{actual} = {match_rate:.1f}%')
        else:
            print(f'  {machine}: 无生产实绩')
    
    print('\n🔒 数据完整性确认:')
    print(f'  • Excel文件: 出荷统计表7.18（更新版).xlsx')
    print(f'  • 数据期间: 2025年8月1日 - 9月30日 ({len(aug_sep_columns)}天)')
    print(f'  • 验证机种: {len(machines)}个')
    print(f'  • 验证类别: {len(categories)}个')
    print(f'  • 验证时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    
    return all_correct

if __name__ == "__main__":
    final_verification()
