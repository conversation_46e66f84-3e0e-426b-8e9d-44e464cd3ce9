import pandas as pd
from datetime import datetime

# 重新读取Excel文件
file_path = r'计划vs实际\第一阶段\出荷统计表8.4.xlsx'
df = pd.read_excel(file_path, sheet_name='8月')

print('=== 重新检查Excel数据结构 ===')
print(f'数据形状: {df.shape}')
print()

# 显示前20行的机种和类别信息
print('前20行的机种和类别信息:')
for i in range(min(20, len(df))):
    model = df.iloc[i]['机种']
    category = df.iloc[i]['类别']
    print(f'第{i+1:2d}行: 机种={str(model):12s} | 类别={category}')

print()

# 获取日期列
date_cols = [col for col in df.columns if isinstance(col, datetime)]
print(f'日期列数量: {len(date_cols)}')
print(f'日期范围: {date_cols[0].strftime("%Y-%m-%d")} 到 {date_cols[-1].strftime("%Y-%m-%d")}')
print()

# 显示每个机种的前几个数值以验证数据
print('=== 验证每个机种的数据 ===')
current_model = None
row_count = 0

for i in range(len(df)):
    model = df.iloc[i]['机种']
    category = df.iloc[i]['类别']
    
    if pd.notna(model):
        current_model = model
        row_count = 0
        print(f'\n{current_model}:')
    
    if pd.notna(category):
        # 显示该行前5个日期的数值
        first_5_values = df.iloc[i][date_cols[:5]].tolist()
        print(f'  {category:8s}: {first_5_values}')
        row_count += 1
        
        if row_count >= 4:  # 每个机种4行数据
            continue

print()
print('=== 完整数据检查 ===')
# 重新按机种分组检查所有数据
models = []
for i in range(len(df)):
    model = df.iloc[i]['机种']
    if pd.notna(model):
        models.append((i, model))

print(f'找到的机种: {[m[1] for m in models]}')

for i, (start_idx, model) in enumerate(models):
    print(f'\n{model} (从第{start_idx+1}行开始):')
    
    # 检查该机种的4行数据
    for j in range(4):
        if start_idx + j < len(df):
            row_idx = start_idx + j
            category = df.iloc[row_idx]['类别']
            
            # 计算该行的总和和非零值数量
            row_data = df.iloc[row_idx][date_cols]
            non_zero_count = (row_data != 0).sum()
            total_sum = row_data.sum()
            
            print(f'  第{row_idx+1:2d}行 {category:8s}: 非零天数={non_zero_count:2d}, 总计={total_sum:8.2f}')
