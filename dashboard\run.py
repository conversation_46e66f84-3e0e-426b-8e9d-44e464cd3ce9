#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仪表板启动脚本
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def setup_environment():
    """设置运行环境"""
    # 确保必要的目录存在
    directories = ['logs', 'data', 'temp']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    # 设置环境变量
    os.environ['PYTHONPATH'] = str(Path(__file__).parent)
    
    print("✅ 环境设置完成")

def check_dependencies():
    """检查依赖包"""
    try:
        import streamlit
        import pandas
        import plotly
        import numpy
        print("✅ 依赖包检查通过")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖包: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def run_dashboard(host='localhost', port=8501, debug=False):
    """运行仪表板"""
    if not check_dependencies():
        return False
    
    setup_environment()
    
    # 构建启动命令
    cmd = [
        sys.executable, '-m', 'streamlit', 'run', 'app.py',
        '--server.address', host,
        '--server.port', str(port),
        '--server.headless', 'true' if not debug else 'false',
        '--server.runOnSave', 'true' if debug else 'false',
        '--browser.gatherUsageStats', 'false'
    ]
    
    if debug:
        cmd.extend(['--logger.level', 'debug'])
    
    print(f"🚀 启动仪表板...")
    print(f"📍 访问地址: http://{host}:{port}")
    print(f"🔧 调试模式: {'开启' if debug else '关闭'}")
    print("按 Ctrl+C 停止服务")
    
    try:
        subprocess.run(cmd, cwd=Path(__file__).parent)
    except KeyboardInterrupt:
        print("\n👋 仪表板已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='作业平衡度分析仪表板启动器')
    parser.add_argument('--host', default='localhost', help='服务器地址 (默认: localhost)')
    parser.add_argument('--port', type=int, default=8501, help='服务器端口 (默认: 8501)')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--check', action='store_true', help='仅检查环境和依赖')
    
    args = parser.parse_args()
    
    if args.check:
        print("🔍 检查运行环境...")
        if check_dependencies():
            print("✅ 环境检查通过，可以正常运行")
        else:
            print("❌ 环境检查失败，请安装缺少的依赖")
        return
    
    # 运行仪表板
    success = run_dashboard(args.host, args.port, args.debug)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
