import pandas as pd
from datetime import datetime

# 重新仔细验证Excel数据
df = pd.read_excel('出荷统计表7.18（更新版).xlsx', header=1)

print('🔍 重新验证Excel数据准确性')
print('=' * 50)

# 获取8-9月列
date_columns = [col for col in df.columns if isinstance(col, datetime)]
aug_sep_columns = [col for col in date_columns if col.month in [8, 9] and col.year == 2025]
aug_sep_columns.sort()

print(f'8-9月数据列数: {len(aug_sep_columns)}')
print(f'日期范围: {min(aug_sep_columns)} 到 {max(aug_sep_columns)}')

# 验证所有机种的数据
machines = ['JT028', 'JT026', 'JH011-SH', 'JH027-SB', 'JH027-SC', 'JH027-SD']
categories = ['生产计划', '生产实绩', '空箱纳入']

print('\n📊 Excel中的实际数据:')
for machine in machines:
    print(f'\n{machine}:')
    for category in categories:
        data = df[(df['机种'] == machine) & (df['类别'] == category)]
        if not data.empty:
            total = 0
            for col in aug_sep_columns:
                if col in data.columns:
                    val = data[col].iloc[0]
                    if pd.notna(val) and isinstance(val, (int, float)):
                        total += val
            print(f'  {category}: {total:.2f}')
        else:
            print(f'  {category}: 数据未找到')

# 特别检查JH027-SB和JH027-SC的详细数据
print('\n🔍 详细检查JH027-SB生产实绩:')
jh027sb_actual = df[(df['机种'] == 'JH027-SB') & (df['类别'] == '生产实绩')]
if not jh027sb_actual.empty:
    print('前10天数据:')
    for i, col in enumerate(aug_sep_columns[:10]):
        val = jh027sb_actual[col].iloc[0]
        date_str = col.strftime('%m-%d')
        print(f'  {date_str}: {val}')

print('\n🔍 详细检查JH027-SC生产计划:')
jh027sc_plan = df[(df['机种'] == 'JH027-SC') & (df['类别'] == '生产计划')]
if not jh027sc_plan.empty:
    print('前10天数据:')
    for i, col in enumerate(aug_sep_columns[:10]):
        val = jh027sc_plan[col].iloc[0]
        date_str = col.strftime('%m-%d')
        print(f'  {date_str}: {val}')

# 计算精确的总量
print('\n📊 精确计算总量:')
jh027sb_total = 0
for col in aug_sep_columns:
    val = jh027sb_actual[col].iloc[0]
    if pd.notna(val) and isinstance(val, (int, float)):
        jh027sb_total += val

jh027sc_total = 0
for col in aug_sep_columns:
    val = jh027sc_plan[col].iloc[0]
    if pd.notna(val) and isinstance(val, (int, float)):
        jh027sc_total += val

print(f'JH027-SB 生产实绩精确总量: {jh027sb_total:.10f}')
print(f'JH027-SC 生产计划精确总量: {jh027sc_total:.10f}')

# 四舍五入到小数点后2位
print(f'JH027-SB 生产实绩(保留2位): {round(jh027sb_total, 2)}')
print(f'JH027-SC 生产计划(保留2位): {round(jh027sc_total, 2)}')
