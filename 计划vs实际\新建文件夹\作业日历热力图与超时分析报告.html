<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>作业日历热力图与超时分析报告</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f8f9fa;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: none;
        }
        .card-header {
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        .card-title {
            color: #2c3e50;
            font-size: 1.4rem;
            font-weight: 600;
            margin: 0;
            display: inline-block;
            margin-left: 10px;
        }
        .chart-container {
            min-height: 400px;
            margin: 20px 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
            color: #856404;
        }
        .overtime-alert {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .chart-caption {
            text-align: center;
            color: #666;
            font-size: 0.9rem;
            margin-top: 10px;
            font-style: italic;
        }
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
        }
        @media (max-width: 768px) {
            .two-column {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-calendar-alt me-3"></i>作业日历热力图与超时分析报告</h1>
            <p class="mb-0">数据期间：2025-08-01 至 2025-09-30</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">583</div>
                <div class="stat-label">总作业任务数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">116</div>
                <div class="stat-label">超时作业任务数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">19.9%</div>
                <div class="stat-label">超时作业比例</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">61</div>
                <div class="stat-label">作业天数</div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <i class="fas fa-calendar-alt"></i>
                <h3 class="card-title">作业日历热力图</h3>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <div id="calendarHeatmap" style="width: 100%; height: 500px;"></div>
                    <p class="chart-caption">2025年8-9月作业数量日历热力图，颜色越深表示作业强度越高</p>
                </div>
                <div class="overtime-alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>作业强度分析：</strong>从热力图可以看出，作业分布不均匀。
                    最高强度达到<span class="highlight">19个任务/天</span>，
                    超时作业占比<span class="highlight">19.9%</span>，
                    需要关注作业负荷平衡和超时管理。
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <i class="fas fa-building"></i>
                <h3 class="card-title">各栋别作业日历热力图</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h5 class="text-center mb-3">A栋作业热力图</h5>
                        <div class="chart-container" style="height: 300px;">
                            <div id="calendarHeatmapA" style="width: 100%; height: 100%;"></div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h5 class="text-center mb-3">B栋作业热力图</h5>
                        <div class="chart-container" style="height: 300px;">
                            <div id="calendarHeatmapB" style="width: 100%; height: 100%;"></div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h5 class="text-center mb-3">C栋作业热力图</h5>
                        <div class="chart-container" style="height: 300px;">
                            <div id="calendarHeatmapC" style="width: 100%; height: 100%;"></div>
                        </div>
                    </div>
                </div>
                <p class="chart-caption">各栋别8-9月作业分布热力图，可对比不同栋别的作业强度差异</p>
                <div class="overtime-alert">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>栋别对比分析：</strong>
                    从分栋别热力图可以清晰看出，<span class="highlight">B栋作业密度最高且分布最广</span>，
                    <span class="highlight">A栋作业相对较少且集中</span>，
                    <span class="highlight">C栋作业强度中等但超时较多</span>。
                    建议重新平衡各栋别的作业分配。
                </div>
            </div>
        </div>

        <div class="two-column">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-building"></i>
                    <h3 class="card-title">各栋别作业分布</h3>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <div id="buildingDistribution" style="width: 100%; height: 400px;"></div>
                        <p class="chart-caption">A、B、C三栋作业量分布情况</p>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <i class="fas fa-clock"></i>
                    <h3 class="card-title">各栋别超时作业分布</h3>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <div id="overtimeBuildingDistribution" style="width: 100%; height: 400px;"></div>
                        <p class="chart-caption">各栋别超过17:30标准工时的作业数量</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-line"></i>
                <h3 class="card-title">超时作业趋势分析</h3>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <div id="overtimeTrend" style="width: 100%; height: 400px;"></div>
                    <p class="chart-caption">每日超时作业数量变化趋势</p>
                </div>
                <div class="overtime-alert">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>超时分析结论：</strong>
                    总计<span class="highlight">116个超时作业</span>，
                    占总作业量的<span class="highlight">19.9%</span>。
                    超时作业主要集中在B栋，建议优化作业安排，合理分配工作负荷。
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <i class="fas fa-table"></i>
                <h3 class="card-title">超时作业详细列表</h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>日期</th>
                                <th>栋别</th>
                                <th>起始时间</th>
                                <th>截止时间</th>
                                <th>作业内容</th>
                                <th>作业时长</th>
                                <th>数量</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>8/1</td>
                                <td>B</td>
                                <td>17:00</td>
                                <td class="text-danger"><strong>19:30</strong></td>
                                <td>JT026-24电池</td>
                                <td>2:30</td>
                                <td>36.0</td>
                            </tr>
                            <tr>
                                <td>8/1</td>
                                <td>B</td>
                                <td>19:30</td>
                                <td class="text-danger"><strong>22:00</strong></td>
                                <td>JT026-24电池</td>
                                <td>2:30</td>
                                <td>36.0</td>
                            </tr>
                            <tr>
                                <td>8/1</td>
                                <td>B</td>
                                <td>22:00</td>
                                <td class="text-danger"><strong>00:30</strong></td>
                                <td>JT026-24电池</td>
                                <td>2:30</td>
                                <td>36.0</td>
                            </tr>
                            <tr>
                                <td>8/1</td>
                                <td>B</td>
                                <td>16:00</td>
                                <td class="text-danger"><strong>18:00</strong></td>
                                <td>JH027-SB周转箱</td>
                                <td>2:00</td>
                                <td>40.0</td>
                            </tr>
                            <tr>
                                <td>8/1</td>
                                <td>B</td>
                                <td>00:30</td>
                                <td class="text-danger"><strong>02:30</strong></td>
                                <td>JH027-SC待检品</td>
                                <td>2:00</td>
                                <td>40.0</td>
                            </tr>
                            <tr>
                                <td>8/1</td>
                                <td>B</td>
                                <td>02:30</td>
                                <td class="text-danger"><strong>04:30</strong></td>
                                <td>JH027-SC待检品</td>
                                <td>2:00</td>
                                <td>40.0</td>
                            </tr>
                            <tr>
                                <td>8/4</td>
                                <td>C</td>
                                <td>16:30</td>
                                <td class="text-danger"><strong>19:30</strong></td>
                                <td>JT028完成品</td>
                                <td>3:00</td>
                                <td>44.0</td>
                            </tr>
                            <tr>
                                <td>8/5</td>
                                <td>B</td>
                                <td>17:30</td>
                                <td class="text-danger"><strong>19:30</strong></td>
                                <td>JH027-SD周转箱</td>
                                <td>2:00</td>
                                <td>40.0</td>
                            </tr>
                            <tr>
                                <td>8/5</td>
                                <td>C</td>
                                <td>16:30</td>
                                <td class="text-danger"><strong>19:30</strong></td>
                                <td>JT028完成品</td>
                                <td>3:00</td>
                                <td>44.0</td>
                            </tr>
                            <tr>
                                <td>8/5</td>
                                <td>C</td>
                                <td>19:30</td>
                                <td class="text-danger"><strong>22:30</strong></td>
                                <td>JT028完成品</td>
                                <td>3:00</td>
                                <td>44.0</td>
                            </tr>
                            <tr>
                                <td>8/5</td>
                                <td>C</td>
                                <td>22:30</td>
                                <td class="text-danger"><strong>00:30</strong></td>
                                <td>US001</td>
                                <td>2:00</td>
                                <td>40.0</td>
                            </tr>
                            <tr>
                                <td>8/5</td>
                                <td>C</td>
                                <td>00:30</td>
                                <td class="text-danger"><strong>02:30</strong></td>
                                <td>US001</td>
                                <td>2:00</td>
                                <td>40.0</td>
                            </tr>
                            <tr>
                                <td>8/5</td>
                                <td>C</td>
                                <td>02:30</td>
                                <td class="text-danger"><strong>04:30</strong></td>
                                <td>US001</td>
                                <td>2:00</td>
                                <td>40.0</td>
                            </tr>
                            <tr>
                                <td>8/6</td>
                                <td>B</td>
                                <td>16:30</td>
                                <td class="text-danger"><strong>18:30</strong></td>
                                <td>JH027-SD待检品</td>
                                <td>2:00</td>
                                <td>40.0</td>
                            </tr>
                            <tr>
                                <td>8/6</td>
                                <td>C</td>
                                <td>16:30</td>
                                <td class="text-danger"><strong>19:30</strong></td>
                                <td>JT028完成品</td>
                                <td>3:00</td>
                                <td>44.0</td>
                            </tr>
                            <tr>
                                <td>8/6</td>
                                <td>C</td>
                                <td>19:30</td>
                                <td class="text-danger"><strong>22:30</strong></td>
                                <td>JT028完成品</td>
                                <td>3:00</td>
                                <td>44.0</td>
                            </tr>
                            <tr>
                                <td>8/8</td>
                                <td>B</td>
                                <td>17:00</td>
                                <td class="text-danger"><strong>19:30</strong></td>
                                <td>JT026-24电池</td>
                                <td>2:30</td>
                                <td>36.0</td>
                            </tr>
                            <tr>
                                <td>8/8</td>
                                <td>B</td>
                                <td>19:30</td>
                                <td class="text-danger"><strong>21:30</strong></td>
                                <td>JH027-SD待检品</td>
                                <td>2:00</td>
                                <td>40.0</td>
                            </tr>
                            <tr>
                                <td>8/8</td>
                                <td>B</td>
                                <td>21:30</td>
                                <td class="text-danger"><strong>23:30</strong></td>
                                <td>JH027-SD待检品</td>
                                <td>2:00</td>
                                <td>40.0</td>
                            </tr>
                            <tr>
                                <td>8/12</td>
                                <td>C</td>
                                <td>17:30</td>
                                <td class="text-danger"><strong>19:30</strong></td>
                                <td>US001</td>
                                <td>2:00</td>
                                <td>40.0</td>
                            </tr>
                            <tr>
                                <td colspan="7" class="text-center text-muted">
                                    ... 还有 96 条超时记录
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 日历热力图数据
        const calendarData = [['2025-08-01', 16], ['2025-08-02', 5], ['2025-08-03', 6], ['2025-08-04', 12], ['2025-08-05', 19], ['2025-08-06', 12], ['2025-08-07', 8], ['2025-08-08', 7], ['2025-08-09', 2], ['2025-08-10', 5], ['2025-08-11', 7], ['2025-08-12', 13], ['2025-08-13', 5], ['2025-08-14', 7], ['2025-08-15', 9], ['2025-08-16', 3], ['2025-08-17', 8], ['2025-08-18', 13], ['2025-08-19', 16], ['2025-08-20', 8], ['2025-08-21', 10], ['2025-08-22', 15], ['2025-08-23', 5], ['2025-08-24', 7], ['2025-08-25', 11], ['2025-08-26', 15], ['2025-08-27', 7], ['2025-08-28', 15], ['2025-08-29', 7], ['2025-08-30', 6], ['2025-08-31', 6], ['2025-09-01', 12], ['2025-09-02', 17], ['2025-09-03', 6], ['2025-09-04', 11], ['2025-09-05', 16], ['2025-09-06', 7], ['2025-09-07', 6], ['2025-09-08', 10], ['2025-09-09', 16], ['2025-09-10', 6], ['2025-09-11', 14], ['2025-09-12', 15], ['2025-09-13', 5], ['2025-09-14', 8], ['2025-09-15', 9], ['2025-09-16', 15], ['2025-09-17', 7], ['2025-09-18', 11], ['2025-09-19', 13], ['2025-09-20', 6], ['2025-09-21', 6], ['2025-09-22', 10], ['2025-09-23', 18], ['2025-09-24', 9], ['2025-09-25', 17], ['2025-09-26', 12], ['2025-09-27', 6], ['2025-09-28', 6], ['2025-09-29', 3], ['2025-09-30', 1]];

        // 各栋别热力图数据
        const calendarDataA = [['2025-08-01', 2], ['2025-08-02', 0], ['2025-08-03', 0], ['2025-08-04', 1], ['2025-08-05', 4], ['2025-08-06', 1], ['2025-08-07', 3], ['2025-08-08', 0], ['2025-08-09', 0], ['2025-08-10', 1], ['2025-08-11', 1], ['2025-08-12', 4], ['2025-08-13', 0], ['2025-08-14', 2], ['2025-08-15', 0], ['2025-08-16', 0], ['2025-08-17', 1], ['2025-08-18', 2], ['2025-08-19', 2], ['2025-08-20', 1], ['2025-08-21', 2], ['2025-08-22', 1], ['2025-08-23', 0], ['2025-08-24', 1], ['2025-08-25', 1], ['2025-08-26', 3], ['2025-08-27', 0], ['2025-08-28', 2], ['2025-08-29', 0], ['2025-08-30', 0], ['2025-08-31', 0], ['2025-09-01', 0], ['2025-09-02', 3], ['2025-09-03', 0], ['2025-09-04', 1], ['2025-09-05', 0], ['2025-09-06', 0], ['2025-09-07', 0], ['2025-09-08', 0], ['2025-09-09', 2], ['2025-09-10', 0], ['2025-09-11', 2], ['2025-09-12', 0], ['2025-09-13', 0], ['2025-09-14', 0], ['2025-09-15', 0], ['2025-09-16', 1], ['2025-09-17', 0], ['2025-09-18', 2], ['2025-09-19', 0], ['2025-09-20', 0], ['2025-09-21', 0], ['2025-09-22', 0], ['2025-09-23', 2], ['2025-09-24', 0], ['2025-09-25', 1], ['2025-09-26', 0], ['2025-09-27', 0], ['2025-09-28', 0], ['2025-09-29', 0], ['2025-09-30', 0]];
        const calendarDataB = [['2025-08-01', 12], ['2025-08-02', 3], ['2025-08-03', 4], ['2025-08-04', 7], ['2025-08-05', 7], ['2025-08-06', 6], ['2025-08-07', 3], ['2025-08-08', 7], ['2025-08-09', 2], ['2025-08-10', 2], ['2025-08-11', 4], ['2025-08-12', 4], ['2025-08-13', 4], ['2025-08-14', 5], ['2025-08-15', 8], ['2025-08-16', 1], ['2025-08-17', 5], ['2025-08-18', 6], ['2025-08-19', 7], ['2025-08-20', 2], ['2025-08-21', 7], ['2025-08-22', 10], ['2025-08-23', 1], ['2025-08-24', 4], ['2025-08-25', 6], ['2025-08-26', 5], ['2025-08-27', 5], ['2025-08-28', 12], ['2025-08-29', 4], ['2025-08-30', 2], ['2025-08-31', 4], ['2025-09-01', 8], ['2025-09-02', 6], ['2025-09-03', 4], ['2025-09-04', 8], ['2025-09-05', 12], ['2025-09-06', 2], ['2025-09-07', 4], ['2025-09-08', 6], ['2025-09-09', 7], ['2025-09-10', 4], ['2025-09-11', 10], ['2025-09-12', 11], ['2025-09-13', 2], ['2025-09-14', 5], ['2025-09-15', 6], ['2025-09-16', 6], ['2025-09-17', 6], ['2025-09-18', 7], ['2025-09-19', 10], ['2025-09-20', 2], ['2025-09-21', 4], ['2025-09-22', 6], ['2025-09-23', 5], ['2025-09-24', 7], ['2025-09-25', 12], ['2025-09-26', 9], ['2025-09-27', 2], ['2025-09-28', 4], ['2025-09-29', 3], ['2025-09-30', 1]];
        const calendarDataC = [['2025-08-01', 2], ['2025-08-02', 2], ['2025-08-03', 2], ['2025-08-04', 4], ['2025-08-05', 8], ['2025-08-06', 5], ['2025-08-07', 2], ['2025-08-08', 0], ['2025-08-09', 0], ['2025-08-10', 2], ['2025-08-11', 2], ['2025-08-12', 5], ['2025-08-13', 1], ['2025-08-14', 0], ['2025-08-15', 1], ['2025-08-16', 2], ['2025-08-17', 2], ['2025-08-18', 5], ['2025-08-19', 7], ['2025-08-20', 5], ['2025-08-21', 1], ['2025-08-22', 4], ['2025-08-23', 4], ['2025-08-24', 2], ['2025-08-25', 4], ['2025-08-26', 7], ['2025-08-27', 2], ['2025-08-28', 1], ['2025-08-29', 3], ['2025-08-30', 4], ['2025-08-31', 2], ['2025-09-01', 4], ['2025-09-02', 8], ['2025-09-03', 2], ['2025-09-04', 2], ['2025-09-05', 4], ['2025-09-06', 5], ['2025-09-07', 2], ['2025-09-08', 4], ['2025-09-09', 7], ['2025-09-10', 2], ['2025-09-11', 2], ['2025-09-12', 4], ['2025-09-13', 3], ['2025-09-14', 3], ['2025-09-15', 3], ['2025-09-16', 8], ['2025-09-17', 1], ['2025-09-18', 2], ['2025-09-19', 3], ['2025-09-20', 4], ['2025-09-21', 2], ['2025-09-22', 4], ['2025-09-23', 11], ['2025-09-24', 2], ['2025-09-25', 4], ['2025-09-26', 3], ['2025-09-27', 4], ['2025-09-28', 2], ['2025-09-29', 0], ['2025-09-30', 0]];

        // 栋别分布数据
        const buildingData = [{'name': 'A栋', 'value': 49}, {'name': 'B栋', 'value': 338}, {'name': 'C栋', 'value': 196}];

        // 超时栋别分布数据
        const overtimeBuildingData = [{'name': 'A栋', 'value': 0}, {'name': 'B栋', 'value': 60}, {'name': 'C栋', 'value': 56}];

        // 超时趋势数据
        const overtimeTrendData = [['2025-08-01', 6], ['2025-08-02', 0], ['2025-08-03', 0], ['2025-08-04', 1], ['2025-08-05', 6], ['2025-08-06', 3], ['2025-08-07', 0], ['2025-08-08', 3], ['2025-08-09', 0], ['2025-08-10', 0], ['2025-08-11', 0], ['2025-08-12', 1], ['2025-08-13', 0], ['2025-08-14', 1], ['2025-08-15', 1], ['2025-08-16', 0], ['2025-08-17', 1], ['2025-08-18', 2], ['2025-08-19', 4], ['2025-08-20', 2], ['2025-08-21', 0], ['2025-08-22', 5], ['2025-08-23', 1], ['2025-08-24', 0], ['2025-08-25', 1], ['2025-08-26', 4], ['2025-08-27', 0], ['2025-08-28', 6], ['2025-08-29', 0], ['2025-08-30', 1], ['2025-08-31', 0], ['2025-09-01', 2], ['2025-09-02', 6], ['2025-09-03', 0], ['2025-09-04', 1], ['2025-09-05', 7], ['2025-09-06', 2], ['2025-09-07', 0], ['2025-09-08', 1], ['2025-09-09', 5], ['2025-09-10', 0], ['2025-09-11', 3], ['2025-09-12', 6], ['2025-09-13', 0], ['2025-09-14', 0], ['2025-09-15', 0], ['2025-09-16', 6], ['2025-09-17', 1], ['2025-09-18', 2], ['2025-09-19', 6], ['2025-09-20', 1], ['2025-09-21', 0], ['2025-09-22', 1], ['2025-09-23', 7], ['2025-09-24', 1], ['2025-09-25', 6], ['2025-09-26', 2], ['2025-09-27', 1], ['2025-09-28', 0], ['2025-09-29', 0], ['2025-09-30', 0]];

        // 初始化日历热力图
        const calendarChart = echarts.init(document.getElementById('calendarHeatmap'));
        const calendarOption = {
            title: {
                text: '2025年8-9月作业日历热力图',
                left: 'center',
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'normal'
                }
            },
            tooltip: {
                position: 'top',
                formatter: function (params) {
                    const date = echarts.time.format(params.data[0], '{yyyy}-{MM}-{dd}', false);
                    return date + '<br/>作业数量: ' + params.data[1] + '个';
                }
            },
            visualMap: {
                min: 0,
                max: 19,
                calculable: true,
                orient: 'horizontal',
                left: 'center',
                bottom: '5%',
                inRange: {
                    color: ['#fff5f5', '#fed7d7', '#feb2b2', '#fc8181', '#f56565', '#e53e3e', '#c53030', '#9b2c2c']
                },
                text: ['高', '低'],
                textStyle: {
                    color: '#666'
                }
            },
            calendar: [
                {
                    top: '12%',
                    left: '1%',
                    right: '1%',
                    bottom: '20%',
                    cellSize: [20, 20],
                    range: ['2025-08-01', '2025-09-30'],
                    orient: 'horizontal',
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: '#ddd',
                            width: 1,
                            type: 'solid'
                        }
                    },
                    itemStyle: {
                        borderWidth: 1,
                        borderColor: '#ddd'
                    },
                    yearLabel: {
                        show: false
                    },
                    monthLabel: {
                        nameMap: 'cn',
                        fontSize: 14,
                        color: '#333',
                        margin: 15
                    },
                    dayLabel: {
                        nameMap: 'cn',
                        fontSize: 12,
                        color: '#666',
                        margin: 10
                    }
                }
            ],
            series: [
                {
                    type: 'heatmap',
                    coordinateSystem: 'calendar',
                    data: calendarData
                }
            ]
        };
        calendarChart.setOption(calendarOption);

        // 初始化各栋别热力图
        function createBuildingHeatmap(containerId, data, buildingName, maxValue) {
            const chart = echarts.init(document.getElementById(containerId));
            const option = {
                tooltip: {
                    position: 'top',
                    formatter: function (params) {
                        const date = echarts.time.format(params.data[0], '{yyyy}-{MM}-{dd}', false);
                        return date + '<br/>' + buildingName + '栋作业: ' + params.data[1] + '个';
                    }
                },
                visualMap: {
                    min: 0,
                    max: maxValue,
                    calculable: true,
                    orient: 'horizontal',
                    left: 'center',
                    bottom: '5%',
                    inRange: {
                        color: ['#fff5f5', '#fed7d7', '#feb2b2', '#fc8181', '#f56565', '#e53e3e']
                    },
                    show: false
                },
                calendar: [
                    {
                        top: '5%',
                        left: '3%',
                        right: '3%',
                        bottom: '15%',
                        cellSize: [12, 12],
                        range: ['2025-08-01', '2025-09-30'],
                        orient: 'horizontal',
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: '#ddd',
                                width: 1,
                                type: 'solid'
                            }
                        },
                        itemStyle: {
                            borderWidth: 1,
                            borderColor: '#ddd'
                        },
                        yearLabel: {
                            show: false
                        },
                        monthLabel: {
                            nameMap: 'cn',
                            fontSize: 10,
                            color: '#333',
                            margin: 8
                        },
                        dayLabel: {
                            nameMap: 'cn',
                            fontSize: 8,
                            color: '#666',
                            margin: 5
                        }
                    }
                ],
                series: [
                    {
                        type: 'heatmap',
                        coordinateSystem: 'calendar',
                        data: data
                    }
                ]
            };
            chart.setOption(option);
            return chart;
        }

        // 计算各栋别的最大值
        const maxA = Math.max(...calendarDataA.map(item => item[1]));
        const maxB = Math.max(...calendarDataB.map(item => item[1]));
        const maxC = Math.max(...calendarDataC.map(item => item[1]));

        // 创建各栋别热力图
        const chartA = createBuildingHeatmap('calendarHeatmapA', calendarDataA, 'A', maxA);
        const chartB = createBuildingHeatmap('calendarHeatmapB', calendarDataB, 'B', maxB);
        const chartC = createBuildingHeatmap('calendarHeatmapC', calendarDataC, 'C', maxC);

        // 初始化栋别分布图
        const buildingChart = echarts.init(document.getElementById('buildingDistribution'));
        const buildingOption = {
            title: {
                text: '各栋别作业分布',
                left: 'center',
                textStyle: {
                    fontSize: 14,
                    fontWeight: 'normal'
                }
            },
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'vertical',
                left: 'left'
            },
            series: [
                {
                    name: '作业分布',
                    type: 'pie',
                    radius: '50%',
                    data: buildingData,
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }
            ]
        };
        buildingChart.setOption(buildingOption);

        // 初始化超时栋别分布图
        const overtimeBuildingChart = echarts.init(document.getElementById('overtimeBuildingDistribution'));
        const overtimeBuildingOption = {
            title: {
                text: '各栋别超时作业分布',
                left: 'center',
                textStyle: {
                    fontSize: 14,
                    fontWeight: 'normal'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            xAxis: {
                type: 'category',
                data: overtimeBuildingData.map(item => item.name)
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '超时作业数',
                    type: 'bar',
                    data: overtimeBuildingData.map(item => item.value),
                    itemStyle: {
                        color: '#e53e3e'
                    }
                }
            ]
        };
        overtimeBuildingChart.setOption(overtimeBuildingOption);

        // 初始化超时趋势图
        const overtimeTrendChart = echarts.init(document.getElementById('overtimeTrend'));
        const overtimeTrendOption = {
            title: {
                text: '超时作业趋势',
                left: 'center',
                textStyle: {
                    fontSize: 14,
                    fontWeight: 'normal'
                }
            },
            tooltip: {
                trigger: 'axis'
            },
            xAxis: {
                type: 'time',
                boundaryGap: false
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '超时作业数',
                    type: 'line',
                    data: overtimeTrendData,
                    smooth: true,
                    lineStyle: {
                        color: '#e53e3e'
                    },
                    areaStyle: {
                        color: 'rgba(229, 62, 62, 0.1)'
                    }
                }
            ]
        };
        overtimeTrendChart.setOption(overtimeTrendOption);

        // 响应式调整
        window.addEventListener('resize', function() {
            calendarChart.resize();
            chartA.resize();
            chartB.resize();
            chartC.resize();
            buildingChart.resize();
            overtimeBuildingChart.resize();
            overtimeTrendChart.resize();
        });
    </script>
</body>
</html>