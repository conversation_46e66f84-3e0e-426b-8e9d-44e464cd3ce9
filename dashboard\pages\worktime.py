#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工时分析页面
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
from components.charts import ChartGenerator
from config.themes import AppleTheme

def render_worktime_page(data: pd.DataFrame, worktime_analyzer):
    """渲染工时分析页面"""
    
    # 页面标题
    st.markdown("""
        <div class="chart-container">
            <div class="chart-title">⏰ 工时分析详情</div>
        </div>
    """, unsafe_allow_html=True)
    
    if data is None or len(data) == 0:
        st.warning("⚠️ 暂无数据，请先上传数据文件")
        return
    
    # 计算每日工作负荷
    daily_workload = worktime_analyzer.calculate_daily_workload(data)
    
    if daily_workload.empty:
        st.error("❌ 无法计算工时数据，请检查数据格式")
        return
    
    # 分析加班模式
    overtime_analysis = worktime_analyzer.analyze_overtime_patterns(daily_workload)
    
    # 关键指标展示
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        total_hours = daily_workload['总工时'].sum()
        st.markdown(f"""
            <div class="metric-card">
                <div class="metric-value">{total_hours:.1f}</div>
                <div class="metric-label">总工作时长 (小时)</div>
                <div class="metric-delta positive">📊 统计完成</div>
            </div>
        """, unsafe_allow_html=True)
    
    with col2:
        avg_daily_hours = daily_workload['总工时'].mean()
        st.markdown(f"""
            <div class="metric-card">
                <div class="metric-value">{avg_daily_hours:.1f}</div>
                <div class="metric-label">平均日工时</div>
                <div class="metric-delta {'positive' if avg_daily_hours <= 8 else 'negative'}">
                    {'✅ 正常' if avg_daily_hours <= 8 else '⚠️ 偏高'}
                </div>
            </div>
        """, unsafe_allow_html=True)
    
    with col3:
        total_overtime = overtime_analysis.get('total_overtime_hours', 0)
        st.markdown(f"""
            <div class="metric-card">
                <div class="metric-value">{total_overtime:.1f}</div>
                <div class="metric-label">总加班时长</div>
                <div class="metric-delta {'negative' if total_overtime > 0 else 'positive'}">
                    {'⚠️ 需关注' if total_overtime > 0 else '✅ 无加班'}
                </div>
            </div>
        """, unsafe_allow_html=True)
    
    with col4:
        overtime_days = overtime_analysis.get('overtime_days', 0)
        st.markdown(f"""
            <div class="metric-card">
                <div class="metric-value">{overtime_days}</div>
                <div class="metric-label">加班天数</div>
                <div class="metric-delta {'negative' if overtime_days > 0 else 'positive'}">
                    总计 {len(daily_workload)} 天
                </div>
            </div>
        """, unsafe_allow_html=True)
    
    # 图表生成器
    chart_generator = ChartGenerator()
    
    # 工时趋势分析
    st.markdown("---")
    st.subheader("📈 工时趋势分析")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown('<div class="chart-container">', unsafe_allow_html=True)
        worktime_trend_fig = chart_generator.create_worktime_trend_chart(daily_workload)
        st.plotly_chart(worktime_trend_fig, use_container_width=True, config=chart_generator.config)
        st.markdown('</div>', unsafe_allow_html=True)
    
    with col2:
        st.markdown('<div class="chart-container">', unsafe_allow_html=True)
        overtime_fig = chart_generator.create_overtime_distribution_chart(daily_workload)
        st.plotly_chart(overtime_fig, use_container_width=True, config=chart_generator.config)
        st.markdown('</div>', unsafe_allow_html=True)
    
    # 栋别工时对比
    st.markdown("---")
    st.subheader("🏢 栋别工时对比")
    
    building_stats = daily_workload.groupby('栋别').agg({
        '总工时': ['sum', 'mean', 'max'],
        '加班工时': ['sum', 'mean'],
        '任务数量': 'sum',
        '效率指标': 'mean'
    }).round(2)
    
    building_stats.columns = ['总工时', '平均工时', '最大工时', '总加班', '平均加班', '总任务', '平均效率']
    
    # 显示统计表格
    st.dataframe(
        building_stats,
        use_container_width=True,
        column_config={
            "总工时": st.column_config.NumberColumn("总工时 (h)", format="%.1f"),
            "平均工时": st.column_config.NumberColumn("平均工时 (h)", format="%.1f"),
            "最大工时": st.column_config.NumberColumn("最大工时 (h)", format="%.1f"),
            "总加班": st.column_config.NumberColumn("总加班 (h)", format="%.1f"),
            "平均加班": st.column_config.NumberColumn("平均加班 (h)", format="%.1f"),
            "总任务": st.column_config.NumberColumn("总任务数", format="%d"),
            "平均效率": st.column_config.NumberColumn("平均效率", format="%.2f")
        }
    )
    
    # 效率分析
    st.markdown("---")
    st.subheader("⚡ 效率分析")
    
    col1, col2 = st.columns(2)
    
    with col1:
        # 效率雷达图
        st.markdown('<div class="chart-container">', unsafe_allow_html=True)
        radar_fig = chart_generator.create_efficiency_radar_chart(building_stats)
        st.plotly_chart(radar_fig, use_container_width=True, config=chart_generator.config)
        st.markdown('</div>', unsafe_allow_html=True)
    
    with col2:
        # 工时分布箱线图
        st.markdown('<div class="chart-container">', unsafe_allow_html=True)
        
        fig = go.Figure()
        
        colors = AppleTheme.get_color_palette()
        for i, building in enumerate(daily_workload['栋别'].unique()):
            building_data = daily_workload[daily_workload['栋别'] == building]
            
            fig.add_trace(go.Box(
                y=building_data['总工时'],
                name=f'{building}栋',
                marker_color=colors[i],
                boxpoints='outliers'
            ))
        
        fig.update_layout(
            title="📊 工时分布箱线图",
            yaxis_title="工时 (小时)",
            height=400,
            plot_bgcolor='white',
            paper_bgcolor='white'
        )
        
        st.plotly_chart(fig, use_container_width=True, config=chart_generator.config)
        st.markdown('</div>', unsafe_allow_html=True)
    
    # 时间段分析
    if '起始时间_parsed' in data.columns:
        st.markdown("---")
        st.subheader("🕐 时间段分析")
        
        peak_analysis = worktime_analyzer.identify_peak_hours(data)
        
        if peak_analysis:
            col1, col2, col3 = st.columns(3)
            
            with col1:
                peak_hour = peak_analysis.get('peak_hour', 'N/A')
                peak_count = peak_analysis.get('peak_count', 0)
                st.markdown(f"""
                    <div class="metric-card">
                        <div class="metric-value">{peak_hour}:00</div>
                        <div class="metric-label">高峰时段</div>
                        <div class="metric-delta positive">{peak_count} 个任务</div>
                    </div>
                """, unsafe_allow_html=True)
            
            with col2:
                morning_tasks = peak_analysis.get('morning_tasks', 0)
                st.markdown(f"""
                    <div class="metric-card">
                        <div class="metric-value">{morning_tasks}</div>
                        <div class="metric-label">上午任务数</div>
                        <div class="metric-delta positive">🌅 上午时段</div>
                    </div>
                """, unsafe_allow_html=True)
            
            with col3:
                afternoon_tasks = peak_analysis.get('afternoon_tasks', 0)
                st.markdown(f"""
                    <div class="metric-card">
                        <div class="metric-value">{afternoon_tasks}</div>
                        <div class="metric-label">下午任务数</div>
                        <div class="metric-delta positive">🌇 下午时段</div>
                    </div>
                """, unsafe_allow_html=True)
            
            # 时段分布图
            hourly_dist = peak_analysis.get('hourly_distribution', {})
            if hourly_dist:
                fig = go.Figure(data=[
                    go.Bar(
                        x=list(hourly_dist.keys()),
                        y=list(hourly_dist.values()),
                        marker_color=AppleTheme.COLORS['primary'],
                        text=list(hourly_dist.values()),
                        textposition='auto'
                    )
                ])
                
                fig.update_layout(
                    title="📊 各时段任务分布",
                    xaxis_title="开始时间 (小时)",
                    yaxis_title="任务数量",
                    height=300,
                    plot_bgcolor='white',
                    paper_bgcolor='white'
                )
                
                st.plotly_chart(fig, use_container_width=True, config=chart_generator.config)
    
    # 详细数据表
    st.markdown("---")
    st.subheader("📋 详细工时记录")
    
    # 添加筛选选项
    col1, col2, col3 = st.columns(3)
    
    with col1:
        selected_buildings = st.multiselect(
            "选择栋别",
            options=daily_workload['栋别'].unique(),
            default=daily_workload['栋别'].unique()
        )
    
    with col2:
        date_range = st.date_input(
            "选择日期范围",
            value=(daily_workload['标准日期'].min(), daily_workload['标准日期'].max()),
            min_value=daily_workload['标准日期'].min(),
            max_value=daily_workload['标准日期'].max()
        )
    
    with col3:
        show_overtime_only = st.checkbox("仅显示加班记录")
    
    # 筛选数据
    filtered_workload = daily_workload[daily_workload['栋别'].isin(selected_buildings)]
    
    if len(date_range) == 2:
        filtered_workload = filtered_workload[
            (filtered_workload['标准日期'] >= pd.Timestamp(date_range[0])) &
            (filtered_workload['标准日期'] <= pd.Timestamp(date_range[1]))
        ]
    
    if show_overtime_only:
        filtered_workload = filtered_workload[filtered_workload['加班工时'] > 0]
    
    # 显示筛选后的数据
    if not filtered_workload.empty:
        st.dataframe(
            filtered_workload.sort_values(['标准日期', '栋别']),
            use_container_width=True,
            column_config={
                "标准日期": st.column_config.DateColumn("日期"),
                "栋别": st.column_config.TextColumn("栋别"),
                "总工时": st.column_config.NumberColumn("总工时 (h)", format="%.1f"),
                "加班工时": st.column_config.NumberColumn("加班工时 (h)", format="%.1f"),
                "任务数量": st.column_config.NumberColumn("任务数", format="%d"),
                "平均工时": st.column_config.NumberColumn("平均工时 (h)", format="%.1f"),
                "效率指标": st.column_config.NumberColumn("效率", format="%.2f")
            },
            hide_index=True
        )
    else:
        st.info("没有符合筛选条件的数据")
