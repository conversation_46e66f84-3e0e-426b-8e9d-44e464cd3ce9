#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新HTML文件中的每日趋势数据
"""

import json
import re

def update_html_daily_trends():
    print('🔧 更新HTML文件中的每日趋势数据')
    print('=' * 50)
    
    # 读取正确的每日趋势数据
    with open('daily_trends_data.json', 'r', encoding='utf-8') as f:
        correct_data = json.load(f)
    
    # 验证数据
    print('✅ 验证正确的JT028 8月5日数据:')
    jt028_data = correct_data['JT028']
    print(f'  生产计划: {jt028_data["plan"][4]}')
    print(f'  生产实绩: {jt028_data["actual"][4]}')
    print(f'  空箱纳入: {jt028_data["empty_box"][4]}')
    
    # 生成JavaScript格式的数据
    print('\n📊 生成JavaScript格式的每日趋势数据...')
    
    js_data_lines = ['        const dailyTrendsData = {']
    
    machines = ['JT028', 'JT026', 'JH011-SH', 'JH027-SB', 'JH027-SC', 'JH027-SD']
    
    for i, machine in enumerate(machines):
        data = correct_data[machine]
        js_data_lines.append(f'            "{machine}": {{')
        js_data_lines.append(f'                "dates": {json.dumps(data["dates"])},')
        js_data_lines.append(f'                "plan": {json.dumps(data["plan"])},')
        js_data_lines.append(f'                "actual": {json.dumps(data["actual"])},')
        js_data_lines.append(f'                "empty_box": {json.dumps(data["empty_box"])}')
        
        if i < len(machines) - 1:
            js_data_lines.append('            },')
        else:
            js_data_lines.append('            }')
    
    js_data_lines.append('        };')
    
    new_daily_trends = '\n'.join(js_data_lines)
    
    # 读取HTML文件
    print('\n📝 读取HTML文件...')
    with open('统计表分析报告（8-9月生产计划vs实绩vs空箱）.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 查找并替换每日趋势数据
    print('🔍 查找每日趋势数据部分...')
    
    # 使用正则表达式找到dailyTrendsData部分
    pattern = r'const dailyTrendsData = \{.*?\};'
    match = re.search(pattern, html_content, re.DOTALL)
    
    if match:
        print('✅ 找到每日趋势数据部分')
        old_data = match.group(0)
        
        # 替换数据
        updated_html = html_content.replace(old_data, new_daily_trends)
        
        # 保存更新后的HTML文件
        print('💾 保存更新后的HTML文件...')
        with open('统计表分析报告（8-9月生产计划vs实绩vs空箱）.html', 'w', encoding='utf-8') as f:
            f.write(updated_html)
        
        print('✅ HTML文件已成功更新！')
        
        # 验证更新后的数据
        print('\n🔍 验证更新后的数据...')
        updated_match = re.search(pattern, updated_html, re.DOTALL)
        if updated_match:
            updated_data = updated_match.group(0)
            # 检查JT028的8月5日数据
            if '1.53333333333333' in updated_data:
                print('✅ JT028数据已正确更新')
            else:
                print('❌ JT028数据更新可能有问题')
        
        return True
    else:
        print('❌ 未找到每日趋势数据部分')
        return False

if __name__ == "__main__":
    success = update_html_daily_trends()
    if success:
        print('\n🎉 HTML文件中的每日趋势数据已成功更新为Excel原始数据！')
    else:
        print('\n❌ 更新失败，请检查HTML文件格式')
