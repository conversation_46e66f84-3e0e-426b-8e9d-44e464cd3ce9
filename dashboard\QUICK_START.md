# 🚀 快速开始指南

## 📋 准备工作清单

### ✅ 环境检查
```bash
# 1. 检查Python版本（需要3.8+）
python --version

# 2. 检查pip是否可用
pip --version
```

### ✅ 安装步骤
```bash
# 1. 进入项目目录
cd dashboard

# 2. 安装依赖（首次使用）
pip install -r requirements.txt

# 3. 生成演示数据（可选）
python demo_data.py
```

## 🎯 5分钟快速体验

### 第1步：启动仪表板 (30秒)
```bash
# 启动命令
python run.py

# 看到这个输出表示成功：
# 🚀 启动仪表板...
# 📍 访问地址: http://localhost:8501
```

### 第2步：打开浏览器 (10秒)
- 打开浏览器
- 访问：`http://localhost:8501`
- 看到欢迎界面

### 第3步：上传数据 (1分钟)
1. **点击左侧边栏的"上传Excel文件"**
2. **选择你的Excel文件**（或使用生成的demo_workload_data.xlsx）
3. **点击"🔄 加载数据"按钮**
4. **等待"✅ 数据加载成功！"提示**

### 第4步：查看分析结果 (3分钟)
1. **概览页面**：查看关键指标和整体趋势
2. **工时分析**：点击"⏰ 工时分析"标签
3. **交互探索**：尝试筛选不同栋别和时间范围

## 📊 界面功能速览

### 🎛️ 左侧控制面板
```
📁 数据管理
├── 📤 上传Excel文件
└── 🔄 加载数据

📅 时间范围
├── 📆 开始日期
└── 📆 结束日期

🏢 栋别筛选
├── ☑️ A栋
├── ☑️ B栋
└── ☑️ C栋

⚡ 系统状态
├── 📊 数据记录数
├── 🕐 最后更新
└── 💯 数据质量
```

### 📈 主界面标签页
```
📈 概览        ⏰ 工时分析      ⚖️ 负荷均衡
🚨 预警监控    🔮 预测分析
```

## 🎨 核心功能演示

### 📈 概览页面功能
**关键指标卡片**
- 🔢 总作业记录：显示数据总量
- ⏰ 总工时：所有工时的累计
- 🚨 总加班工时：超时工作统计
- 🏢 活跃栋别数：参与作业的栋别

**可视化图表**
- 📊 工时趋势图：每日工时变化
- 🥧 负荷分布饼图：各栋别工作量
- 📋 作业类型分布：任务类型统计
- 🔥 工时热力图：工作强度可视化

### ⏰ 工时分析功能
**详细统计**
- 📊 栋别对比表格
- 📈 趋势分析图表
- 🕸️ 效率雷达图
- 📦 工时分布箱线图

**时间段分析**
- 🌅 高峰时段识别
- 📊 时段分布图
- 🔍 详细记录查询

## 💡 使用技巧

### 🎯 数据上传技巧
1. **文件格式**：确保是.xlsx或.xls格式
2. **必需列**：包含日期、栋别、作业类型、起始时间、截止时间
3. **数据质量**：避免空行和格式错误
4. **文件大小**：建议单个文件不超过50MB

### 🔍 分析技巧
1. **时间筛选**：使用日期范围缩小分析范围
2. **栋别对比**：取消勾选某些栋别进行对比
3. **趋势观察**：关注图表中的异常点和趋势
4. **交互探索**：鼠标悬停查看详细数据

### 📊 图表交互
- **缩放**：鼠标滚轮放大缩小
- **平移**：拖拽移动视图
- **悬停**：查看具体数值
- **图例**：点击隐藏/显示数据系列

## 🚨 常见问题快速解决

### ❌ 数据加载失败
**问题**：点击加载数据后显示错误
**解决方案**：
```
1. 检查Excel文件是否包含必需的列
2. 确认日期格式正确（如"4/30"或"2025-04-30"）
3. 检查时间格式正确（如"8:30"）
4. 确保栋别列包含A、B、C等有效值
```

### 📊 图表显示异常
**问题**：图表空白或数据异常
**解决方案**：
```
1. 检查筛选条件是否过于严格
2. 确认选择的时间范围内有数据
3. 刷新浏览器页面
4. 重新上传数据文件
```

### 🐌 运行缓慢
**问题**：页面加载或响应缓慢
**解决方案**：
```
1. 减少数据量（按时间范围筛选）
2. 关闭其他浏览器标签页
3. 使用Chrome或Edge浏览器
4. 检查网络连接
```

## 📱 移动端使用

### 📲 手机/平板访问
1. **获取IP地址**：在电脑上运行`ipconfig`（Windows）或`ifconfig`（Mac/Linux）
2. **启动服务**：`python run.py --host 0.0.0.0`
3. **移动端访问**：`http://你的IP地址:8501`

### 📱 响应式界面
- 自动适配屏幕尺寸
- 触摸友好的交互
- 简化的移动端布局

## 🔧 高级配置

### ⚙️ 自定义端口
```bash
# 使用8502端口
python run.py --port 8502

# 允许外部访问
python run.py --host 0.0.0.0
```

### 🎨 主题配置
编辑 `config/themes.py` 文件：
```python
COLORS = {
    'primary': '#007AFF',    # 主色调
    'secondary': '#5AC8FA',  # 辅助色
    'success': '#34C759',    # 成功色
    'warning': '#FF9500',    # 警告色
    'danger': '#FF3B30',     # 危险色
}
```

### 📊 预警阈值
编辑 `config/settings.py` 文件：
```python
ALERT_THRESHOLDS = {
    'excessive_overtime': 10.0,      # 加班阈值（小时）
    'workload_imbalance': 2.0,       # 负荷不均比例
    'efficiency_drop': 0.5,          # 效率下降阈值
    'continuous_overtime': 3,        # 连续加班天数
}
```

## 📞 获取帮助

### 🆘 遇到问题时
1. **查看控制台**：按F12打开开发者工具查看错误
2. **检查日志**：查看命令行输出的错误信息
3. **重启服务**：按Ctrl+C停止，然后重新运行
4. **清理缓存**：刷新浏览器或清除缓存

### 📚 更多资源
- 📖 详细教程：查看 `TUTORIAL.md`
- 🚀 部署指南：查看 `DEPLOYMENT.md`
- 🔧 技术文档：查看 `README.md`

## 🎉 开始使用

现在你已经掌握了基本使用方法，可以：

1. **🚀 启动仪表板**：`python run.py`
2. **📊 上传你的数据**：使用真实的Excel文件
3. **🔍 探索分析功能**：尝试不同的筛选和图表
4. **📈 获得洞察**：基于数据做出决策

祝你使用愉快！ 🎊
