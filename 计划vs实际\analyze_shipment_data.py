#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
产品出荷数据分析脚本
读取Excel文件，分析每个机种的每日产品出荷情况，制作热力图
分析生产实绩与产品出荷的关系
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import json
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def read_excel_data(file_path):
    """读取Excel文件数据"""
    try:
        # 读取Excel文件，不使用第一行作为列名
        df = pd.read_excel(file_path, header=None)
        print(f"数据形状: {df.shape}")

        return df
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None

def analyze_shipment_data(df):
    """分析产品出荷数据"""
    print("\n=== 产品出荷数据分析 ===")

    # 根据CSV分析，我们知道：
    # 第1行（索引1）包含日期信息，从第2列开始
    # 机种数据结构：每个机种占4行（生产计划、空箱纳入、生产实绩、产品出荷）

    # 提取日期列（从第2列开始，共365天）
    date_row = df.iloc[1, 2:367]  # 从2025-04-01到2026-03-31
    dates = [date for date in date_row if pd.notna(date)]
    print(f"日期范围: {dates[0]} 到 {dates[-1]}")
    print(f"总天数: {len(dates)}")

    # 定义机种和对应的行位置
    machine_info = {
        'JT028': {'start_row': 2, 'shipment_row': 5},      # 产品出荷在第5行
        'JT026': {'start_row': 6, 'shipment_row': 9},      # 产品出荷在第9行
        'JH011-SH': {'start_row': 10, 'shipment_row': 13}, # 产品出荷在第13行
        'JH027-SB': {'start_row': 14, 'shipment_row': 17}, # 产品出荷在第17行
        'JH027-SC': {'start_row': 26, 'shipment_row': 29}, # 产品出荷在第29行
        'JH027-SD': {'start_row': 30, 'shipment_row': 33}  # 产品出荷在第33行
    }

    return dates, machine_info

def extract_shipment_matrix(df, dates, machine_info):
    """提取出荷数据矩阵"""
    print("\n=== 提取出荷数据矩阵 ===")

    # 创建出荷数据矩阵
    shipment_data = {}
    production_data = {}

    for machine_name, info in machine_info.items():
        print(f"\n处理机种: {machine_name}")

        # 提取产品出荷数据（从第2列开始，对应365天）
        shipment_row = df.iloc[info['shipment_row'], 2:367]
        daily_shipment = []

        for value in shipment_row:
            if pd.isna(value):
                daily_shipment.append(0)
            else:
                try:
                    daily_shipment.append(float(value))
                except:
                    daily_shipment.append(0)

        # 提取生产实绩数据（生产实绩在产品出荷行的前一行）
        production_row = df.iloc[info['shipment_row'] - 1, 2:367]
        daily_production = []

        for value in production_row:
            if pd.isna(value):
                daily_production.append(0)
            else:
                try:
                    daily_production.append(float(value))
                except:
                    daily_production.append(0)

        shipment_data[machine_name] = daily_shipment
        production_data[machine_name] = daily_production

        print(f"  提取了 {len(daily_shipment)} 天的出荷数据")
        print(f"  总出荷量: {sum(daily_shipment):.2f}")
        print(f"  总生产量: {sum(daily_production):.2f}")

    return shipment_data, production_data

def create_heatmap(shipment_data, dates, output_path):
    """创建出荷热力图"""
    print("\n=== 创建出荷热力图 ===")

    # 准备数据
    machines = list(shipment_data.keys())
    data_matrix = []

    for machine in machines:
        # 只取前91天的数据（4-6月）
        data_matrix.append(shipment_data[machine][:91])

    # 创建日期标签（4-6月）
    date_labels = []
    start_date = datetime(2025, 4, 1)
    for i in range(91):
        current_date = start_date + timedelta(days=i)
        if i % 7 == 0:  # 每周显示一个日期
            date_labels.append(current_date.strftime('%m-%d'))
        else:
            date_labels.append('')

    # 创建DataFrame
    df_heatmap = pd.DataFrame(data_matrix,
                             index=machines,
                             columns=[f"Day{i+1}" for i in range(91)])

    # 创建热力图
    plt.figure(figsize=(20, 8))

    # 使用红色系配色方案（从浅到深）
    sns.heatmap(df_heatmap,
                annot=False,  # 不显示数值标注（数据太多）
                cmap='Reds',  # 红色系配色
                cbar_kws={'label': '出荷量（车辆）'},
                xticklabels=7,  # 每7天显示一个标签
                yticklabels=True)

    plt.title('各机种每日产品出荷热力图（2025年4-6月）', fontsize=16, fontweight='bold')
    plt.xlabel('日期（4-6月）', fontsize=12)
    plt.ylabel('机种', fontsize=12)
    plt.xticks(rotation=45)
    plt.tight_layout()

    # 保存图片
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"热力图已保存到: {output_path}")

    return df_heatmap

def create_correlation_analysis(production_data, shipment_data, dates, output_path):
    """创建生产实绩与出荷量的相关性分析图"""
    print("\n=== 创建相关性分析图 ===")

    # 准备数据
    machines = list(production_data.keys())

    # 创建子图
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()

    correlations = {}

    for i, machine in enumerate(machines):
        if i < len(axes):
            ax = axes[i]

            # 获取生产和出荷数据（只取前91天）
            production = production_data[machine][:91]
            shipment = shipment_data.get(machine, [0] * 91)[:91]

            # 计算相关系数
            if len(production) > 1 and len(shipment) > 1:
                correlation = np.corrcoef(production, shipment)[0, 1]
                if np.isnan(correlation):
                    correlation = 0
                correlations[machine] = correlation
            else:
                correlation = 0
                correlations[machine] = 0

            # 创建散点图
            ax.scatter(production, shipment, alpha=0.6, s=30, color='red')
            ax.set_xlabel('生产实绩（车辆）')
            ax.set_ylabel('产品出荷（车辆）')
            ax.set_title(f'{machine}\n相关系数: {correlation:.3f}')
            ax.grid(True, alpha=0.3)

            # 添加趋势线
            if len(production) > 1 and max(production) > 0:
                try:
                    z = np.polyfit(production, shipment, 1)
                    p = np.poly1d(z)
                    ax.plot(production, p(production), "r--", alpha=0.8)
                except:
                    pass

    plt.suptitle('各机种生产实绩与产品出荷相关性分析', fontsize=16, fontweight='bold')
    plt.tight_layout()

    # 保存图片
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"相关性分析图已保存到: {output_path}")

    return correlations

def create_time_series_chart(production_data, shipment_data, dates, output_path):
    """创建时间序列对比图"""
    print("\n=== 创建时间序列对比图 ===")

    # 创建日期列表（前91天）
    date_list = []
    start_date = datetime(2025, 4, 1)
    for i in range(91):
        date_list.append(start_date + timedelta(days=i))

    # 创建子图
    machines = list(production_data.keys())
    fig, axes = plt.subplots(3, 2, figsize=(16, 18))
    axes = axes.flatten()

    for i, machine in enumerate(machines):
        if i < len(axes):
            ax = axes[i]

            # 获取数据（前91天）
            production = production_data[machine][:91]
            shipment = shipment_data.get(machine, [0] * 91)[:91]

            # 绘制时间序列
            ax.plot(date_list, production, label='生产实绩', color='blue', linewidth=2)
            ax.plot(date_list, shipment, label='产品出荷', color='red', linewidth=2)

            ax.set_title(f'{machine} 生产实绩 vs 产品出荷', fontsize=12, fontweight='bold')
            ax.set_xlabel('日期')
            ax.set_ylabel('数量（车辆）')
            ax.legend()
            ax.grid(True, alpha=0.3)

            # 设置x轴日期格式
            ax.tick_params(axis='x', rotation=45)

            # 每两周显示一个日期标签
            import matplotlib.dates as mdates
            ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))

    plt.suptitle('各机种生产实绩与产品出荷时间序列对比（2025年4-6月）', fontsize=16, fontweight='bold')
    plt.tight_layout()

    # 保存图片
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"时间序列对比图已保存到: {output_path}")

    return True

def main():
    """主函数"""
    print("开始分析产品出荷数据...")

    # 文件路径
    excel_path = "出荷统计表7.18（更新版).xlsx"

    # 读取数据
    df = read_excel_data(excel_path)
    if df is None:
        return

    # 分析数据结构
    dates, machine_info = analyze_shipment_data(df)

    # 提取出荷数据和生产数据
    shipment_data, production_data = extract_shipment_matrix(df, dates, machine_info)

    # 创建热力图
    heatmap_output = "产品出荷热力图.png"
    df_heatmap = create_heatmap(shipment_data, dates, heatmap_output)

    # 创建相关性分析图
    correlation_output = "生产实绩与出荷相关性分析.png"
    correlations = create_correlation_analysis(production_data, shipment_data, dates, correlation_output)

    # 创建时间序列对比图
    timeseries_output = "生产实绩与出荷时间序列对比.png"
    create_time_series_chart(production_data, shipment_data, dates, timeseries_output)

    # 输出统计结果
    print("\n=== 分析结果汇总 ===")
    print("\n各机种出荷量统计（4-6月）:")
    for machine, data in shipment_data.items():
        total_shipment = sum(data[:91])  # 前91天
        avg_daily = total_shipment / 91
        max_daily = max(data[:91])
        print(f"  {machine}: 总出荷 {total_shipment:.2f} 车辆, 日均 {avg_daily:.2f} 车辆, 最大日出荷 {max_daily:.2f} 车辆")

    print("\n各机种生产实绩统计（4-6月）:")
    for machine, data in production_data.items():
        total_production = sum(data[:91])  # 前91天
        avg_daily = total_production / 91
        max_daily = max(data[:91])
        print(f"  {machine}: 总生产 {total_production:.2f} 车辆, 日均 {avg_daily:.2f} 车辆, 最大日生产 {max_daily:.2f} 车辆")

    print("\n生产实绩与出荷量相关性:")
    for machine, corr in correlations.items():
        if corr > 0.7:
            level = "强正相关"
        elif corr > 0.3:
            level = "中等正相关"
        elif corr > -0.3:
            level = "弱相关"
        elif corr > -0.7:
            level = "中等负相关"
        else:
            level = "强负相关"
        print(f"  {machine}: {corr:.3f} ({level})")

    # 保存数据到JSON
    analysis_data = {
        'shipment_data': {k: v[:91] for k, v in shipment_data.items()},  # 只保存前91天
        'production_data': {k: v[:91] for k, v in production_data.items()},  # 只保存前91天
        'correlations': correlations,
        'date_range': '2025-04-01 to 2025-06-30'
    }

    with open('shipment_analysis_data.json', 'w', encoding='utf-8') as f:
        json.dump(analysis_data, f, ensure_ascii=False, indent=2)

    print("\n分析完成！生成的文件:")
    print(f"  - {heatmap_output}")
    print(f"  - {correlation_output}")
    print(f"  - {timeseries_output}")
    print("  - shipment_analysis_data.json")

if __name__ == "__main__":
    main()
