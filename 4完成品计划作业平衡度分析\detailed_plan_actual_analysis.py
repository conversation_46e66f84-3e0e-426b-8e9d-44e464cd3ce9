import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json

def detailed_plan_actual_analysis():
    try:
        # 读取Excel文件
        df = pd.read_excel('7月计划与实际比对/作业登记表ABC栋7月.xlsx', sheet_name=0)
        
        # 处理日期格式
        def parse_date(date_str):
            if pd.isna(date_str):
                return None
            try:
                if isinstance(date_str, datetime):
                    return date_str.date()
                date_str = str(date_str).strip()
                if '/' in date_str and len(date_str.split('/')) == 2:
                    month, day = date_str.split('/')
                    return datetime(2025, int(month), int(day)).date()
                elif '2025' in date_str:
                    return pd.to_datetime(date_str).date()
                return None
            except:
                return None
        
        # 处理时间格式
        def parse_time_to_minutes(time_str):
            if pd.isna(time_str):
                return None
            try:
                time_str = str(time_str).strip()
                if ':' in time_str:
                    hour, minute = time_str.split(':')
                    return int(hour) * 60 + int(minute)
                return None
            except:
                return None
        
        # 处理时长格式（转换为分钟）
        def parse_duration_to_minutes(duration_str):
            if pd.isna(duration_str):
                return None
            try:
                duration_str = str(duration_str).strip()
                if ':' in duration_str:
                    hour, minute = duration_str.split(':')
                    return int(hour) * 60 + int(minute)
                return None
            except:
                return None
        
        # 应用解析函数
        df['日期_解析'] = df['日期'].apply(parse_date)
        df['起始时间_分钟'] = df['起始时间'].apply(parse_time_to_minutes)
        df['截止时间_分钟'] = df['截止时间'].apply(parse_time_to_minutes)
        df['作业时长_分钟'] = df['作业时长'].apply(parse_duration_to_minutes)
        df['实际开始时间_分钟'] = df['实际开始时间'].apply(parse_time_to_minutes)
        df['实际结束时间_分钟'] = df['实际结束时间'].apply(parse_time_to_minutes)
        df['实际作业时长_分钟'] = df['实际作业时长'].apply(parse_duration_to_minutes)
        
        print("=== 计划与实际时间对比详细分析 ===")
        
        # 筛选有完整计划和实际时间的记录
        complete_records = df[
            df['起始时间_分钟'].notna() & 
            df['截止时间_分钟'].notna() & 
            df['实际开始时间_分钟'].notna() & 
            df['实际结束时间_分钟'].notna()
        ].copy()
        
        print(f"有完整时间记录的作业数: {len(complete_records)}")
        
        if len(complete_records) > 0:
            # 计算时间差异
            complete_records['开始时间差异_分钟'] = complete_records['实际开始时间_分钟'] - complete_records['起始时间_分钟']
            complete_records['结束时间差异_分钟'] = complete_records['实际结束时间_分钟'] - complete_records['截止时间_分钟']
            
            # 计算计划和实际作业时长
            complete_records['计划作业时长_分钟'] = complete_records['截止时间_分钟'] - complete_records['起始时间_分钟']
            complete_records['实际作业时长_计算_分钟'] = complete_records['实际结束时间_分钟'] - complete_records['实际开始时间_分钟']
            complete_records['作业时长差异_分钟'] = complete_records['实际作业时长_计算_分钟'] - complete_records['计划作业时长_分钟']
            
            print("\n=== 时间差异统计 ===")
            print("开始时间差异统计 (分钟):")
            print(complete_records['开始时间差异_分钟'].describe())
            
            print("\n结束时间差异统计 (分钟):")
            print(complete_records['结束时间差异_分钟'].describe())
            
            print("\n作业时长差异统计 (分钟):")
            print(complete_records['作业时长差异_分钟'].describe())
            
            # 准时性分析
            on_time_start = (complete_records['开始时间差异_分钟'].abs() <= 15).sum()
            on_time_end = (complete_records['结束时间差异_分钟'].abs() <= 15).sum()
            
            print(f"\n=== 准时性分析 ===")
            print(f"开始时间准时率 (±15分钟): {on_time_start/len(complete_records)*100:.1f}%")
            print(f"结束时间准时率 (±15分钟): {on_time_end/len(complete_records)*100:.1f}%")
            
            # 延迟分析
            delayed_start = (complete_records['开始时间差异_分钟'] > 15).sum()
            delayed_end = (complete_records['结束时间差异_分钟'] > 15).sum()
            early_start = (complete_records['开始时间差异_分钟'] < -15).sum()
            early_end = (complete_records['结束时间差异_分钟'] < -15).sum()
            
            print(f"开始延迟率 (>15分钟): {delayed_start/len(complete_records)*100:.1f}%")
            print(f"结束延迟率 (>15分钟): {delayed_end/len(complete_records)*100:.1f}%")
            print(f"开始提前率 (<-15分钟): {early_start/len(complete_records)*100:.1f}%")
            print(f"结束提前率 (<-15分钟): {early_end/len(complete_records)*100:.1f}%")
            
            # 效率分析
            overtime_work = (complete_records['作业时长差异_分钟'] > 30).sum()
            efficient_work = (complete_records['作业时长差异_分钟'] < -30).sum()
            
            print(f"\n=== 效率分析 ===")
            print(f"超时作业率 (>30分钟): {overtime_work/len(complete_records)*100:.1f}%")
            print(f"高效作业率 (<-30分钟): {efficient_work/len(complete_records)*100:.1f}%")
        
        # 按栋别分析时间差异
        print("\n=== 按栋别时间差异分析 ===")
        if len(complete_records) > 0:
            building_time_analysis = complete_records.groupby('栋别').agg({
                '开始时间差异_分钟': ['mean', 'std', 'count'],
                '结束时间差异_分钟': ['mean', 'std'],
                '作业时长差异_分钟': ['mean', 'std']
            }).round(2)
            
            print(building_time_analysis)
        
        # 异常和加班详细分析
        print("\n=== 异常和加班详细分析 ===")
        
        # 异常原因分类
        exception_df = df[df['异常原因'].notna()].copy()
        if len(exception_df) > 0:
            print("异常原因分类统计:")
            
            # 车辆相关异常
            vehicle_exceptions = exception_df[exception_df['异常原因'].str.contains('车辆|到厂|迟到', na=False)]
            print(f"车辆相关异常: {len(vehicle_exceptions)}次 ({len(vehicle_exceptions)/len(exception_df)*100:.1f}%)")
            
            # 设备相关异常
            equipment_exceptions = exception_df[exception_df['异常原因'].str.contains('自动仓库|设备', na=False)]
            print(f"设备相关异常: {len(equipment_exceptions)}次 ({len(equipment_exceptions)/len(exception_df)*100:.1f}%)")
            
            # 流程相关异常
            process_exceptions = exception_df[exception_df['异常原因'].str.contains('等待|库存|拣货|备货', na=False)]
            print(f"流程相关异常: {len(process_exceptions)}次 ({len(process_exceptions)/len(exception_df)*100:.1f}%)")
        
        # 加班分析
        overtime_df = df[df['加班原因'].notna()].copy()
        if len(overtime_df) > 0:
            print(f"\n加班作业详细分析:")
            print("加班原因统计:")
            print(overtime_df['加班原因'].value_counts())
            
            print("\n加班作业按栋别分布:")
            print(overtime_df['栋别'].value_counts())
        
        # 生成关键指标汇总
        summary_stats = {
            'total_records': len(df),
            'execution_rate': len(df[df['实际开始时间'].notna()]) / len(df) * 100,
            'exception_rate': len(df[df['异常原因'].notna()]) / len(df) * 100,
            'overtime_rate': len(df[df['加班原因'].notna()]) / len(df) * 100,
            'building_distribution': df['栋别'].value_counts().to_dict(),
            'work_type_distribution': df['作业类型'].value_counts().to_dict(),
            'daily_work_count': df.groupby('日期_解析').size().to_dict()
        }
        
        # 如果有完整时间记录，添加时间分析指标
        if len(complete_records) > 0:
            summary_stats.update({
                'on_time_start_rate': on_time_start / len(complete_records) * 100,
                'on_time_end_rate': on_time_end / len(complete_records) * 100,
                'delayed_start_rate': delayed_start / len(complete_records) * 100,
                'delayed_end_rate': delayed_end / len(complete_records) * 100,
                'avg_start_delay_minutes': complete_records['开始时间差异_分钟'].mean(),
                'avg_end_delay_minutes': complete_records['结束时间差异_分钟'].mean(),
                'avg_duration_diff_minutes': complete_records['作业时长差异_分钟'].mean()
            })
        
        # 保存汇总数据
        with open('7月计划实际对比_汇总指标.json', 'w', encoding='utf-8') as f:
            json.dump(summary_stats, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n=== 关键指标汇总 ===")
        print(f"总作业数: {summary_stats['total_records']}")
        print(f"实际执行率: {summary_stats['execution_rate']:.1f}%")
        print(f"异常率: {summary_stats['exception_rate']:.1f}%")
        print(f"加班率: {summary_stats['overtime_rate']:.1f}%")
        
        if 'on_time_start_rate' in summary_stats:
            print(f"开始准时率: {summary_stats['on_time_start_rate']:.1f}%")
            print(f"结束准时率: {summary_stats['on_time_end_rate']:.1f}%")
            print(f"平均开始延迟: {summary_stats['avg_start_delay_minutes']:.1f}分钟")
            print(f"平均结束延迟: {summary_stats['avg_end_delay_minutes']:.1f}分钟")
        
        print("\n汇总指标已保存到 '7月计划实际对比_汇总指标.json'")
        
        return df, complete_records
        
    except Exception as e:
        print(f'详细分析时出错: {e}')
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    df, complete_records = detailed_plan_actual_analysis()
