const emptyBoxAnalysisData = {
    "JT028": {
        "dates": ["08-01", "08-02", "08-03", "08-04", "08-05", "08-06", "08-07", "08-08", "08-09", "08-10", "08-11", "08-12", "08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19", "08-20", "08-21", "08-22", "08-23", "08-24", "08-25", "08-26", "08-27", "08-28", "08-29", "08-30", "08-31"],
        "plan": [1.6, 0.8, 0.8, 1.6, 1.6, 0.8, 0.8, 1.6, 1.6, 1.6, 1.6, 1.6, 1.6, 0.0, 0.0, 0.0, 0.0, 1.6, 1.6, 1.2, 1.2, 1.6, 0.8, 0.8, 1.6, 1.6, 1.2, 1.2, 0.0, 1.6, 1.6],
        "empty_box": [1.0, 0.0, 0.0, 1.0, 1.0, 0.0, 3.0, 3.0, 0.0, 0.0, 3.0, 3.0, 1.0, 2.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.75, 0.0, 1.0, 2.0, 3.0, 2.0, 1.0, 0.0, 0.0],
        "daily_diff": [-0.6000000000000001, -0.8, -0.8, -0.6000000000000001, -0.6000000000000001, -0.8, 2.2, 1.4, -1.6, -1.6, 1.4, 1.4, -0.6000000000000001, 2.0, 0.0, 0.0, 0.0, -1.6, -1.6, -0.19999999999999996, -0.19999999999999996, -0.6000000000000001, 0.95, -0.8, -0.6000000000000001, 0.3999999999999999, 1.8, 0.8, 1.0, -1.6, -1.6],
        "plan_total": 35.2,
        "empty_box_total": 31.75,
        "total_diff": -3.450000000000003,
        "working_days": 26,
        "match_rate": 0.0
    },
    "JT026": {
        "dates": ["08-01", "08-02", "08-03", "08-04", "08-05", "08-06", "08-07", "08-08", "08-09", "08-10", "08-11", "08-12", "08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19", "08-20", "08-21", "08-22", "08-23", "08-24", "08-25", "08-26", "08-27", "08-28", "08-29", "08-30", "08-31"],
        "plan": [0.0, 0.0, 0.0, 1.7, 1.9, 1.9, 1.925, 0.95, 1.9, 1.9, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.4, 1.4, 1.4, 1.4, 0.95, 1.9, 1.9, 1.4, 1.4, 1.4, 1.4, 0.0, 1.7, 1.9],
        "empty_box": [0.0, 0.0, 0.0, 2.0, 2.0, 2.0, 2.0, 2.0, 3.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.0, 1.0, 1.0, 2.0, 2.0, 2.0, 0.0, 2.0, 2.0, 2.0, 0.0, 0.0, 3.0, 0.0],
        "daily_diff": [0.0, 0.0, 0.0, 0.30000000000000004, 0.10000000000000009, 0.10000000000000009, 0.07499999999999996, 1.05, 1.1, -1.9, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.6000000000000001, -0.3999999999999999, -0.3999999999999999, 0.6000000000000001, 1.05, 0.10000000000000009, -1.9, 0.6000000000000001, 0.6000000000000001, 0.6000000000000001, -1.4, 0.0, 1.3, -1.9],
        "plan_total": 31.724999999999998,
        "empty_box_total": 32.0,
        "total_diff": 0.27500000000000213,
        "working_days": 20,
        "match_rate": 5.0
    },
    "JH011-SH": {
        "dates": ["08-01", "08-02", "08-03", "08-04", "08-05", "08-06", "08-07", "08-08", "08-09", "08-10", "08-11", "08-12", "08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19", "08-20", "08-21", "08-22", "08-23", "08-24", "08-25", "08-26", "08-27", "08-28", "08-29", "08-30", "08-31"],
        "plan": [0.0, 0.0, 0.0, 0.0, 0.525, 0.525, 0.525, 0.525, 0.0, 0.0, 0.525, 0.525, 0.525, 0.0, 0.0, 0.0, 0.0, 0.525, 0.525, 0.525, 0.525, 0.0, 0.0, 0.0, 0.525, 0.525, 0.333333333333333, 0.0, 0.0, 0.0, 0.0],
        "empty_box": [0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.75, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        "daily_diff": [0.0, 0.0, 0.0, 1.0, -0.525, 0.475, -0.525, -0.525, 0.0, 0.0, 0.475, 0.22499999999999998, -0.525, 0.0, 0.0, 0.0, 1.0, -0.525, 0.475, -0.525, -0.525, 0.0, 0.0, 1.0, -0.525, -0.525, -0.333333333333333, 0.0, 0.0, 0.0, 0.0],
        "plan_total": 7.158333333333333,
        "empty_box_total": 6.75,
        "total_diff": -0.4083333333333332,
        "working_days": 14,
        "match_rate": 0.0
    },
    "JH027-SB": {
        "dates": ["08-01", "08-02", "08-03", "08-04", "08-05", "08-06", "08-07", "08-08", "08-09", "08-10", "08-11", "08-12", "08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19", "08-20", "08-21", "08-22", "08-23", "08-24", "08-25", "08-26", "08-27", "08-28", "08-29", "08-30", "08-31"],
        "plan": [0.875, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.725, 0.925, 0.0, 0.425, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.425, 0.0, 0.95, 0.425, 0.0, 0.0],
        "empty_box": [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.25, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 0.0, 0.0, 0.0],
        "daily_diff": [0.125, -0.925, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, -0.925, 0.0, 0.0, 0.25, 0.0, 0.0, 2.0, -0.725, -0.925, 0.0, -0.425, 0.0, 0.0, 1.0, -0.925, 0.0, 0.0, 0.575, 0.0, 0.050000000000000044, -0.425, 0.0, 0.0],
        "plan_total": 7.525,
        "empty_box_total": 7.25,
        "total_diff": -0.27500000000000036,
        "working_days": 10,
        "match_rate": 7.142857142857142
    },
    "US001": {
        "dates": ["08-01", "08-02", "08-03", "08-04", "08-05", "08-06", "08-07", "08-08", "08-09", "08-10", "08-11", "08-12", "08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19", "08-20", "08-21", "08-22", "08-23", "08-24", "08-25", "08-26", "08-27", "08-28", "08-29", "08-30", "08-31"],
        "plan": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        "empty_box": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        "daily_diff": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        "plan_total": 0,
        "empty_box_total": 0,
        "total_diff": 0,
        "working_days": 0,
        "match_rate": 0
    },
    "UF009": {
        "dates": ["08-01", "08-02", "08-03", "08-04", "08-05", "08-06", "08-07", "08-08", "08-09", "08-10", "08-11", "08-12", "08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19", "08-20", "08-21", "08-22", "08-23", "08-24", "08-25", "08-26", "08-27", "08-28", "08-29", "08-30", "08-31"],
        "plan": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        "empty_box": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        "daily_diff": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        "plan_total": 0,
        "empty_box_total": 0,
        "total_diff": 0,
        "working_days": 0,
        "match_rate": 0
    },
    "JH027-SC": {
        "dates": ["08-01", "08-02", "08-03", "08-04", "08-05", "08-06", "08-07", "08-08", "08-09", "08-10", "08-11", "08-12", "08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19", "08-20", "08-21", "08-22", "08-23", "08-24", "08-25", "08-26", "08-27", "08-28", "08-29", "08-30", "08-31"],
        "plan": [0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.95, 0.95, 0.425, 0.95, 0.0, 0.075, 0.0, 1.9, 0.95, 0.95, 0.95, 1.9, 0.0, 0.925, 0.95, 0.0, 1.9, 0.0, 0.0, 1.85, 0.0],
        "empty_box": [0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 1.0, 0.5, 0.5, 0.0, 1.0, 0.0, 1.0, 1.0, 1.0, 2.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 0.0, 1.0, 2.0, 0.0],
        "daily_diff": [0.0, 1.0, -0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, -0.925, 0.050000000000000044, 0.050000000000000044, 0.07500000000000001, -0.44999999999999996, 0.0, 0.925, 0.0, -0.8999999999999999, 0.050000000000000044, 0.050000000000000044, 1.05, -0.8999999999999999, 1.0, -0.925, 0.050000000000000044, 1.0, -0.8999999999999999, 0.0, 1.0, 0.1499999999999999, 0.0],
        "plan_total": 17.474999999999998,
        "empty_box_total": 19.0,
        "total_diff": 1.5250000000000021,
        "working_days": 16,
        "match_rate": 28.57142857142857
    },
    "JH027-SD": {
        "dates": ["08-01", "08-02", "08-03", "08-04", "08-05", "08-06", "08-07", "08-08", "08-09", "08-10", "08-11", "08-12", "08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19", "08-20", "08-21", "08-22", "08-23", "08-24", "08-25", "08-26", "08-27", "08-28", "08-29", "08-30", "08-31"],
        "plan": [0.975, 0.0, 0.0, 1.9, 1.375, 1.375, 1.9, 1.9, 0.925, 0.925, 0.95, 0.425, 0.95, 0.95, 0.5, 0.5, 0.95, 0.0, 0.0, 0.0, 0.925, 0.0, 0.925, 0.925, 0.0, 0.425, 0.0, 0.95, 0.0, 0.0, 0.0],
        "empty_box": [1.0, 0.0, 2.0, 1.0, 2.0, 2.0, 1.0, 2.0, 0.0, 1.0, 1.0, 0.5, 0.5, 1.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        "daily_diff": [0.025000000000000022, 0.0, 2.0, -0.8999999999999999, 0.625, 0.625, -0.8999999999999999, 0.10000000000000009, -0.925, 0.07499999999999996, 0.050000000000000044, 0.07500000000000001, -0.44999999999999996, 0.050000000000000044, 0.5, -0.5, -0.95, 0.0, 1.0, 0.0, -0.925, 2.0, -0.925, -0.925, 0.0, 0.575, 0.0, -0.95, 0.0, 0.0, 0.0],
        "plan_total": 20.65,
        "empty_box_total": 20.0,
        "total_diff": -0.6499999999999986,
        "working_days": 20,
        "match_rate": 21.73913043478261
    },
};