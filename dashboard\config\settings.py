#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仪表板配置设置
"""

import os
from dataclasses import dataclass
from typing import Dict, List, Any

@dataclass
class DashboardConfig:
    """仪表板配置类"""
    
    # 应用基本配置
    APP_NAME: str = "作业平衡度分析仪表板"
    APP_VERSION: str = "1.0.0"
    APP_DESCRIPTION: str = "智能化仓库作业管理与优化系统"
    
    # 数据配置
    SUPPORTED_FILE_TYPES: List[str] = ['xlsx', 'xls']
    MAX_FILE_SIZE_MB: int = 50
    DEFAULT_NORMAL_WORK_HOURS: int = 8
    
    # 时间配置
    LUNCH_BREAK_START: str = "11:30"
    LUNCH_BREAK_END: str = "12:30"
    
    # 预警阈值配置
    ALERT_THRESHOLDS: Dict[str, float] = {
        'excessive_overtime': 10.0,      # 单日加班超过10小时
        'workload_imbalance': 2.0,       # 栋别间负荷比例超过2:1
        'efficiency_drop': 0.5,          # 效率低于0.5任务/小时
        'continuous_overtime': 3,        # 连续3天加班
        'data_quality_min': 0.8          # 数据质量最低要求80%
    }
    
    # 图表配置
    CHART_CONFIG: Dict[str, Any] = {
        'height': 400,
        'responsive': True,
        'displayModeBar': False,
        'toImageButtonOptions': {
            'format': 'png',
            'filename': 'chart',
            'height': 500,
            'width': 800,
            'scale': 2
        }
    }
    
    # 缓存配置
    CACHE_TTL_SECONDS: int = 3600  # 1小时
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 页面配置
    PAGE_CONFIG: Dict[str, Any] = {
        'page_title': APP_NAME,
        'page_icon': '📊',
        'layout': 'wide',
        'initial_sidebar_state': 'expanded'
    }
    
    # 建筑物配置
    VALID_BUILDINGS: List[str] = ['A', 'B', 'C']
    
    # 作业类型配置
    WORK_TYPES: List[str] = ['入库', '出库', '备货', '装车', '其他']
    
    @classmethod
    def get_env_config(cls) -> Dict[str, Any]:
        """获取环境变量配置"""
        return {
            'DEBUG': os.getenv('DEBUG', 'False').lower() == 'true',
            'PORT': int(os.getenv('PORT', 8501)),
            'HOST': os.getenv('HOST', 'localhost'),
            'DATA_PATH': os.getenv('DATA_PATH', './data'),
            'LOG_PATH': os.getenv('LOG_PATH', './logs')
        }
    
    @classmethod
    def validate_config(cls) -> bool:
        """验证配置有效性"""
        try:
            # 验证阈值配置
            for key, value in cls.ALERT_THRESHOLDS.items():
                if not isinstance(value, (int, float)) or value < 0:
                    raise ValueError(f"Invalid threshold value for {key}: {value}")
            
            # 验证建筑物配置
            if not cls.VALID_BUILDINGS or len(cls.VALID_BUILDINGS) == 0:
                raise ValueError("VALID_BUILDINGS cannot be empty")
            
            return True
            
        except Exception as e:
            print(f"Configuration validation failed: {e}")
            return False
