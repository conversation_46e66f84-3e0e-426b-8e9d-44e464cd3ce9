#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
8-9月数据分析脚本
"""

import pandas as pd
import numpy as np
import json
from datetime import datetime, <PERSON><PERSON><PERSON>

def analyze_august_september():
    # 读取刚才保存的数据
    with open('august_september_analysis_data.json', 'r', encoding='utf-8') as f:
        data = json.load(f)

    print('=== 8-9月详细分析 ===')

    # 分析每个机种的执行情况
    execution_analysis = {}
    empty_box_analysis = {}

    for machine, machine_data in data.items():
        plan = machine_data['生产计划']
        actual = machine_data['生产实绩']
        empty_box = machine_data['空箱纳入']
        shipment = machine_data['产品出荷']
        
        # 计算总量
        total_plan = sum(plan)
        total_actual = sum(actual)
        total_empty = sum(empty_box)
        total_shipment = sum(shipment)
        
        # 计算执行率
        execution_rate = (total_actual / total_plan * 100) if total_plan > 0 else 0
        
        # 计算每日匹配度
        daily_matches = 0
        good_matches = 0
        total_days = len(plan)
        working_days = sum(1 for p in plan if p > 0)
        
        daily_deviations = []
        for i in range(len(plan)):
            if plan[i] > 0:
                deviation = abs(actual[i] - plan[i]) / plan[i] if plan[i] > 0 else 0
                daily_deviations.append(deviation)
                if deviation <= 0.05:  # 5%以内算完美匹配
                    daily_matches += 1
                if deviation <= 0.15:  # 15%以内算良好匹配
                    good_matches += 1
        
        perfect_match_rate = (daily_matches / working_days * 100) if working_days > 0 else 0
        good_match_rate = (good_matches / working_days * 100) if working_days > 0 else 0
        avg_deviation = np.mean(daily_deviations) if daily_deviations else 0
        
        # 空箱分析
        empty_box_deviations = []
        empty_perfect_matches = 0
        empty_good_matches = 0
        
        for i in range(len(actual)):
            if actual[i] > 0:
                deviation = abs(empty_box[i] - actual[i])
                empty_box_deviations.append(deviation)
                if deviation <= 0.1:  # 0.1车辆以内算完美匹配
                    empty_perfect_matches += 1
                if deviation <= 0.5:  # 0.5车辆以内算良好匹配
                    empty_good_matches += 1
        
        actual_working_days = sum(1 for a in actual if a > 0)
        empty_perfect_rate = (empty_perfect_matches / actual_working_days * 100) if actual_working_days > 0 else 0
        empty_good_rate = (empty_good_matches / actual_working_days * 100) if actual_working_days > 0 else 0
        empty_avg_deviation = np.mean(empty_box_deviations) if empty_box_deviations else 0
        
        execution_analysis[machine] = {
            'total_plan': total_plan,
            'total_actual': total_actual,
            'total_empty': total_empty,
            'total_shipment': total_shipment,
            'execution_rate': execution_rate,
            'working_days': working_days,
            'perfect_match_rate': perfect_match_rate,
            'good_match_rate': good_match_rate,
            'avg_deviation': avg_deviation
        }
        
        empty_box_analysis[machine] = {
            'empty_perfect_rate': empty_perfect_rate,
            'empty_good_rate': empty_good_rate,
            'empty_avg_deviation': empty_avg_deviation,
            'empty_vs_actual_diff': total_empty - total_actual
        }

    # 输出分析结果
    print('\n=== 生产计划vs实际执行对比分析 ===')
    for machine, analysis in execution_analysis.items():
        if analysis['total_plan'] > 0:
            print(f'\n【{machine}】')
            print(f'  计划总量: {analysis["total_plan"]:.2f}车辆')
            print(f'  实绩总量: {analysis["total_actual"]:.2f}车辆')
            print(f'  执行率: {analysis["execution_rate"]:.1f}%')
            print(f'  工作天数: {analysis["working_days"]}天')
            print(f'  完美匹配率: {analysis["perfect_match_rate"]:.1f}%')
            print(f'  良好匹配率: {analysis["good_match_rate"]:.1f}%')
            print(f'  平均偏差: {analysis["avg_deviation"]:.3f}')

    print('\n=== 空箱纳入与库存分析 ===')
    for machine, analysis in empty_box_analysis.items():
        exec_data = execution_analysis[machine]
        if exec_data['total_plan'] > 0:
            print(f'\n【{machine}】')
            print(f'  空箱总量: {exec_data["total_empty"]:.2f}车辆')
            print(f'  实绩总量: {exec_data["total_actual"]:.2f}车辆')
            print(f'  差异: {analysis["empty_vs_actual_diff"]:.2f}车辆')
            print(f'  空箱完美匹配率: {analysis["empty_perfect_rate"]:.1f}%')
            print(f'  空箱良好匹配率: {analysis["empty_good_rate"]:.1f}%')
            print(f'  空箱平均偏差: {analysis["empty_avg_deviation"]:.2f}车辆')

    # 保存分析结果
    analysis_results = {
        'execution_analysis': execution_analysis,
        'empty_box_analysis': empty_box_analysis,
        'period': '2025年8-9月',
        'total_days': 61
    }

    with open('august_september_analysis_results.json', 'w', encoding='utf-8') as f:
        json.dump(analysis_results, f, ensure_ascii=False, indent=2)

    print('\n分析结果已保存到 august_september_analysis_results.json')
    return analysis_results

if __name__ == "__main__":
    analyze_august_september()
