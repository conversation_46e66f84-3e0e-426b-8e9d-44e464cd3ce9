import pandas as pd
import json
from datetime import datetime

# 重新读取Excel文件并生成正确的数据
file_path = r'计划vs实际\第一阶段\出荷统计表8.4.xlsx'
df = pd.read_excel(file_path, sheet_name='8月')

# 获取日期列
date_cols = [col for col in df.columns if isinstance(col, datetime)]
date_labels = [col.strftime('%m-%d') for col in date_cols]

# 机种列表
models = ['JT028', 'JT026', 'JH011-SH', 'JH027-SB', 'US001', 'UF009', 'JH027-SC', 'JH027-SD']

# 处理数据
analysis_data = {}

for i, model in enumerate(models):
    start_idx = i * 4
    
    if start_idx + 3 < len(df):
        plan_row = df.iloc[start_idx][date_cols].fillna(0).tolist()
        empty_box_row = df.iloc[start_idx + 1][date_cols].fillna(0).tolist()
        
        # 计算每日差异
        daily_diff = [empty_box_row[j] - plan_row[j] for j in range(len(plan_row))]
        
        # 计算统计指标
        plan_total = sum(plan_row)
        empty_box_total = sum(empty_box_row)
        total_diff = empty_box_total - plan_total
        
        # 计算有效数据天数
        working_days = sum(1 for x in plan_row if x > 0)
        
        # 计算匹配率
        active_days = 0
        perfect_match_days = 0
        for j in range(len(plan_row)):
            if plan_row[j] > 0 or empty_box_row[j] > 0:
                active_days += 1
                if abs(daily_diff[j]) <= 0.1:
                    perfect_match_days += 1
        
        match_rate = (perfect_match_days / active_days * 100) if active_days > 0 else 0
        
        analysis_data[model] = {
            'dates': date_labels,
            'plan': plan_row,
            'empty_box': empty_box_row,
            'daily_diff': daily_diff,
            'plan_total': plan_total,
            'empty_box_total': empty_box_total,
            'total_diff': total_diff,
            'working_days': working_days,
            'match_rate': match_rate
        }

# 生成JavaScript格式的数据
js_lines = ['const emptyBoxAnalysisData = {']

for model, data in analysis_data.items():
    js_lines.append(f'    "{model}": {{')
    js_lines.append(f'        "dates": {json.dumps(data["dates"])},')
    js_lines.append(f'        "plan": {json.dumps(data["plan"])},')
    js_lines.append(f'        "empty_box": {json.dumps(data["empty_box"])},')
    js_lines.append(f'        "daily_diff": {json.dumps(data["daily_diff"])},')
    js_lines.append(f'        "plan_total": {data["plan_total"]},')
    js_lines.append(f'        "empty_box_total": {data["empty_box_total"]},')
    js_lines.append(f'        "total_diff": {data["total_diff"]},')
    js_lines.append(f'        "working_days": {data["working_days"]},')
    js_lines.append(f'        "match_rate": {data["match_rate"]}')
    js_lines.append('    },')

js_lines.append('};')

# 保存到文件
with open('real_data.js', 'w', encoding='utf-8') as f:
    f.write('\n'.join(js_lines))

print('真实数据已生成并保存到 real_data.js')
print('数据概览:')
for model, data in analysis_data.items():
    print(f'{model}: 计划={data["plan_total"]:.1f}, 空箱={data["empty_box_total"]:.1f}, 差异={data["total_diff"]:.1f}, 工作天数={data["working_days"]}')
