<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>各机种空箱纳入vs生产计划每日对比分析报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #2c3e50;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        .header .subtitle {
            color: #7f8c8d;
            margin-top: 10px;
            font-size: 16px;
        }
        .summary-section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #ecf0f1;
            border-radius: 8px;
        }
        .summary-title {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }
        .summary-card {
            background-color: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #3498db;
        }
        .summary-card h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
            font-size: 16px;
        }
        .summary-stats {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-value {
            font-weight: bold;
            font-size: 16px;
            color: #2c3e50;
        }
        .stat-label {
            color: #7f8c8d;
            font-size: 12px;
        }
        .chart-section {
            margin-bottom: 40px;
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: center;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin-bottom: 20px;
        }
        .model-selector {
            text-align: center;
            margin-bottom: 20px;
        }
        .model-selector select {
            padding: 8px 15px;
            font-size: 16px;
            border: 2px solid #3498db;
            border-radius: 5px;
            background-color: white;
        }
        .analysis-text {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-top: 15px;
            font-size: 14px;
            line-height: 1.6;
        }
        .positive { color: #27ae60; }
        .negative { color: #e74c3c; }
        .neutral { color: #f39c12; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>各机种空箱纳入vs生产计划每日对比分析报告</h1>
            <div class="subtitle">基于出荷统计表8.4.xlsx数据 | 分析时间范围：2025年8月1日-31日</div>
        </div>

        <div class="summary-section">
            <div class="summary-title">📊 整体概览</div>
            <div class="summary-grid" id="summaryGrid">
                <!-- 动态生成概览卡片 -->
            </div>
        </div>

        <div class="chart-section">
            <div class="chart-title">📈 各机种空箱纳入vs生产计划每日对比图</div>
            <div class="model-selector">
                <label for="modelSelect">选择机种：</label>
                <select id="modelSelect" onchange="updateChart()">
                    <!-- 动态生成选项 -->
                </select>
            </div>
            <div class="chart-container">
                <canvas id="comparisonChart"></canvas>
            </div>
            <div id="analysisText" class="analysis-text">
                <!-- 动态生成分析文本 -->
            </div>
        </div>

        <div class="chart-section">
            <div class="chart-title">📊 每日差异分析图</div>
            <div class="chart-container">
                <canvas id="differenceChart"></canvas>
            </div>
        </div>
    </div>

    <script>
        // 修正后的真实数据来源：出荷统计表8.4.xlsx
        const emptyBoxAnalysisData = {
            "JT028": {
                "dates": ["08-01", "08-02", "08-03", "08-04", "08-05", "08-06", "08-07", "08-08", "08-09", "08-10", "08-11", "08-12", "08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19", "08-20", "08-21", "08-22", "08-23", "08-24", "08-25", "08-26", "08-27", "08-28", "08-29", "08-30", "08-31"],
                "plan": [1.6, 0.8, 0.8, 1.6, 1.6, 0.8, 0.8, 1.6, 1.6, 1.6, 1.6, 1.6, 1.6, 0.0, 0.0, 0.0, 0.0, 1.6, 1.6, 1.2, 1.2, 1.6, 0.8, 0.8, 1.6, 1.6, 1.2, 1.2, 0.0, 1.6, 1.6],
                "empty_box": [1.0, 0.0, 0.0, 1.0, 1.0, 0.0, 3.0, 3.0, 0.0, 0.0, 3.0, 3.0, 1.0, 2.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.75, 0.0, 1.0, 2.0, 3.0, 2.0, 1.0, 0.0, 0.0],
                "daily_diff": [-0.6, -0.8, -0.8, -0.6, -0.6, -0.8, 2.2, 1.4, -1.6, -1.6, 1.4, 1.4, -0.6, 2.0, 0.0, 0.0, 0.0, -1.6, -1.6, -0.2, -0.2, -0.6, 0.95, -0.8, -0.6, 0.4, 1.8, 0.8, 1.0, -1.6, -1.6],
                "plan_total": 35.2,
                "empty_box_total": 31.75,
                "total_diff": -3.45,
                "working_days": 26,
                "match_rate": 0.0
            },
            "JT026": {
                "dates": ["08-01", "08-02", "08-03", "08-04", "08-05", "08-06", "08-07", "08-08", "08-09", "08-10", "08-11", "08-12", "08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19", "08-20", "08-21", "08-22", "08-23", "08-24", "08-25", "08-26", "08-27", "08-28", "08-29", "08-30", "08-31"],
                "plan": [0.0, 0.0, 0.0, 1.7, 1.9, 1.9, 1.925, 0.95, 1.9, 1.9, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.4, 1.4, 1.4, 1.4, 0.95, 1.9, 1.9, 1.4, 1.4, 1.4, 1.4, 0.0, 1.7, 1.9],
                "empty_box": [0.0, 0.0, 0.0, 2.0, 2.0, 2.0, 2.0, 2.0, 3.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.0, 1.0, 1.0, 2.0, 2.0, 2.0, 0.0, 2.0, 2.0, 2.0, 0.0, 0.0, 3.0, 0.0],
                "daily_diff": [0.0, 0.0, 0.0, 0.3, 0.1, 0.1, 0.075, 1.05, 1.1, -1.9, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.6, -0.4, -0.4, 0.6, 1.05, 0.1, -1.9, 0.6, 0.6, 0.6, -1.4, 0.0, 1.3, -1.9],
                "plan_total": 31.72,
                "empty_box_total": 32.0,
                "total_diff": 0.28,
                "working_days": 20,
                "match_rate": 5.0
            },
            "JH011-SH": {
                "dates": ["08-01", "08-02", "08-03", "08-04", "08-05", "08-06", "08-07", "08-08", "08-09", "08-10", "08-11", "08-12", "08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19", "08-20", "08-21", "08-22", "08-23", "08-24", "08-25", "08-26", "08-27", "08-28", "08-29", "08-30", "08-31"],
                "plan": [0.0, 0.0, 0.0, 0.0, 0.525, 0.525, 0.525, 0.525, 0.0, 0.0, 0.525, 0.525, 0.525, 0.0, 0.0, 0.0, 0.0, 0.525, 0.525, 0.525, 0.525, 0.0, 0.0, 0.0, 0.525, 0.525, 0.333, 0.0, 0.0, 0.0, 0.0],
                "empty_box": [0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.75, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
                "daily_diff": [0.0, 0.0, 0.0, 1.0, -0.525, 0.475, -0.525, -0.525, 0.0, 0.0, 0.475, 0.225, -0.525, 0.0, 0.0, 0.0, 1.0, -0.525, 0.475, -0.525, -0.525, 0.0, 0.0, 1.0, -0.525, -0.525, -0.333, 0.0, 0.0, 0.0, 0.0],
                "plan_total": 7.16,
                "empty_box_total": 6.75,
                "total_diff": -0.41,
                "working_days": 14,
                "match_rate": 0.0
            },
            "JH027-SB": {
                "dates": ["08-01", "08-02", "08-03", "08-04", "08-05", "08-06", "08-07", "08-08", "08-09", "08-10", "08-11", "08-12", "08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19", "08-20", "08-21", "08-22", "08-23", "08-24", "08-25", "08-26", "08-27", "08-28", "08-29", "08-30", "08-31"],
                "plan": [0.875, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.725, 0.925, 0.0, 0.425, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.425, 0.0, 0.95, 0.425, 0.0, 0.0],
                "empty_box": [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.25, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 0.0, 0.0, 0.0],
                "daily_diff": [0.125, -0.925, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, -0.925, 0.0, 0.0, 0.25, 0.0, 0.0, 2.0, -0.725, -0.925, 0.0, -0.425, 0.0, 0.0, 1.0, -0.925, 0.0, 0.0, 0.575, 0.0, 0.05, -0.425, 0.0, 0.0],
                "plan_total": 7.53,
                "empty_box_total": 7.25,
                "total_diff": -0.28,
                "working_days": 10,
                "match_rate": 7.1
            },
            "JH027-SC": {
                "dates": ["08-01", "08-02", "08-03", "08-04", "08-05", "08-06", "08-07", "08-08", "08-09", "08-10", "08-11", "08-12", "08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19", "08-20", "08-21", "08-22", "08-23", "08-24", "08-25", "08-26", "08-27", "08-28", "08-29", "08-30", "08-31"],
                "plan": [0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.95, 0.95, 0.425, 0.95, 0.0, 0.075, 0.0, 1.9, 0.95, 0.95, 0.95, 1.9, 0.0, 0.925, 0.95, 0.0, 1.9, 0.0, 0.0, 1.85, 0.0],
                "empty_box": [0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 1.0, 0.5, 0.5, 0.0, 1.0, 0.0, 1.0, 1.0, 1.0, 2.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 0.0, 1.0, 2.0, 0.0],
                "daily_diff": [0.0, 1.0, -0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, -0.925, 0.05, 0.05, 0.075, -0.45, 0.0, 0.925, 0.0, -0.9, 0.05, 0.05, 1.05, -0.9, 1.0, -0.925, 0.05, 1.0, -0.9, 0.0, 1.0, 0.15, 0.0],
                "plan_total": 17.47,
                "empty_box_total": 19.0,
                "total_diff": 1.53,
                "working_days": 16,
                "match_rate": 28.6
            },
            "JH027-SD": {
                "dates": ["08-01", "08-02", "08-03", "08-04", "08-05", "08-06", "08-07", "08-08", "08-09", "08-10", "08-11", "08-12", "08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19", "08-20", "08-21", "08-22", "08-23", "08-24", "08-25", "08-26", "08-27", "08-28", "08-29", "08-30", "08-31"],
                "plan": [0.975, 0.0, 0.0, 1.9, 1.375, 1.375, 1.9, 1.9, 0.925, 0.925, 0.95, 0.425, 0.95, 0.95, 0.5, 0.5, 0.95, 0.0, 0.0, 0.0, 0.925, 0.0, 0.925, 0.925, 0.0, 0.425, 0.0, 0.95, 0.0, 0.0, 0.0],
                "empty_box": [1.0, 0.0, 2.0, 1.0, 2.0, 2.0, 1.0, 2.0, 0.0, 1.0, 1.0, 0.5, 0.5, 1.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0],
                "daily_diff": [0.025, 0.0, 2.0, -0.9, 0.625, 0.625, -0.9, 0.1, -0.925, 0.075, 0.05, 0.075, -0.45, 0.05, 0.5, -0.5, -0.95, 0.0, 1.0, 0.0, -0.925, 2.0, -0.925, -0.925, 0.0, 0.575, 0.0, -0.95, 0.0, 0.0, 0.0],
                "plan_total": 20.65,
                "empty_box_total": 20.0,
                "total_diff": -0.65,
                "working_days": 20,
                "match_rate": 21.7
            }
        };

        let comparisonChart = null;
        let differenceChart = null;

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            loadFullData();
            generateSummaryCards();
            populateModelSelector();
            initializeCharts();
        });

        // 加载完整数据
        async function loadFullData() {
            try {
                const response = await fetch('empty_box_analysis_data.json');
                if (response.ok) {
                    const fullData = await response.json();
                    Object.assign(emptyBoxAnalysisData, fullData);
                }
            } catch (error) {
                console.log('使用内置数据');
            }
        }

        // 生成概览卡片
        function generateSummaryCards() {
            const summaryGrid = document.getElementById('summaryGrid');
            summaryGrid.innerHTML = '';

            Object.keys(emptyBoxAnalysisData).forEach(model => {
                const data = emptyBoxAnalysisData[model];
                if (data.working_days === 0) return; // 跳过无数据的机种

                const card = document.createElement('div');
                card.className = 'summary-card';

                const diffClass = data.total_diff > 0 ? 'positive' : data.total_diff < 0 ? 'negative' : 'neutral';
                const matchClass = data.match_rate >= 80 ? 'positive' : data.match_rate >= 50 ? 'neutral' : 'negative';

                card.innerHTML = `
                    <h4>${model}</h4>
                    <div class="summary-stats">
                        <div class="stat-item">
                            <div class="stat-value">${data.plan_total.toFixed(1)}</div>
                            <div class="stat-label">计划总量</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${data.empty_box_total.toFixed(1)}</div>
                            <div class="stat-label">空箱总量</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value ${diffClass}">${data.total_diff > 0 ? '+' : ''}${data.total_diff.toFixed(1)}</div>
                            <div class="stat-label">总差异</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value ${matchClass}">${data.match_rate.toFixed(1)}%</div>
                            <div class="stat-label">匹配率</div>
                        </div>
                    </div>
                `;
                summaryGrid.appendChild(card);
            });
        }

        // 填充机种选择器
        function populateModelSelector() {
            const select = document.getElementById('modelSelect');
            select.innerHTML = '';

            Object.keys(emptyBoxAnalysisData).forEach(model => {
                const data = emptyBoxAnalysisData[model];
                if (data.working_days === 0) return; // 跳过无数据的机种

                const option = document.createElement('option');
                option.value = model;
                option.textContent = model;
                select.appendChild(option);
            });
        }

        // 初始化图表
        function initializeCharts() {
            updateChart();
            createDifferenceChart();
        }

        // 更新对比图表
        function updateChart() {
            const selectedModel = document.getElementById('modelSelect').value;
            if (!selectedModel || !emptyBoxAnalysisData[selectedModel]) return;

            const data = emptyBoxAnalysisData[selectedModel];
            const ctx = document.getElementById('comparisonChart').getContext('2d');

            if (comparisonChart) {
                comparisonChart.destroy();
            }

            comparisonChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.dates,
                    datasets: [{
                        label: '生产计划',
                        data: data.plan,
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        borderWidth: 2,
                        fill: false,
                        tension: 0.1
                    }, {
                        label: '空箱纳入',
                        data: data.empty_box,
                        borderColor: '#e74c3c',
                        backgroundColor: 'rgba(231, 76, 60, 0.1)',
                        borderWidth: 2,
                        fill: false,
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `${selectedModel} - 空箱纳入vs生产计划每日对比`,
                            font: { size: 16, weight: 'bold' }
                        },
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '数量'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '日期'
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });

            updateAnalysisText(selectedModel);
        }

        // 创建差异分析图
        function createDifferenceChart() {
            const ctx = document.getElementById('differenceChart').getContext('2d');

            const datasets = [];
            const colors = ['#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c', '#34495e', '#e67e22'];
            let colorIndex = 0;

            Object.keys(emptyBoxAnalysisData).forEach(model => {
                const data = emptyBoxAnalysisData[model];
                if (data.working_days === 0) return;

                datasets.push({
                    label: model,
                    data: data.daily_diff,
                    borderColor: colors[colorIndex % colors.length],
                    backgroundColor: colors[colorIndex % colors.length] + '20',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.1
                });
                colorIndex++;
            });

            if (differenceChart) {
                differenceChart.destroy();
            }

            differenceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: emptyBoxAnalysisData[Object.keys(emptyBoxAnalysisData)[0]].dates,
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '各机种每日差异对比（空箱纳入 - 生产计划）',
                            font: { size: 16, weight: 'bold' }
                        },
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            title: {
                                display: true,
                                text: '差异数量'
                            },
                            grid: {
                                color: function(context) {
                                    if (context.tick.value === 0) {
                                        return '#000000';
                                    }
                                    return '#e0e0e0';
                                }
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '日期'
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });
        }

        // 更新分析文本
        function updateAnalysisText(model) {
            const data = emptyBoxAnalysisData[model];
            const analysisDiv = document.getElementById('analysisText');

            let analysis = `<strong>${model} 分析结果：</strong><br>`;
            analysis += `• 工作天数：${data.working_days}天<br>`;
            analysis += `• 生产计划总量：${data.plan_total.toFixed(1)}<br>`;
            analysis += `• 空箱纳入总量：${data.empty_box_total.toFixed(1)}<br>`;
            analysis += `• 总差异：${data.total_diff > 0 ? '+' : ''}${data.total_diff.toFixed(1)}<br>`;
            analysis += `• 匹配率：${data.match_rate.toFixed(1)}%<br><br>`;

            if (data.total_diff > 0) {
                analysis += `<span class="positive">✓ 空箱纳入超出计划 ${data.total_diff.toFixed(1)} 单位，库存充足</span>`;
            } else if (data.total_diff < 0) {
                analysis += `<span class="negative">⚠ 空箱纳入不足计划 ${Math.abs(data.total_diff).toFixed(1)} 单位，需要关注</span>`;
            } else {
                analysis += `<span class="neutral">= 空箱纳入与计划完全匹配</span>`;
            }

            if (data.match_rate >= 80) {
                analysis += `<br><span class="positive">✓ 匹配率优秀，计划执行良好</span>`;
            } else if (data.match_rate >= 50) {
                analysis += `<br><span class="neutral">△ 匹配率一般，有改善空间</span>`;
            } else {
                analysis += `<br><span class="negative">⚠ 匹配率较低，需要优化计划执行</span>`;
            }

            analysisDiv.innerHTML = analysis;
        }
    </script>
</body>
</html>
