# 📊 作业平衡度分析仪表板

一个基于 Streamlit 构建的现代化仓库作业管理与优化系统，采用 Apple 风格设计，提供实时数据分析和智能预警功能。

## ✨ 主要功能

### 📈 数据概览
- 关键指标实时监控
- 多维度数据可视化
- 数据质量评估
- 智能洞察分析

### ⏰ 工时分析
- 每日工时趋势分析
- 加班模式识别
- 效率指标计算
- 高峰时段分析

### ⚖️ 负荷均衡
- 栋别间负荷对比
- 作业类型分布分析
- 均衡度评分
- 优化建议生成

### 🚨 预警监控
- 过度加班预警
- 负荷不均衡检测
- 效率异常监控
- 连续加班提醒

### 🔮 预测分析
- 工作负荷预测
- 趋势分析
- 场景模拟
- 决策支持

## 🚀 快速开始

### 环境要求
- Python 3.8+
- 推荐使用虚拟环境

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行应用
```bash
streamlit run app.py
```

### 访问应用
打开浏览器访问：http://localhost:8501

## 📁 项目结构

```
dashboard/
├── app.py                 # 主应用入口
├── config/               # 配置模块
│   ├── settings.py       # 应用配置
│   └── themes.py         # 主题样式
├── core/                 # 核心功能
│   ├── data_manager.py   # 数据管理
│   └── analyzers.py      # 分析引擎
├── components/           # UI组件
│   └── charts.py         # 图表组件
├── pages/                # 页面模块
│   └── overview.py       # 概览页面
├── requirements.txt      # 依赖包
└── README.md            # 说明文档
```

## 📊 数据格式要求

支持的Excel文件格式，需包含以下列：
- `日期`: 作业日期 (格式: "4/30" 或 datetime)
- `栋别`: 建筑物标识 (A, B, C)
- `作业类型`: 作业分类 (入库, 出库, 备货, 装车等)
- `起始时间`: 开始时间 (格式: "8:30")
- `截止时间`: 结束时间 (格式: "17:30")
- `作业内容`: 具体作业描述

## 🎨 设计特色

### Apple 风格设计
- 简洁现代的界面
- 卡片式布局
- 柔和的阴影和圆角
- 渐变色彩搭配
- 响应式设计

### 交互体验
- 实时数据更新
- 平滑动画过渡
- 直观的筛选控件
- 丰富的悬停提示

## ⚙️ 配置说明

### 预警阈值配置
在 `config/settings.py` 中可以调整预警阈值：
```python
ALERT_THRESHOLDS = {
    'excessive_overtime': 10.0,      # 单日加班超过10小时
    'workload_imbalance': 2.0,       # 栋别间负荷比例超过2:1
    'efficiency_drop': 0.5,          # 效率低于0.5任务/小时
    'continuous_overtime': 3,        # 连续3天加班
}
```

### 主题颜色配置
在 `config/themes.py` 中可以自定义颜色：
```python
COLORS = {
    'primary': '#007AFF',
    'secondary': '#5AC8FA',
    'success': '#34C759',
    'warning': '#FF9500',
    'danger': '#FF3B30',
    # ...
}
```

## 🔧 扩展开发

### 添加新页面
1. 在 `pages/` 目录下创建新的页面模块
2. 实现 `render_xxx_page()` 函数
3. 在 `app.py` 中导入并调用

### 添加新图表
1. 在 `components/charts.py` 中添加新的图表方法
2. 使用 Plotly 创建交互式图表
3. 应用统一的主题样式

### 添加新分析功能
1. 在 `core/analyzers.py` 中添加分析类
2. 实现具体的分析逻辑
3. 在页面中调用分析结果

## 📈 性能优化

- 使用 `@st.cache_data` 缓存数据处理结果
- 懒加载大型图表和数据集
- 分页显示大量数据
- 异步处理耗时操作

## 🐛 故障排除

### 常见问题
1. **数据加载失败**: 检查Excel文件格式和必要列是否存在
2. **图表显示异常**: 确认数据类型和格式正确
3. **性能问题**: 减少数据量或启用缓存

### 日志查看
应用运行时会在控制台输出详细日志，帮助定位问题。

## 📝 更新日志

### v1.0.0 (2025-01-18)
- 初始版本发布
- 实现基础数据分析功能
- Apple 风格界面设计
- 实时预警系统

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进这个项目！

## 📄 许可证

MIT License - 详见 LICENSE 文件
