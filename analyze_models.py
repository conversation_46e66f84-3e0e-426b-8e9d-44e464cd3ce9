import pandas as pd
import numpy as np

# 读取Excel文件
file_path = r'计划vs实际\第一阶段\作业计划表ABC栋20250801~20250830_自动排程.xlsx'
df = pd.read_excel(file_path, sheet_name=0)

# 过滤有效数据
valid_df = df[df['日期'].notna() & df['日期'].astype(str).str.contains(r'^\d+/\d+$', na=False)]

print('=== 机种相关作业分析 ===')

# 提取机种信息
def extract_model(content):
    if pd.isna(content):
        return 'Unknown'
    content_str = str(content)
    if 'JT028' in content_str:
        return 'JT028'
    elif 'JT026' in content_str:
        return 'JT026'
    elif 'JH027-SC' in content_str:
        return 'JH027-SC'
    elif 'JH027-SD' in content_str:
        return 'JH027-SD'
    elif 'JH027-SB' in content_str:
        return 'JH027-SB'
    elif 'JH011-SH' in content_str:
        return 'JH011-SH'
    elif 'US001' in content_str:
        return 'US001'
    elif 'UF009' in content_str:
        return 'UF009'
    else:
        return 'Other'

valid_df['机种'] = valid_df['作业内容'].apply(extract_model)

# 按机种统计作业次数
print('各机种作业次数统计:')
model_counts = valid_df['机种'].value_counts()
for model, count in model_counts.items():
    print(f'{model}: {count}次')
print()

# 按机种和作业类型统计
print('各机种的入库/出库作业分布:')
model_work_stats = valid_df.groupby(['机种', '作业类型']).size().unstack(fill_value=0)
print(model_work_stats)
print()

# 分析每个机种的作业内容类型
print('各机种的具体作业内容:')
for model in ['JT028', 'JT026', 'JH027-SC', 'JH027-SD', 'JH027-SB', 'JH011-SH', 'US001', 'UF009']:
    model_data = valid_df[valid_df['机种'] == model]
    if len(model_data) > 0:
        print(f'\n{model}:')
        content_types = model_data['作业内容'].value_counts()
        for content, count in content_types.items():
            print(f'  {content}: {count}次')

print('\n数据分析完成！')
