#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Apple风格主题配置
"""

import streamlit as st

class AppleTheme:
    """Apple风格主题管理器"""
    
    # 颜色配置
    COLORS = {
        'primary': '#007AFF',
        'secondary': '#5AC8FA', 
        'success': '#34C759',
        'warning': '#FF9500',
        'danger': '#FF3B30',
        'info': '#5856D6',
        'light': '#F2F2F7',
        'dark': '#1C1C1E',
        'white': '#FFFFFF',
        'gray_100': '#F2F2F7',
        'gray_200': '#E5E5EA',
        'gray_300': '#D1D1D6',
        'gray_400': '#C7C7CC',
        'gray_500': '#AEAEB2',
        'gray_600': '#8E8E93',
        'gray_700': '#636366',
        'gray_800': '#48484A',
        'gray_900': '#2C2C2E'
    }
    
    @classmethod
    def apply_theme(cls):
        """应用Apple风格主题"""
        st.markdown(f"""
            <style>
            /* 全局样式重置 */
            .main {{
                padding: 1rem 2rem;
                background-color: {cls.COLORS['light']};
            }}
            
            /* 主标题样式 */
            .main-header {{
                text-align: center;
                padding: 2rem 0;
                background: linear-gradient(135deg, {cls.COLORS['primary']}, {cls.COLORS['secondary']});
                border-radius: 16px;
                margin-bottom: 2rem;
                color: white;
                box-shadow: 0 8px 32px rgba(0, 122, 255, 0.3);
            }}
            
            .main-header h1 {{
                font-size: 2.5rem;
                font-weight: 700;
                margin: 0;
                text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }}
            
            .main-header p {{
                font-size: 1.1rem;
                margin: 0.5rem 0 0 0;
                opacity: 0.9;
            }}
            
            /* 侧边栏样式 */
            .sidebar-header {{
                text-align: center;
                padding: 1rem 0;
                background: linear-gradient(135deg, {cls.COLORS['gray_800']}, {cls.COLORS['gray_700']});
                border-radius: 12px;
                margin-bottom: 1rem;
                color: white;
            }}
            
            .sidebar-header h1 {{
                font-size: 1.5rem;
                margin: 0;
                font-weight: 600;
            }}
            
            .sidebar-header p {{
                font-size: 0.9rem;
                margin: 0.25rem 0 0 0;
                opacity: 0.8;
            }}
            
            /* 卡片样式 */
            .metric-card {{
                background: {cls.COLORS['white']};
                padding: 1.5rem;
                border-radius: 16px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.08);
                margin-bottom: 1rem;
                border: 1px solid {cls.COLORS['gray_200']};
                transition: transform 0.2s ease, box-shadow 0.2s ease;
            }}
            
            .metric-card:hover {{
                transform: translateY(-2px);
                box-shadow: 0 8px 30px rgba(0,0,0,0.12);
            }}
            
            .metric-value {{
                font-size: 2.5rem;
                font-weight: 700;
                color: {cls.COLORS['primary']};
                margin: 0;
                line-height: 1;
            }}
            
            .metric-label {{
                font-size: 0.9rem;
                color: {cls.COLORS['gray_600']};
                margin: 0.5rem 0 0 0;
                font-weight: 500;
            }}
            
            .metric-delta {{
                font-size: 0.8rem;
                font-weight: 600;
                margin-top: 0.25rem;
            }}
            
            .metric-delta.positive {{
                color: {cls.COLORS['success']};
            }}
            
            .metric-delta.negative {{
                color: {cls.COLORS['danger']};
            }}
            
            /* 图表容器样式 */
            .chart-container {{
                background: {cls.COLORS['white']};
                padding: 1.5rem;
                border-radius: 16px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.08);
                margin-bottom: 1.5rem;
                border: 1px solid {cls.COLORS['gray_200']};
            }}
            
            .chart-title {{
                font-size: 1.2rem;
                font-weight: 600;
                color: {cls.COLORS['gray_800']};
                margin-bottom: 1rem;
                padding-bottom: 0.5rem;
                border-bottom: 2px solid {cls.COLORS['gray_200']};
            }}
            
            /* 欢迎页面样式 */
            .welcome-container {{
                text-align: center;
                padding: 3rem 2rem;
                background: {cls.COLORS['white']};
                border-radius: 20px;
                box-shadow: 0 8px 40px rgba(0,0,0,0.1);
                margin: 2rem 0;
            }}
            
            .welcome-container h2 {{
                color: {cls.COLORS['gray_800']};
                font-size: 2rem;
                margin-bottom: 1rem;
                font-weight: 600;
            }}
            
            .welcome-container p {{
                color: {cls.COLORS['gray_600']};
                font-size: 1.1rem;
                margin-bottom: 2rem;
            }}
            
            .feature-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1.5rem;
                margin-top: 2rem;
            }}
            
            .feature-card {{
                background: linear-gradient(135deg, {cls.COLORS['gray_100']}, {cls.COLORS['white']});
                padding: 1.5rem;
                border-radius: 16px;
                border: 1px solid {cls.COLORS['gray_200']};
                transition: transform 0.2s ease;
            }}
            
            .feature-card:hover {{
                transform: translateY(-4px);
            }}
            
            .feature-card h3 {{
                color: {cls.COLORS['primary']};
                font-size: 1.1rem;
                margin-bottom: 0.5rem;
                font-weight: 600;
            }}
            
            .feature-card p {{
                color: {cls.COLORS['gray_600']};
                font-size: 0.9rem;
                margin: 0;
                line-height: 1.4;
            }}
            
            /* 按钮样式 */
            .stButton > button {{
                background: linear-gradient(135deg, {cls.COLORS['primary']}, {cls.COLORS['secondary']});
                color: white;
                border: none;
                border-radius: 12px;
                padding: 0.5rem 1.5rem;
                font-weight: 600;
                transition: all 0.2s ease;
                box-shadow: 0 4px 15px rgba(0, 122, 255, 0.3);
            }}
            
            .stButton > button:hover {{
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(0, 122, 255, 0.4);
            }}
            
            /* 选择框样式 */
            .stSelectbox > div > div {{
                background-color: {cls.COLORS['white']};
                border: 1px solid {cls.COLORS['gray_300']};
                border-radius: 12px;
            }}
            
            /* 文件上传器样式 */
            .stFileUploader > div {{
                background-color: {cls.COLORS['white']};
                border: 2px dashed {cls.COLORS['gray_300']};
                border-radius: 12px;
                padding: 1rem;
            }}
            
            /* 标签页样式 */
            .stTabs [data-baseweb="tab-list"] {{
                gap: 8px;
                background-color: {cls.COLORS['gray_100']};
                border-radius: 12px;
                padding: 4px;
            }}
            
            .stTabs [data-baseweb="tab"] {{
                background-color: transparent;
                border-radius: 8px;
                color: {cls.COLORS['gray_600']};
                font-weight: 500;
                padding: 8px 16px;
                transition: all 0.2s ease;
            }}
            
            .stTabs [aria-selected="true"] {{
                background-color: {cls.COLORS['white']};
                color: {cls.COLORS['primary']};
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }}
            
            /* 指标组件样式 */
            [data-testid="metric-container"] {{
                background: {cls.COLORS['white']};
                border: 1px solid {cls.COLORS['gray_200']};
                padding: 1rem;
                border-radius: 12px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            }}
            
            /* 页脚样式 */
            .footer {{
                text-align: center;
                padding: 1rem;
                color: {cls.COLORS['gray_500']};
                font-size: 0.9rem;
            }}
            
            /* 隐藏Streamlit默认元素 */
            #MainMenu {{visibility: hidden;}}
            footer {{visibility: hidden;}}
            header {{visibility: hidden;}}
            
            /* 响应式设计 */
            @media (max-width: 768px) {{
                .main {{
                    padding: 0.5rem 1rem;
                }}
                
                .main-header h1 {{
                    font-size: 2rem;
                }}
                
                .feature-grid {{
                    grid-template-columns: 1fr;
                }}
            }}
            </style>
        """, unsafe_allow_html=True)
    
    @classmethod
    def get_color_palette(cls):
        """获取图表颜色调色板"""
        return [
            cls.COLORS['primary'],
            cls.COLORS['secondary'],
            cls.COLORS['success'],
            cls.COLORS['warning'],
            cls.COLORS['danger'],
            cls.COLORS['info'],
            cls.COLORS['gray_600'],
            cls.COLORS['gray_400']
        ]
    
    @classmethod
    def get_chart_config(cls):
        """获取图表配置"""
        return {
            'displayModeBar': False,
            'responsive': True,
            'toImageButtonOptions': {
                'format': 'png',
                'filename': 'chart',
                'height': 500,
                'width': 800,
                'scale': 2
            }
        }
