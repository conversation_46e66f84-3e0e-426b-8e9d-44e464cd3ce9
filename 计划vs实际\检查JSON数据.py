import json

# 检查生成的JSON文件
with open('daily_trends_data.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# 检查JH027-SB和JH027-SC的数据
print('📊 检查JSON文件中的数据:')
print('JH027-SB 生产实绩总量:', sum(data['JH027-SB']['actual']))
print('JH027-SC 生产计划总量:', sum(data['JH027-SC']['plan']))

# 检查前几天的数据
print('\nJH027-SB 前10天实绩数据:')
for i in range(10):
    date = data['JH027-SB']['dates'][i]
    actual = data['JH027-SB']['actual'][i]
    print(f'  {date}: {actual}')

print('\nJH027-SC 前10天计划数据:')
for i in range(10):
    date = data['JH027-SC']['dates'][i]
    plan = data['JH027-SC']['plan'][i]
    print(f'  {date}: {plan}')

# 检查是否需要更新
print('\n🔍 数据对比:')
print('Excel vs JSON:')
print(f'JH027-SB 实绩: 15.58 vs {sum(data["JH027-SB"]["actual"]):.2f}')
print(f'JH027-SC 计划: 48.22 vs {sum(data["JH027-SC"]["plan"]):.2f}')

if abs(sum(data['JH027-SB']['actual']) - 15.58) > 0.01:
    print('❌ JH027-SB 实绩数据需要更新')
else:
    print('✅ JH027-SB 实绩数据正确')

if abs(sum(data['JH027-SC']['plan']) - 48.22) > 0.01:
    print('❌ JH027-SC 计划数据需要更新')
else:
    print('✅ JH027-SC 计划数据正确')
