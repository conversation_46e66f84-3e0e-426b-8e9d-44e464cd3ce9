<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统计表分析报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.5em;
            color: #2c3e50;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header .subtitle {
            font-size: 1.2em;
            color: #7f8c8d;
            margin-bottom: 20px;
        }
        
        .header .meta {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .meta-item {
            background: #ecf0f1;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 0.9em;
            color: #34495e;
        }
        
        .card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        
        .card h3 {
            color: #34495e;
            margin: 20px 0 15px 0;
            font-size: 1.3em;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            width: 100%;
            margin: 20px 0;
            background: #fff;
            border-radius: 8px;
            padding: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .chart-container canvas {
            max-width: 100% !important;
            max-height: 100% !important;
        }
        
        .insights {
            background: #f8f9fa;
            border-left: 5px solid #3498db;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 10px 10px 0;
        }
        
        .insights h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .insights ul {
            list-style: none;
            padding-left: 0;
        }
        
        .insights li {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }
        
        .insights li:before {
            content: "▶";
            color: #3498db;
            position: absolute;
            left: 0;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }
        
        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        
        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .meta {
                flex-direction: column;
                align-items: center;
            }
            
            .card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>统计表分析报告</h1>

        
            <div class="subtitle">基于出荷统计表的数据分析与洞察</div>

        
            <div class="meta">
                <div class="meta-item">📅 分析期间：2025年4月-6月</div>
                <div class="meta-item">🏭 机种数量：8个</div>
                <div class="meta-item">📊 作业记录：855条</div>
                <div class="meta-item">🔍 分析维度：4个</div>
            </div>
        </div>

        <!-- 执行摘要 -->
        <div class="card">
            <h2>📋 执行摘要</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">94.2%</div>
                    <div class="metric-label">平均计划执行率</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">35,880</div>
                    <div class="metric-label">总作业量（车辆）</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">460</div>
                    <div class="metric-label">日均作业量（车辆）</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">0.161</div>
                    <div class="metric-label">平均作业平衡度</div>
                </div>
            </div>
            
            <div class="insights">
                <h4>🎯 关键发现</h4>
                <ul>
                    <li><strong>JH027-SC机种表现突出</strong>：产能利用率达133.3%，超额完成生产计划</li>
                    <li><strong>JH027-SD机种需要关注</strong>：产能利用率仅64.9%，存在产能浪费</li>
                    <li><strong>空箱库存管理有待优化</strong>：JH027-SC机种空箱过剩20.6车辆</li>
                    <li><strong>B栋作业最平衡</strong>：变异系数0.135，作业分配相对均匀</li>
                </ul>
            </div>
        </div>

        <!-- 分析1：生产计划vs实际执行对比（按日维度） -->
        <div class="card">
            <h2>📊 生产计划vs实际执行对比分析（按日维度）</h2>
            <div class="chart-container">
                <canvas id="planVsActualChart"></canvas>
            </div>

            <h3>每日执行匹配度分析</h3>
            <div class="metrics-grid">
                <div class="metric-card" style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%);">
                    <div class="metric-value">JH027-SC</div>
                    <div class="metric-label">执行率最高（126.1%）</div>
                </div>
                <div class="metric-card" style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);">
                    <div class="metric-value">JT028</div>
                    <div class="metric-label">最稳定（87.0%完美匹配）</div>
                </div>
                <div class="metric-card" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);">
                    <div class="metric-value">JH027-SD</div>
                    <div class="metric-label">执行率最低（78.6%）</div>
                </div>
            </div>

            <h3>每日执行表现详细分析</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                <div class="alert alert-success">
                    <strong>✅ 执行优秀机种</strong><br>
                    • <strong>JT028</strong>：87.0%完美匹配，58.4%良好执行<br>
                    • <strong>JH027-SC</strong>：94.7%完美匹配，平均执行率126.1%<br>
                    • 执行稳定性好，偏差控制在合理范围
                </div>
                <div class="alert alert-warning">
                    <strong>⚠️ 需要改善机种</strong><br>
                    • <strong>JH027-SD</strong>：53.1%天数未完成，累计不足25.7车辆<br>
                    • <strong>JT026</strong>：34.9%天数未完成，6月执行率下降<br>
                    • 存在较大的日执行波动
                </div>
            </div>

            <h3>90天完整数据趋势图</h3>
            <div class="chart-container">
                <canvas id="dailyExecutionTrendChart"></canvas>
            </div>

            <div class="insights">
                <h4>📈 基于正确数据的深度分析洞察</h4>
                <ul>
                    <li><strong>执行模式差异</strong>：JT028执行稳定（104.9%平均执行率），表现优秀</li>
                    <li><strong>稳定性分化</strong>：JT028执行最稳定（76.6%完美匹配），生产控制良好</li>
                    <li><strong>累计差异显著</strong>：JT028累计超额4.1车辆，超额完成目标</li>
                    <li><strong>执行天数差异</strong>：JT028工作日77天，执行效率高</li>
                </ul>
            </div>

            <div class="alert alert-success">
                <strong>✅ 优秀表现：</strong>基于正确数据，JT028机种执行稳定，76.6%的天数完美匹配计划，累计超额4.1车辆，是生产计划执行的标杆机种。
            </div>
        </div>

        <!-- 分析2：产能利用率分析 -->
        <div class="card">
            <h2>⚡ 产能利用率分析</h2>
            <div class="chart-container">
                <canvas id="capacityChart"></canvas>
            </div>

            <h3>各机种产能利用率详情</h3>
            <div class="metrics-grid">
                <div class="metric-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                    <div class="metric-value">133.3%</div>
                    <div class="metric-label">JH027-SC（最高）</div>
                </div>
                <div class="metric-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <div class="metric-value">104.9%</div>
                    <div class="metric-label">JT028</div>
                </div>
                <div class="metric-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                    <div class="metric-value">64.9%</div>
                    <div class="metric-label">JH027-SD（最低）</div>
                </div>
            </div>

            <div class="insights">
                <h4>🔍 深度分析</h4>
                <ul>
                    <li><strong>高效机种</strong>：JH027-SC和JT028产能利用率超过100%，设备运行良好</li>
                    <li><strong>标准机种</strong>：JT026、JH011-SH、JH027-SB利用率在89-94%，属于正常范围</li>
                    <li><strong>待优化机种</strong>：JH027-SD利用率偏低，存在改善空间</li>
                </ul>
            </div>
        </div>

        <!-- 分析3：空箱纳入分析（按日维度） -->
        <div class="card">
            <h2>📦 空箱纳入与库存分析（按日维度）</h2>
            <div class="chart-container">
                <canvas id="emptyBoxChart"></canvas>
            </div>

            <h3>每日空箱纳入趋势分析</h3>
            <div class="chart-container">
                <canvas id="dailyEmptyBoxTrendChart"></canvas>
            </div>

            <h3>每日匹配度评估</h3>
            <div class="metrics-grid">
                <div class="metric-card" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);">
                    <div class="metric-value">JT026</div>
                    <div class="metric-label">波动性最高（1.01）</div>
                </div>
                <div class="metric-card" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);">
                    <div class="metric-value">JH027-SC</div>
                    <div class="metric-label">匹配度最好（63.2%）</div>
                </div>
                <div class="metric-card" style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%);">
                    <div class="metric-value">JH027-SD</div>
                    <div class="metric-label">稳定性最好（0.69）</div>
                </div>
            </div>

            <h3>每日匹配度详细分析</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                <div class="alert alert-success">
                    <strong>✅ 匹配度良好机种</strong><br>
                    • JH027-SC：完美匹配率63.2%<br>
                    • JH027-SD：完美匹配率60.9%<br>
                    • 平均绝对偏差均低于0.51车辆
                </div>
                <div class="alert alert-warning">
                    <strong>⚠️ 需要优化机种</strong><br>
                    • JT026：完美匹配率55.2%<br>
                    • JT028：完美匹配率仅18.2%<br>
                    • 存在较大波动性和连续偏差
                </div>
            </div>

            <h3>问题模式识别</h3>
            <div class="alert alert-info">
                <strong>📊 关键发现：</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>连续性问题</strong>：JT026和JH027-SD存在最长6天连续过剩</li>
                    <li><strong>波动性问题</strong>：JT026波动性最高（1.01），需要重点关注</li>
                    <li><strong>匹配度差异</strong>：JH027-SC表现最佳，JT026需要改善</li>
                </ul>
            </div>

            <h3>4-6月月度匹配度对比</h3>
            <div class="chart-container">
                <canvas id="monthlyMatchRateChart"></canvas>
            </div>

            <div class="alert alert-info">
                <strong>📊 90天完整数据分析发现：</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>JH027-SC表现最佳</strong>：5月匹配率达93.3%，6月保持86.7%</li>
                    <li><strong>JT026（基于正确数据）：完美匹配率55.2%，执行率92.8%</li>
                    <li><strong>JH027-SD先升后降</strong>：4月48.0%→5月80.0%→6月57.9%</li>
                    <li><strong>JT028不稳定</strong>：5月达到45.5%高峰，但4月和6月均低于8%</li>
                </ul>
            </div>

            <div class="insights">
                <h4>💡 基于90天数据的优化建议</h4>
                <ul>
                    <li><strong>建立日预测模型</strong>：基于90天历史数据建立更精准的需求预测</li>
                    <li><strong>设置动态缓冲</strong>：JT026设置±1.0车辆，其他机种±0.5车辆缓冲量</li>
                    <li><strong>月度调整策略</strong>：根据月度表现趋势提前调整下月配送计划</li>
                    <li><strong>重点监控JT026</strong>：建立每日预警，连续3天偏差>0.5立即干预</li>
                    <li><strong>复制成功经验</strong>：将JH027-SC的5-6月管理模式推广应用</li>
                    <li><strong>季节性调整</strong>：识别5月表现普遍较好的规律，分析成功因素</li>
                </ul>
            </div>
        </div>




        <!-- 总结与建议 -->
        <div class="card">
            <h2>📝 总结与改善建议</h2>

            <h3>🎯 主要发现</h3>
            <div class="alert alert-success">
                <strong>✅ 积极方面：</strong><br>
                • 整体计划执行率良好，平均达94.2%<br>
                • JH027-SC和JT028机种产能利用率优秀<br>
                • 月度作业负荷分布相对均衡<br>
                • B栋作业平衡度表现最佳
            </div>

            <div class="alert alert-warning">
                <strong>⚠️ 需要改善：</strong><br>
                • JH027-SD机种产能利用率偏低（64.9%）<br>
                • 空箱库存管理需要优化<br>
                • 日作业量波动较大（40-676车辆）<br>
                • A栋作业平衡度有待提升
            </div>

            <h3>💡 改善建议</h3>
            <div class="insights">
                <h4>短期改善措施（1-3个月）</h4>
                <ul>
                    <li><strong>优化JH027-SD产能</strong>：检查设备状态，调整人员配置，确保原料供应</li>
                    <li><strong>调整空箱配送</strong>：建立动态调配机制，减少过剩，补充不足</li>
                    <li><strong>平衡A栋作业</strong>：重新分配作业任务，提高作业均匀度</li>
                </ul>
            </div>

            <div class="insights">
                <h4>中长期优化策略（3-12个月）</h4>
                <ul>
                    <li><strong>建立预测模型</strong>：基于历史数据预测作业负荷，提前调配资源</li>
                    <li><strong>实施精益管理</strong>：减少浪费，提高整体运营效率</li>
                    <li><strong>数字化升级</strong>：引入智能调度系统，实现作业负荷自动平衡</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 调试信息
        console.log('开始加载图表...');
        console.log('Chart.js版本：', typeof Chart !== 'undefined' ? Chart.version : '未加载');

        // 图表配置
        Chart.defaults.font.family = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
        Chart.defaults.font.size = 12;
        Chart.defaults.color = '#2c3e50';

        // 1. 生产计划vs实际执行对比图表（总量对比）
        console.log('创建图表1：生产计划vs实际执行对比');
        const planVsActualCtx = document.getElementById('planVsActualChart').getContext('2d');
        const chart1 = new Chart(planVsActualCtx, {
            type: 'bar',
            data: {
                labels: ['JT028', 'JT026', 'JH011-SH', 'JH027-SB', 'JH027-SC', 'JH027-SD'],
                datasets: [{
                    label: '生产计划',
                    data: [84.53, 149.25, 29.55, 15.62, 57.48, 73.17],
                    backgroundColor: 'rgba(231, 76, 60, 0.8)',
                    borderColor: 'rgba(231, 76, 60, 1)',
                    borderWidth: 2
                }, {
                    label: '生产实绩',
                    data: [88.64, 138.47, 27.83, 13.95, 76.60, 47.50],
                    backgroundColor: 'rgba(52, 152, 219, 0.8)',
                    borderColor: 'rgba(52, 152, 219, 1)',
                    borderWidth: 2
                }, {
                    label: '每日完美匹配率',
                    data: [88.3, 59.8, 142.9, 280.0, 96.5, 85.9],
                    backgroundColor: 'rgba(46, 204, 113, 0.8)',
                    borderColor: 'rgba(46, 204, 113, 1)',
                    borderWidth: 2,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '各机种生产计划vs实际执行对比（含每日匹配度）',
                        font: { size: 16, weight: 'bold' }
                    },
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '数量（车辆）'
                        },
                        position: 'left'
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: '完美匹配率（%）'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                        max: 300
                    }
                }
            }
        });
        console.log('图表1创建成功');

        // 生成91天的日期标签（4-6月）
        function generateDateLabels() {
            const labels = [];
            const startDate = new Date(2024, 3, 1); // 2024年4月1日
            for (let i = 0; i < 91; i++) {
                const date = new Date(startDate);
                date.setDate(startDate.getDate() + i);
                labels.push((date.getMonth() + 1).toString().padStart(2, '0') + '/' +
                           date.getDate().toString().padStart(2, '0'));
            }
            return labels;
        }

        // 真实的日期标签
        const realDateLabels = ["2025-04-01", "2025-04-02", "2025-04-03", "2025-04-04", "2025-04-05", "2025-04-06", "2025-04-07", "2025-04-08", "2025-04-09", "2025-04-10", "2025-04-11", "2025-04-12", "2025-04-13", "2025-04-14", "2025-04-15", "2025-04-16", "2025-04-17", "2025-04-18", "2025-04-19", "2025-04-20", "2025-04-21", "2025-04-22", "2025-04-23", "2025-04-24", "2025-04-25", "2025-04-26", "2025-04-27", "2025-04-28", "2025-04-29", "2025-04-30", "2025-05-01", "2025-05-02", "2025-05-03", "2025-05-04", "2025-05-05", "2025-05-06", "2025-05-07", "2025-05-08", "2025-05-09", "2025-05-10", "2025-05-11", "2025-05-12", "2025-05-13", "2025-05-14", "2025-05-15", "2025-05-16", "2025-05-17", "2025-05-18", "2025-05-19", "2025-05-20", "2025-05-21", "2025-05-22", "2025-05-23", "2025-05-24", "2025-05-25", "2025-05-26", "2025-05-27", "2025-05-28", "2025-05-29", "2025-05-30", "2025-05-31", "2025-06-01", "2025-06-02", "2025-06-03", "2025-06-04", "2025-06-05", "2025-06-06", "2025-06-07", "2025-06-08", "2025-06-09", "2025-06-10", "2025-06-11", "2025-06-12", "2025-06-13", "2025-06-14", "2025-06-15", "2025-06-16", "2025-06-17", "2025-06-18", "2025-06-19", "2025-06-20", "2025-06-21", "2025-06-22", "2025-06-23", "2025-06-24", "2025-06-25", "2025-06-26", "2025-06-27", "2025-06-28", "2025-06-29", "2025-06-30"];

        // 1.2 每日执行趋势图表（以JT028为例）
        const dailyExecutionTrendCtx = document.getElementById('dailyExecutionTrendChart').getContext('2d');

        // 使用真实的JT028数据
        const jt028PlanDataForTrend = [0, 0, 0, 1.4333333333333333, 1.4333333333333333, 1.4333333333333333, 1.4333333333333333, 1.45, 0.7, 0.7166666666666667, 0.7166666666666667, 0, 0, 1.4333333333333333, 1.4333333333333333, 1.4333333333333333, 1.4333333333333333, 1.45, 0.7, 0.7166666666666667, 1.4333333333333333, 1.4333333333333333, 1.4333333333333333, 1.4333333333333333, 1.45, 0.7, 0.7166666666666667, 0.7166666666666667, 0.7166666666666667, 0, 0, 0, 0, 0, 0, 0, 1.4333333333333333, 1.4333333333333333, 1.4333333333333333, 0, 0, 1.4333333333333333, 1.45, 0.7, 0, 0.7166666666666667, 0, 0, 0.7166666666666667, 1.4333333333333333, 1.4333333333333333, 1.4333333333333333, 0.7166666666666667, 0, 0, 1.4333333333333333, 1.4333333333333333, 0, 1.4333333333333333, 0, 0, 0, 0, 0.7166666666666667, 1.4333333333333333, 0, 0, 1.4333333333333333, 1.4333333333333333, 1.4333333333333333, 0, 1.4333333333333333, 1.4333333333333333, 0.7166666666666667, 0, 0, 1.4333333333333333, 1.4333333333333333, 1.4333333333333333, 0.7166666666666667, 0, 0, 0, 0, 0, 1.4333333333333333, 1.4333333333333333, 1.4333333333333333, 0, 0, 0];
        const jt028ActualDataForTrend = [0, 0, 0, 1.3833333333333333, 1.3666666666666667, 1.4, 1.4, 1.45, 1.1666666666666667, 0.7666666666666667, 0.7666666666666667, 0, 0, 1.3833333333333333, 1.3666666666666667, 1.4, 1.4, 1.45, 1.1666666666666667, 0.7666666666666667, 1.3833333333333333, 1.3666666666666667, 1.4, 1.4, 1.45, 1.1666666666666667, 0.7666666666666667, 0.7666666666666667, 0.7666666666666667, 0, 0, 0, 0, 0, 0, 0.5833333333333334, 1.3833333333333333, 1.3666666666666667, 1.4, 0, 0, 1.3833333333333333, 1.45, 1.1666666666666667, 0, 0.7666666666666667, 0, 0, 0.7666666666666667, 1.3833333333333333, 1.3666666666666667, 1.4, 0.7666666666666667, 0, 0.5833333333333334, 1.3833333333333333, 1.3666666666666667, 1.4, 1.4, 0, 0, 0, 0, 0.7666666666666667, 1.3833333333333333, 1.3666666666666667, 1.4, 1.4, 1.3666666666666667, 1.4, 1.3833333333333333, 1.3666666666666667, 1.4, 0.7666666666666667, 0, 0, 1.3833333333333333, 1.3666666666666667, 1.4, 0.7666666666666667, 1.1666666666666667, 0, 0, 0.5833333333333334, 1.1666666666666667, 1.3833333333333333, 1.3666666666666667, 1.4, 0, 0, 0];
        const jt028DiffDataForTrend = [0, 0, 0, -0.050000000000000044, -0.06666666666666665, -0.03333333333333344, -0.03333333333333344, 0, 0.4666666666666668, 0.050000000000000044, 0.050000000000000044, 0, 0, -0.050000000000000044, -0.06666666666666665, -0.03333333333333344, -0.03333333333333344, 0, 0.4666666666666668, 0.050000000000000044, -0.050000000000000044, -0.06666666666666665, -0.03333333333333344, -0.03333333333333344, 0, 0.4666666666666668, 0.050000000000000044, 0.050000000000000044, 0.050000000000000044, 0, 0, 0, 0, 0, 0, 0.5833333333333334, -0.050000000000000044, -0.06666666666666665, -0.03333333333333344, 0, 0, -0.050000000000000044, 0, 0.4666666666666668, 0, 0.050000000000000044, 0, 0, 0.050000000000000044, -0.050000000000000044, -0.06666666666666665, -0.03333333333333344, 0.050000000000000044, 0, 0.5833333333333334, -0.050000000000000044, -0.06666666666666665, 1.4, -0.03333333333333344, 0, 0, 0, 0, 0.050000000000000044, -0.050000000000000044, -0.06666666666666665, 1.4, -0.03333333333333344, -0.06666666666666665, -0.03333333333333344, 1.3833333333333333, -0.06666666666666665, -0.03333333333333344, 0.050000000000000044, 0, 0, -0.050000000000000044, -0.06666666666666665, -0.03333333333333344, 0.050000000000000044, 1.1666666666666667, 0, 0, 0.5833333333333334, 1.1666666666666667, -0.050000000000000044, -0.06666666666666665, -0.03333333333333344, 0, 0, 0];

        new Chart(dailyExecutionTrendCtx, {
            type: 'line',
            data: {
                labels: realDateLabels,
                datasets: [{
                    label: 'JT028生产计划',
                    data: jt028PlanDataForTrend,
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    borderColor: 'rgba(231, 76, 60, 1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.1
                }, {
                    label: 'JT028生产实绩',
                    data: jt028ActualDataForTrend,
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    borderColor: 'rgba(52, 152, 219, 1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.1
                }, {
                    label: '每日执行差异',
                    data: jt028DiffDataForTrend,
                    backgroundColor: 'rgba(46, 204, 113, 0.1)',
                    borderColor: 'rgba(46, 204, 113, 1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.1,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'JT028 90天完整数据趋势图',
                        font: { size: 16, weight: 'bold' }
                    },
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: '日期（月-日）'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '数量（车辆）'
                        },
                        position: 'left'
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: '执行差异（车辆）'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });

        // 2. 产能利用率图表
        const capacityCtx = document.getElementById('capacityChart').getContext('2d');
        new Chart(capacityCtx, {
            type: 'doughnut',
            data: {
                labels: ['JT028', 'JT026', 'JH011-SH', 'JH027-SB', 'JH027-SC', 'JH027-SD'],
                datasets: [{
                    data: [104.9, 92.8, 94.2, 89.3, 133.3, 64.9],
                    backgroundColor: [
                        'rgba(46, 204, 113, 0.8)',
                        'rgba(52, 152, 219, 0.8)',
                        'rgba(155, 89, 182, 0.8)',
                        'rgba(241, 196, 15, 0.8)',
                        'rgba(26, 188, 156, 0.8)',
                        'rgba(231, 76, 60, 0.8)'
                    ],
                    borderColor: [
                        'rgba(46, 204, 113, 1)',
                        'rgba(52, 152, 219, 1)',
                        'rgba(155, 89, 182, 1)',
                        'rgba(241, 196, 15, 1)',
                        'rgba(26, 188, 156, 1)',
                        'rgba(231, 76, 60, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '各机种产能利用率分布（%）',
                        font: { size: 16, weight: 'bold' }
                    },
                    legend: {
                        position: 'right'
                    }
                }
            }
        });

        // 3. 空箱纳入分析图表（总量对比）
        const emptyBoxCtx = document.getElementById('emptyBoxChart').getContext('2d');
        new Chart(emptyBoxCtx, {
            type: 'bar',
            data: {
                labels: ['JT028', 'JT026', 'JH011-SH', 'JH027-SB', 'JH027-SC', 'JH027-SD'],
                datasets: [{
                    label: '生产计划',
                    data: [84.53, 149.25, 29.55, 15.62, 57.48, 73.17],
                    backgroundColor: 'rgba(52, 152, 219, 0.6)',
                    borderColor: 'rgba(52, 152, 219, 1)',
                    borderWidth: 2
                }, {
                    label: '空箱纳入',
                    data: [87.80, 140.00, 26.27, 15.72, 78.03, 47.50],
                    backgroundColor: 'rgba(241, 196, 15, 0.6)',
                    borderColor: 'rgba(241, 196, 15, 1)',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '空箱纳入vs生产计划总量对比（车辆）',
                        font: { size: 16, weight: 'bold' }
                    },
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '数量（车辆）'
                        }
                    }
                }
            }
        });

        // 3.2 每日空箱纳入趋势图表（4-6月完整数据）
        const dailyEmptyBoxTrendCtx = document.getElementById('dailyEmptyBoxTrendChart').getContext('2d');



        // JT026的91天真实数据（基于正确的Excel数据）
        const jt026PlanData = [0.0, 1.7, 1.875, 1.875, 1.875, 0.95, 1.875, 1.875, 1.875, 1.95, 0.95, 1.875, 1.875, 1.875, 1.875, 1.55, 1.95, 0.95, 1.875, 1.875, 1.875, 1.8, 1.875, 1.875, 0.95, 1.875, 1.875, 1.8, 1.875, 0.0, 0.0, 0.0, 1.7, 1.875, 1.875, 1.875, 1.875, 1.95, 0.95, 1.875, 1.875, 1.875, 1.25, 1.875, 1.95, 0.95, 1.875, 1.875, 1.7, 1.875, 1.875, 1.95, 0.95, 1.875, 1.875, 1.875, 1.875, 1.875, 1.625, 0.95, 1.875, 1.875, 1.875, 1.7, 1.875, 1.95, 0.95, 1.875, 1.875, 1.575, 1.875, 1.875, 1.95, 0.95, 1.875, 1.875, 1.875, 1.875, 1.875, 1.95, 0.95, 1.875, 1.875, 1.7, 1.875, 1.875, 1.95, 0.95, 1.875, 1.875, 0.95];

        const jt026EmptyData = [0.0, 0.0, 1.0, 2.0, 2.0, 2.0, 2.0, 3.0, 1.0, 1.0, 0.0, 1.0, 1.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 1.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 1.0, 2.0, 2.0, 3.0, 0.0, 0.0, 0.0, 0.0, 2.0, 2.0, 2.0, 2.0, 2.0, 3.0, 0.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 0.0, 2.0, 2.0, 2.0, 2.0, 2.0, 3.0, 0.0, 3.0, 2.0, 3.0, 3.0, 1.0, 0.0, 0.0, 0.0, 1.0, 2.0, 2.0, 2.0, 3.0, 0.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 0.0, 2.0, 2.0, 2.0, 2.0, 2.0, 3.0, 0.0, 2.0, 1.0, 0.0, 0.0, 0.0, 3.0, 0.0];

        const jt026ActualData = [0.0, 0.0, 1.9, 1.9, 1.9, 1.9, 1.9, 1.9, 1.9, 1.1, 0.0, 0.0, 1.625, 1.975, 1.575, 1.9, 1.9, 2.0, 1.75, 1.875, 1.85, 1.9, 1.9, 1.95, 1.9, 1.9, 1.9, 1.9, 1.525, 0.0, 0.0, 0.0, 1.8, 1.9, 1.9, 1.9, 1.9, 1.9, 1.1, 1.425, 1.9, 1.9, 1.9, 1.9, 1.9, 1.75, 1.9, 1.9, 1.9, 1.9, 1.9, 1.9, 1.1, 1.8, 1.9, 1.775, 1.9, 1.9, 1.775, 0.0, 0.0, 1.75, 1.9, 1.9, 1.9, 1.925, 1.175, 1.9, 1.9, 1.9, 1.9, 1.9, 1.925, 1.25, 1.9, 1.9, 1.7, 1.9, 1.9, 1.925, 1.225, 1.85, 1.9, 1.9, 0.6, 0.0, 0.0, 0.0, 0.0, 1.575, 0.625];

        const jt026DiffData = jt026PlanData.map((plan, i) => jt026ActualData[i] - plan);

        new Chart(dailyEmptyBoxTrendCtx, {
            type: 'line',
            data: {
                labels: realDateLabels,
                datasets: [{
                    label: 'JT026生产计划',
                    data: jt026PlanData,
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    borderColor: 'rgba(52, 152, 219, 1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.1
                }, {
                    label: 'JT026空箱纳入',
                    data: jt026EmptyData,
                    backgroundColor: 'rgba(241, 196, 15, 0.1)',
                    borderColor: 'rgba(241, 196, 15, 1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.1
                }, {
                    label: '每日差异',
                    data: jt026DiffData,
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    borderColor: 'rgba(231, 76, 60, 1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.1,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'JT026每日空箱纳入vs生产计划趋势（4-6月完整91天，基于正确Excel数据）',
                        font: { size: 16, weight: 'bold' }
                    },
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: '日期（月-日）'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '数量（车辆）'
                        },
                        position: 'left'
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: '差异（车辆）'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });

        // 3.3 月度匹配率对比图表
        const monthlyMatchRateCtx = document.getElementById('monthlyMatchRateChart').getContext('2d');
        new Chart(monthlyMatchRateCtx, {
            type: 'bar',
            data: {
                labels: ['4月', '5月', '6月'],
                datasets: [{
                    label: 'JT026完美匹配率',
                    data: [7.1, 20.7, 55.2],
                    backgroundColor: 'rgba(231, 76, 60, 0.8)',
                    borderColor: 'rgba(231, 76, 60, 1)',
                    borderWidth: 2
                }, {
                    label: 'JT028完美匹配率',
                    data: [7.7, 45.5, 6.9],
                    backgroundColor: 'rgba(52, 152, 219, 0.8)',
                    borderColor: 'rgba(52, 152, 219, 1)',
                    borderWidth: 2
                }, {
                    label: 'JH027-SC完美匹配率',
                    data: [33.3, 93.3, 86.7],
                    backgroundColor: 'rgba(46, 204, 113, 0.8)',
                    borderColor: 'rgba(46, 204, 113, 1)',
                    borderWidth: 2
                }, {
                    label: 'JH027-SD完美匹配率',
                    data: [48.0, 80.0, 57.9],
                    backgroundColor: 'rgba(241, 196, 15, 0.8)',
                    borderColor: 'rgba(241, 196, 15, 1)',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '各机种月度完美匹配率对比（基于90天完整数据）',
                        font: { size: 16, weight: 'bold' }
                    },
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: '完美匹配率（%）'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '月份'
                        }
                    }
                }
            }
        });



        // 添加交互效果
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px)';
                this.style.boxShadow = '0 15px 40px rgba(0,0,0,0.15)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 10px 30px rgba(0,0,0,0.1)';
            });
        });

        // 页面加载完成后的动画效果
        window.addEventListener('load', function() {
            document.querySelectorAll('.card').forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    card.style.transition = 'all 0.6s ease';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });
        });


    </script>
</body>
</html>
