<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仓库作业计划平衡性分析报告</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@4.0.0/dist/chart.min.css">
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2ecc71;
            --accent-color: #e74c3c;
            --dark-color: #2c3e50;
            --light-color: #ecf0f1;
            --warning-color: #f39c12;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            padding-top: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            color: white;
            padding: 2rem 0;
            border-radius: 10px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .card {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            border: none;
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-header {
            background-color: var(--primary-color);
            color: white;
            font-weight: bold;
            padding: 1rem;
        }
        
        .card-header.warning {
            background-color: var(--warning-color);
        }
        
        .card-header.success {
            background-color: var(--secondary-color);
        }
        
        .card-header.danger {
            background-color: var(--accent-color);
        }
        
        .stat-card {
            text-align: center;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 1.5rem;
            background-color: white;
        }
        
        .stat-card h3 {
            margin-bottom: 0.5rem;
            font-size: 1.2rem;
            color: var(--dark-color);
        }
        
        .stat-card .value {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .stat-card.warning .value {
            color: var(--warning-color);
        }
        
        .stat-card.success .value {
            color: var(--secondary-color);
        }
        
        .stat-card.danger .value {
            color: var(--accent-color);
        }
        
        .chart-container {
            position: relative;
            margin: auto;
            height: 400px;
            width: 100%;
        }
        
        .img-fluid {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .table {
            width: 100%;
            margin-bottom: 1rem;
            color: #212529;
            border-collapse: collapse;
        }
        
        .table th {
            background-color: var(--primary-color);
            color: white;
            position: sticky;
            top: 0;
        }
        
        .table-hover tbody tr:hover {
            background-color: rgba(52, 152, 219, 0.1);
        }
        
        .high-fluctuation {
            background-color: rgba(231, 76, 60, 0.2);
        }
        
        .conclusion {
            background-color: var(--light-color);
            padding: 2rem;
            border-radius: 10px;
            margin-top: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .conclusion h2 {
            color: var(--dark-color);
            margin-bottom: 1rem;
        }
        
        .nav-tabs {
            margin-bottom: 1rem;
        }
        
        .nav-tabs .nav-link {
            border-radius: 10px 10px 0 0;
            padding: 0.5rem 1rem;
            font-weight: 500;
        }
        
        .nav-tabs .nav-link.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .tab-content {
            padding: 1rem;
            background-color: white;
            border-radius: 0 0 10px 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        footer {
            background-color: var(--dark-color);
            color: white;
            padding: 2rem 0;
            margin-top: 3rem;
            border-radius: 10px;
        }
        
        @media (max-width: 768px) {
            .stat-card .value {
                font-size: 1.5rem;
            }
            
            .chart-container {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header text-center">
            <h1>仓库作业计划平衡性分析报告</h1>
            <p class="lead">基于2025年ABC栋+捷通仓库出入库操作记录的数据分析</p>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h2>分析概述</h2>
                    </div>
                    <div class="card-body">
                        <p>本报告对仓库出入库操作记录进行了全面分析，重点关注作业计划安排的平衡性，识别波动较大的日期，并评估计划分布的合理性。通过对作业数量、作业时长和作业次数的统计分析，我们可以清晰地了解仓库运营的效率和稳定性。</p>
                        <p>分析结果显示，仓库作业计划整体呈现一定的波动性，但大部分日期的作业量波动在合理范围内。我们识别出了5个波动较大的日期，这些日期的作业量与平均值的偏差超过了30%，需要特别关注。</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card">
                    <h3>平均每日作业数量</h3>
                    <div class="value" id="avgQuantity">483.57</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <h3>平均每日作业时长</h3>
                    <div class="value" id="avgDuration">26.17小时</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <h3>平均每日作业次数</h3>
                    <div class="value" id="avgOperations">11.76次</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card warning">
                    <h3>波动较大的日期数量</h3>
                    <div class="value" id="highFluctuationCount">5</div>
                </div>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="stat-card">
                    <h3>数量变异系数</h3>
                    <div class="value" id="cvQuantity">26.15%</div>
                    <small class="text-muted">变异系数越小，波动性越低</small>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <h3>作业时长变异系数</h3>
                    <div class="value" id="cvDuration">19.42%</div>
                    <small class="text-muted">变异系数越小，波动性越低</small>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <h3>作业次数变异系数</h3>
                    <div class="value" id="cvOperations">23.27%</div>
                    <small class="text-muted">变异系数越小，波动性越低</small>
                </div>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h2>每日作业量趋势分析</h2>
                    </div>
                    <div class="card-body">
                        <ul class="nav nav-tabs" id="trendTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="quantity-tab" data-bs-toggle="tab" data-bs-target="#quantity" type="button" role="tab" aria-controls="quantity" aria-selected="true">作业数量</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="duration-tab" data-bs-toggle="tab" data-bs-target="#duration" type="button" role="tab" aria-controls="duration" aria-selected="false">作业时长</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="operations-tab" data-bs-toggle="tab" data-bs-target="#operations" type="button" role="tab" aria-controls="operations" aria-selected="false">作业次数</button>
                            </li>
                        </ul>
                        <div class="tab-content" id="trendTabsContent">
                            <div class="tab-pane fade show active" id="quantity" role="tabpanel" aria-labelledby="quantity-tab">
                                <div class="chart-container">
                                    <img src="images/daily_quantity_trend.png" alt="每日作业数量趋势图" class="img-fluid">
                                </div>
                                <p class="mt-3">每日作业数量趋势图显示了各日期的作业数量变化情况，红色标记点表示波动较大的日期（偏差超过±30%）。整体来看，作业数量呈现一定的波动性，但大部分日期的波动在合理范围内。</p>
                            </div>
                            <div class="tab-pane fade" id="duration" role="tabpanel" aria-labelledby="duration-tab">
                                <div class="chart-container">
                                    <img src="images/daily_duration_trend.png" alt="每日作业时长趋势图" class="img-fluid">
                                </div>
                                <p class="mt-3">每日作业时长趋势图显示了各日期的作业时长变化情况，红色标记点表示波动较大的日期（偏差超过±30%）。作业时长的波动性相对较小，变异系数为19.42%，说明作业时长的分布相对稳定。</p>
                            </div>
                            <div class="tab-pane fade" id="operations" role="tabpanel" aria-labelledby="operations-tab">
                                <div class="chart-container">
                                    <img src="images/daily_operations_trend.png" alt="每日作业次数趋势图" class="img-fluid">
                                </div>
                                <p class="mt-3">每日作业次数趋势图显示了各日期的作业次数变化情况，红色标记点表示波动较大的日期（偏差超过±30%）。作业次数的变异系数为23.27%，处于中等波动水平。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header danger">
                        <h2>偏差分析</h2>
                    </div>
                    <div class="card-body">
                        <ul class="nav nav-tabs" id="deviationTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="quantity-deviation-tab" data-bs-toggle="tab" data-bs-target="#quantity-deviation" type="button" role="tab" aria-controls="quantity-deviation" aria-selected="true">数量偏差</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="duration-deviation-tab" data-bs-toggle="tab" data-bs-target="#duration-deviation" type="button" role="tab" aria-controls="duration-deviation" aria-selected="false">时长偏差</button>
                            </li>
                        </ul>
                        <div class="tab-content" id="deviationTabsContent">
                            <div class="tab-pane fade show active" id="quantity-deviation" role="tabpanel" aria-labelledby="quantity-deviation-tab">
                                <div class="chart-container">
                                    <img src="images/quantity_deviation_percentage.png" alt="每日作业数量偏差百分比分析" class="img-fluid">
                                </div>
                                <p class="mt-3">每日作业数量偏差百分比分析图显示了各日期作业数量与平均值的偏差百分比。红色虚线表示±30%的波动阈值，超出此范围的日期被视为波动较大，需要特别关注。</p>
                            </div>
                            <div class="tab-pane fade" id="duration-deviation" role="tabpanel" aria-labelledby="duration-deviation-tab">
                                <div class="chart-container">
                                    <img src="images/duration_deviation_percentage.png" alt="每日作业时长偏差百分比分析" class="img-fluid">
                                </div>
                                <p class="mt-3">每日作业时长偏差百分比分析图显示了各日期作业时长与平均值的偏差百分比。红色虚线表示±30%的波动阈值，超出此范围的日期被视为波动较大，需要特别关注。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header success">
                        <h2>栋别与作业类型分析</h2>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <img src="images/building_operation_type_distribution.png" alt="各栋别不同作业类型数量分布" class="img-fluid">
                        </div>
                        <p class="mt-3">各栋别不同作业类型数量分布图显示了A、B、C三个栋别的入库和出库作业数量分布情况。可以看出各栋别的作业类型分布存在差异，这可能与各栋别的功能定位和业务特点有关。</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header success">
                        <h2>方向分析</h2>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <img src="images/direction_distribution_pie.png" alt="各方向作业数量分布" class="img-fluid">
                        </div>
                        <p class="mt-3">各方向作业数量分布饼图显示了不同方向（如日本、捷通等）的作业数量占比情况。这有助于了解仓库业务的主要流向和重点合作伙伴。</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h2>波动性指标分析</h2>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <img src="images/fluctuation_radar.png" alt="作业计划波动性指标分析" class="img-fluid">
                        </div>
                        <p class="mt-3">作业计划波动性指标分析雷达图展示了数量、时长和次数三个维度的变异系数。数量变异系数最高（26.15%），说明作业数量的波动性相对较大；作业时长变异系数最低（19.42%），说明作业时长的分布相对稳定。</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h2>各栋别作业数量分布</h2>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <img src="images/building_quantity_boxplot.png" alt="各栋别作业数量分布箱线图" class="img-fluid">
                        </div>
                        <p class="mt-3">各栋别作业数量分布箱线图显示了A、B、C三个栋别的作业数量分布情况。通过箱线图可以直观地看出各栋别作业数量的中位数、四分位数范围和异常值，有助于比较不同栋别的作业负荷情况。</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h2>日期与栋别热力图</h2>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <img src="images/building_date_heatmap.png" alt="各日期各栋别作业数量热力图" class="img-fluid">
                        </div>
                        <p class="mt-3">各日期各栋别作业数量热力图直观地展示了不同日期不同栋别的作业数量分布情况。颜色越深表示作业数量越多，通过热力图可以快速识别作业量集中的时间和栋别，有助于优化资源分配和作业计划安排。</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="conclusion">
            <h2>分析结论与建议</h2>
            <div class="row">
                <div class="col-md-12">
                    <h4>1. 作业计划平衡性评估</h4>
                    <p>根据变异系数分析，仓库作业计划的整体波动性处于中等水平：</p>
                    <ul>
                        <li>数量变异系数为26.15%，表示作业数量的波动性相对较大</li>
                        <li>作业时长变异系数为19.42%，表示作业时长的分布相对稳定</li>
                        <li>作业次数变异系数为23.27%，处于中等波动水平</li>
                    </ul>
                    <p>一般而言，变异系数低于15%表示波动性较低，15%-30%表示中等波动，超过30%表示波动性较高。因此，当前作业计划的平衡性基本符合良好的计划分布情况，但仍有优化空间。</p>
                    
                    <h4>2. 波动较大日期分析</h4>
                    <p>分析识别出5个波动较大的日期，这些日期的作业量与平均值的偏差超过了30%。这些日期可能存在以下问题：</p>
                    <ul>
                        <li>作业计划安排不合理，导致某些日期作业量过大或过小</li>
                        <li>特殊业务需求或紧急订单导致的临时作业量增加</li>
                        <li>人力资源分配不均衡，影响作业效率</li>
                    </ul>
                    
                    <h4>3. 栋别与作业类型分析</h4>
                    <p>各栋别的作业类型分布存在差异，这可能与各栋别的功能定位和业务特点有关。建议根据各栋别的特点优化资源分配和作业计划安排。</p>
                    
                    <h4>4. 改进建议</h4>
                    <p>基于分析结果，提出以下改进建议：</p>
                    <ul>
                        <li><strong>优化作业计划安排</strong>：尽量避免作业量的大幅波动，合理分配各日期的作业任务</li>
                        <li><strong>加强预测与预警</strong>：建立作业量预测模型，提前识别可能出现的波动，及时调整作业计划</li>
                        <li><strong>资源弹性调配</strong>：根据作业量预测，灵活调配人力和设备资源，提高应对波动的能力</li>
                        <li><strong>优化栋别功能定位</strong>：根据各栋别的作业特点，优化功能定位和资源配置</li>
                        <li><strong>建立绩效评估体系</strong>：定期评估作业计划的平衡性，持续改进作业计划安排</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <footer class="text-center">
        <div class="container">
            <p>仓库作业计划平衡性分析报告 © 2025</p>
            <p>基于ABC栋+捷通2025年出入库操作记录数据</p>
        </div>
    </footer>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 从JSON文件加载分析结果数据
        fetch('analysis_results.json')
            .then(response => response.json())
            .then(data => {
                // 更新统计卡片数据
                document.getElementById('avgQuantity').textContent = data.avg_quantity.toFixed(2);
                document.getElementById('avgDuration').textContent = data.avg_duration.toFixed(2) + '小时';
                document.getElementById('avgOperations').textContent = data.avg_operations.toFixed(2) + '次';
                document.getElementById('highFluctuationCount').textContent = data.high_fluctuation_count;
                document.getElementById('cvQuantity').textContent = data.cv_quantity.toFixed(2) + '%';
                document.getElementById('cvDuration').textContent = data.cv_duration.toFixed(2) + '%';
                document.getElementById('cvOperations').textContent = data.cv_operations.toFixed(2) + '%';
            })
            .catch(error => console.error('Error loading analysis results:', error));
    </script>
</body>
</html>
