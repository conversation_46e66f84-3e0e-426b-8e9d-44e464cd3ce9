#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成演示数据
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta, time
import random

def generate_demo_data():
    """生成演示用的作业数据"""
    
    # 设置随机种子以确保可重现性
    np.random.seed(42)
    random.seed(42)
    
    # 基础配置
    start_date = datetime(2025, 4, 21)
    end_date = datetime(2025, 5, 17)
    buildings = ['A', 'B', 'C']
    work_types = ['入库', '出库', '备货', '装车']
    work_contents = [
        'JT026-24电池', 'JT026-24周转箱', 'JH027-SC待检品', 
        'JH027-SD待检品', 'JT028-电池组', 'JH029-成品',
        'JT030-配件', 'JH031-半成品', 'JT032-原材料'
    ]
    
    # 生成日期列表
    date_list = []
    current_date = start_date
    while current_date <= end_date:
        # 跳过周末（可选）
        if current_date.weekday() < 5:  # 0-4是周一到周五
            date_list.append(current_date)
        current_date += timedelta(days=1)
    
    data = []
    
    for date in date_list:
        # 每天生成8-16个任务
        daily_tasks = random.randint(8, 16)
        
        for _ in range(daily_tasks):
            # 随机选择栋别（B栋任务更多，模拟负荷不均）
            building_weights = [0.2, 0.6, 0.2]  # A:20%, B:60%, C:20%
            building = np.random.choice(buildings, p=building_weights)
            
            # 根据栋别调整工作类型分布
            if building == 'A':
                work_type_weights = [0.4, 0.3, 0.2, 0.1]  # 主要入库
            elif building == 'B':
                work_type_weights = [0.3, 0.4, 0.2, 0.1]  # 主要出库
            else:  # C栋
                work_type_weights = [0.3, 0.3, 0.3, 0.1]  # 相对均衡
            
            work_type = np.random.choice(work_types, p=work_type_weights)
            work_content = random.choice(work_contents)
            
            # 生成工作时间
            start_hour = random.randint(7, 15)  # 7点到15点开始
            start_minute = random.choice([0, 30])  # 整点或半点
            start_time = time(start_hour, start_minute)
            
            # 工作时长：1-4小时，B栋平均更长
            if building == 'B':
                duration_hours = random.uniform(1.5, 4.5)
            else:
                duration_hours = random.uniform(1.0, 3.5)
            
            # 计算结束时间
            start_datetime = datetime.combine(date, start_time)
            end_datetime = start_datetime + timedelta(hours=duration_hours)
            end_time = end_datetime.time()
            
            # 格式化日期为"4/30"格式
            date_str = f"{date.month}/{date.day}"
            
            data.append({
                '日期': date_str,
                '栋别': building,
                '作业类型': work_type,
                '起始时间': start_time.strftime('%H:%M'),
                '截止时间': end_time.strftime('%H:%M'),
                '作业内容': work_content,
                '备注': f'{building}栋{work_type}作业'
            })
    
    # 创建DataFrame
    df = pd.DataFrame(data)
    
    # 添加一些特殊情况来模拟真实场景
    
    # 1. 添加一些加班情况（B栋更多）
    overtime_indices = df[df['栋别'] == 'B'].sample(n=15).index
    for idx in overtime_indices:
        # 延长工作时间到晚上
        df.loc[idx, '截止时间'] = f"{random.randint(19, 22)}:00"
    
    # 2. 添加一些早班情况
    early_indices = df.sample(n=10).index
    for idx in early_indices:
        df.loc[idx, '起始时间'] = f"{random.randint(6, 7)}:30"
    
    # 3. 添加一些跨天作业
    overnight_indices = df[df['栋别'] == 'B'].sample(n=5).index
    for idx in overnight_indices:
        df.loc[idx, '起始时间'] = "22:00"
        df.loc[idx, '截止时间'] = "02:00"
    
    # 排序
    df = df.sort_values(['日期', '栋别', '起始时间']).reset_index(drop=True)
    
    return df

def save_demo_data():
    """保存演示数据到Excel文件"""
    df = generate_demo_data()
    
    # 保存到Excel文件
    filename = 'demo_workload_data.xlsx'
    df.to_excel(filename, index=False, sheet_name='作业登记表')
    
    print(f"✅ 演示数据已生成并保存到: {filename}")
    print(f"📊 数据统计:")
    print(f"   - 总记录数: {len(df)}")
    print(f"   - 日期范围: {df['日期'].min()} - {df['日期'].max()}")
    print(f"   - 栋别分布: {df['栋别'].value_counts().to_dict()}")
    print(f"   - 作业类型: {df['作业类型'].value_counts().to_dict()}")
    
    return filename

def create_sample_excel():
    """创建包含多个工作表的示例Excel文件"""
    
    # 生成主数据
    main_df = generate_demo_data()
    
    # 生成汇总数据
    summary_df = main_df.groupby(['日期', '栋别']).agg({
        '作业类型': 'count',
        '起始时间': 'min',
        '截止时间': 'max'
    }).rename(columns={'作业类型': '任务数量'}).reset_index()
    
    # 生成栋别统计
    building_stats = main_df.groupby('栋别').agg({
        '作业类型': 'count',
        '作业内容': 'nunique'
    }).rename(columns={
        '作业类型': '总任务数',
        '作业内容': '作业内容种类'
    }).reset_index()
    
    # 保存到多工作表Excel文件
    filename = 'complete_demo_data.xlsx'
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        main_df.to_excel(writer, sheet_name='作业登记表', index=False)
        summary_df.to_excel(writer, sheet_name='日度汇总', index=False)
        building_stats.to_excel(writer, sheet_name='栋别统计', index=False)
    
    print(f"✅ 完整演示数据已保存到: {filename}")
    print(f"📋 包含工作表:")
    print(f"   - 作业登记表: {len(main_df)} 条记录")
    print(f"   - 日度汇总: {len(summary_df)} 条记录")
    print(f"   - 栋别统计: {len(building_stats)} 条记录")
    
    return filename

if __name__ == "__main__":
    print("🎯 生成仪表板演示数据...")
    
    # 生成基础演示数据
    basic_file = save_demo_data()
    
    # 生成完整演示数据
    complete_file = create_sample_excel()
    
    print(f"\n📁 生成的文件:")
    print(f"   1. {basic_file} - 基础演示数据")
    print(f"   2. {complete_file} - 完整演示数据")
    
    print(f"\n🚀 使用方法:")
    print(f"   1. 启动仪表板: python run.py")
    print(f"   2. 在浏览器中打开: http://localhost:8501")
    print(f"   3. 上传 {basic_file} 文件")
    print(f"   4. 点击'加载数据'开始分析")
    
    print(f"\n💡 数据特点:")
    print(f"   - 模拟了21个工作日的数据")
    print(f"   - B栋负荷较重，有加班情况")
    print(f"   - 包含各种作业类型和时间段")
    print(f"   - 数据质量良好，适合演示")
