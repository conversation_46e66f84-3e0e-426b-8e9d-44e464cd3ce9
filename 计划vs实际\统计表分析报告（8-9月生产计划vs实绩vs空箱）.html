<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>8-9月统计表分析报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.5em;
            color: #2c3e50;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header .subtitle {
            font-size: 1.2em;
            color: #7f8c8d;
            margin-bottom: 20px;
        }
        
        .header .meta {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .meta-item {
            background: #ecf0f1;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 0.9em;
            color: #34495e;
        }
        
        .card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        
        .card h3 {
            color: #34495e;
            margin: 20px 0 15px 0;
            font-size: 1.3em;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            width: 100%;
            margin: 20px 0;
            background: #fff;
            border-radius: 8px;
            padding: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .insights {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #3498db;
        }
        
        .insights h4 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .insights ul {
            list-style-type: none;
            padding: 0;
        }
        
        .insights li {
            margin: 10px 0;
            padding-left: 20px;
            position: relative;
        }
        
        .insights li:before {
            content: "▶";
            color: #3498db;
            position: absolute;
            left: 0;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        
        .alert-danger {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        
        .comparison-table th {
            background: #3498db;
            color: white;
            font-weight: 600;
        }
        
        .comparison-table tr:hover {
            background: #f8f9fa;
        }
        
        .status-excellent { color: #27ae60; font-weight: bold; }
        .status-good { color: #f39c12; font-weight: bold; }
        .status-warning { color: #e74c3c; font-weight: bold; }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header .meta {
                flex-direction: column;
                align-items: center;
            }
            
            .card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>8-9月统计表分析报告</h1>
            <div class="subtitle">基于出荷统计表的8-9月数据分析与洞察</div>
            <div class="meta">
                <div class="meta-item">📅 分析期间：2025年8月-9月</div>
                <div class="meta-item">🏭 机种数量：6个主力机种</div>
                <div class="meta-item">📊 分析天数：61天</div>
                <div class="meta-item">🔍 分析维度：生产计划vs实绩、空箱库存</div>
            </div>
        </div>

        <!-- 执行摘要 -->
        <div class="card">
            <h2>📋 执行摘要</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">102.0%</div>
                    <div class="metric-label">平均计划执行率</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">258</div>
                    <div class="metric-label">总作业量（车辆）</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">4.2</div>
                    <div class="metric-label">日均作业量（车辆）</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">61</div>
                    <div class="metric-label">分析天数</div>
                </div>
            </div>
            
            <div class="insights">
                <h4>🎯 关键发现</h4>
                <ul>
                    <li><strong>JH027-SD机种表现突出</strong>：执行率达133.1%，超额完成生产计划</li>
                    <li><strong>JH027-SC机种需要关注</strong>：执行率仅58.6%，存在显著产能不足</li>
                    <li><strong>JT系列机种表现稳定</strong>：JT028和JT026执行率均达100%，完美匹配计划</li>
                    <li><strong>空箱库存管理差异明显</strong>：JT026空箱不足17.15车辆，需要优化</li>
                </ul>
            </div>
        </div>

        <!-- 分析1：生产计划vs实际执行对比（按日维度） -->
        <div class="card">
            <h2>📊 生产计划vs实际执行对比分析（按日维度）</h2>
            <div class="chart-container">
                <canvas id="planVsActualChart"></canvas>
            </div>

            <h3>总量对比分析</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>机种</th>
                        <th>计划总量</th>
                        <th>实绩总量</th>
                        <th>执行率</th>
                        <th>工作天数</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>JH027-SD</strong></td>
                        <td>35.15</td>
                        <td>46.80</td>
                        <td class="status-excellent">133.1%</td>
                        <td>34</td>
                        <td>✅ 超额完成</td>
                    </tr>
                    <tr>
                        <td><strong>JH027-SB</strong></td>
                        <td>13.10</td>
                        <td>15.57</td>
                        <td class="status-excellent">118.9%</td>
                        <td>14</td>
                        <td>✅ 超额完成</td>
                    </tr>
                    <tr>
                        <td><strong>JT028</strong></td>
                        <td>69.00</td>
                        <td>69.00</td>
                        <td class="status-excellent">100.0%</td>
                        <td>52</td>
                        <td>✅ 完美执行</td>
                    </tr>
                    <tr>
                        <td><strong>JT026</strong></td>
                        <td>86.15</td>
                        <td>86.15</td>
                        <td class="status-excellent">100.0%</td>
                        <td>48</td>
                        <td>✅ 完美执行</td>
                    </tr>
                    <tr>
                        <td><strong>JH011-SH</strong></td>
                        <td>6.49</td>
                        <td>6.49</td>
                        <td class="status-excellent">100.0%</td>
                        <td>13</td>
                        <td>✅ 完美执行</td>
                    </tr>
                    <tr>
                        <td><strong>JH027-SC</strong></td>
                        <td>48.23</td>
                        <td>28.25</td>
                        <td class="status-warning">58.6%</td>
                        <td>39</td>
                        <td>❌ 需改善</td>
                    </tr>
                </tbody>
            </table>

            <h3>每日执行匹配度分析</h3>
            <div class="metrics-grid">
                <div class="metric-card" style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%);">
                    <div class="metric-value">JT028</div>
                    <div class="metric-label">完美匹配率100%</div>
                </div>
                <div class="metric-card" style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);">
                    <div class="metric-value">JT026</div>
                    <div class="metric-label">完美匹配率100%</div>
                </div>
                <div class="metric-card" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);">
                    <div class="metric-value">JH027-SC</div>
                    <div class="metric-label">匹配率最低35.9%</div>
                </div>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                <div class="alert alert-success">
                    <strong>✅ 执行优秀机种</strong><br>
                    • <strong>JT028</strong>：100%完美匹配，执行率100%<br>
                    • <strong>JT026</strong>：100%完美匹配，执行率100%<br>
                    • <strong>JH011-SH</strong>：100%完美匹配，执行率100%<br>
                    • 执行稳定性极佳，偏差为零
                </div>
                <div class="alert alert-warning">
                    <strong>⚠️ 需要改善机种</strong><br>
                    • <strong>JH027-SC</strong>：仅35.9%完美匹配，执行率58.6%<br>
                    • <strong>JH027-SD</strong>：44.1%完美匹配，虽超额但不稳定<br>
                    • 存在较大的日执行波动和计划偏差
                </div>
            </div>

            <h3>每日执行趋势分析</h3>
            <div class="chart-container">
                <canvas id="dailyTrendChart"></canvas>
            </div>

            <h3>各机种每日详细趋势对比</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                <div class="chart-container" style="height: 300px;">
                    <canvas id="jt028TrendChart"></canvas>
                </div>
                <div class="chart-container" style="height: 300px;">
                    <canvas id="jt026TrendChart"></canvas>
                </div>
                <div class="chart-container" style="height: 300px;">
                    <canvas id="jh011shTrendChart"></canvas>
                </div>
                <div class="chart-container" style="height: 300px;">
                    <canvas id="jh027sbTrendChart"></canvas>
                </div>
                <div class="chart-container" style="height: 300px;">
                    <canvas id="jh027scTrendChart"></canvas>
                </div>
                <div class="chart-container" style="height: 300px;">
                    <canvas id="jh027sdTrendChart"></canvas>
                </div>
            </div>

            <div class="insights">
                <h4>📈 深度分析洞察</h4>
                <ul>
                    <li><strong>执行模式分化明显</strong>：JT系列执行完美，JH027系列差异巨大</li>
                    <li><strong>稳定性表现优异</strong>：JT028、JT026、JH011-SH三个机种实现零偏差</li>
                    <li><strong>产能利用差异</strong>：JH027-SC产能严重不足，JH027-SD产能过剩</li>
                    <li><strong>计划准确性</strong>：JT系列计划制定精准，执行控制到位</li>
                    <li><strong>日趋势特征</strong>：从每日趋势图可以看出工作日和周末的明显差异</li>
                </ul>
            </div>
        </div>

        <!-- 分析2：空箱纳入与库存分析（按日维度） -->
        <div class="card">
            <h2>📦 空箱纳入与库存分析（按日维度）</h2>
            <div class="chart-container">
                <canvas id="emptyBoxChart"></canvas>
            </div>

            <h3>总量对比分析</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>机种</th>
                        <th>空箱总量</th>
                        <th>实绩总量</th>
                        <th>差异</th>
                        <th>匹配状态</th>
                        <th>评级</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>JH011-SH</strong></td>
                        <td>6.49</td>
                        <td>6.49</td>
                        <td class="status-excellent">0.00</td>
                        <td>完美匹配</td>
                        <td>⭐⭐⭐⭐⭐</td>
                    </tr>
                    <tr>
                        <td><strong>JH027-SC</strong></td>
                        <td>28.25</td>
                        <td>28.25</td>
                        <td class="status-excellent">0.00</td>
                        <td>完美匹配</td>
                        <td>⭐⭐⭐⭐⭐</td>
                    </tr>
                    <tr>
                        <td><strong>JT028</strong></td>
                        <td>67.47</td>
                        <td>69.00</td>
                        <td class="status-good">-1.53</td>
                        <td>轻微不足</td>
                        <td>⭐⭐⭐⭐</td>
                    </tr>
                    <tr>
                        <td><strong>JH027-SD</strong></td>
                        <td>43.98</td>
                        <td>46.80</td>
                        <td class="status-good">-2.82</td>
                        <td>轻微不足</td>
                        <td>⭐⭐⭐⭐</td>
                    </tr>
                    <tr>
                        <td><strong>JH027-SB</strong></td>
                        <td>12.15</td>
                        <td>15.57</td>
                        <td class="status-warning">-3.42</td>
                        <td>明显不足</td>
                        <td>⭐⭐⭐</td>
                    </tr>
                    <tr>
                        <td><strong>JT026</strong></td>
                        <td>69.00</td>
                        <td>86.15</td>
                        <td class="status-warning">-17.15</td>
                        <td>严重不足</td>
                        <td>⭐⭐</td>
                    </tr>
                </tbody>
            </table>

            <h3>每日匹配度评估</h3>
            <div class="metrics-grid">
                <div class="metric-card" style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%);">
                    <div class="metric-value">JH011-SH</div>
                    <div class="metric-label">完美匹配率100.0%</div>
                </div>
                <div class="metric-card" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);">
                    <div class="metric-value">JT028</div>
                    <div class="metric-label">完美匹配率97.8%</div>
                </div>
                <div class="metric-card" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);">
                    <div class="metric-value">JT026</div>
                    <div class="metric-label">完美匹配率0%</div>
                </div>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                <div class="alert alert-success">
                    <strong>✅ 空箱管理优秀</strong><br>
                    • <strong>JH011-SH</strong>：总量完美匹配，匹配率100.0%<br>
                    • <strong>JH027-SC</strong>：总量完美匹配，匹配率100.0%<br>
                    • <strong>JT028</strong>：轻微不足1.53车辆，匹配率97.8%<br>
                    • <strong>JH027-SD</strong>：匹配率94.0%，管理良好<br>
                    • 空箱供应相对稳定
                </div>
                <div class="alert alert-warning">
                    <strong>⚠️ 空箱管理需改善</strong><br>
                    • <strong>JT026</strong>：严重不足17.15车辆，匹配率80.1%<br>
                    • <strong>JH027-SB</strong>：不足3.43车辆，匹配率78.0%<br>
                    • 需要优化空箱供应计划
                </div>
            </div>

            <h3>空箱供应波动性分析</h3>
            <div class="chart-container">
                <canvas id="emptyBoxVariationChart"></canvas>
            </div>

            <h3>各机种空箱纳入vs生产实际每日对比（每日差异）</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                <div class="chart-container" style="height: 300px;">
                    <canvas id="jt028EmptyBoxTrendChart"></canvas>
                </div>
                <div class="chart-container" style="height: 300px;">
                    <canvas id="jt026EmptyBoxTrendChart"></canvas>
                </div>
                <div class="chart-container" style="height: 300px;">
                    <canvas id="jh011shEmptyBoxTrendChart"></canvas>
                </div>
                <div class="chart-container" style="height: 300px;">
                    <canvas id="jh027sbEmptyBoxTrendChart"></canvas>
                </div>
                <div class="chart-container" style="height: 300px;">
                    <canvas id="jh027scEmptyBoxTrendChart"></canvas>
                </div>
                <div class="chart-container" style="height: 300px;">
                    <canvas id="jh027sdEmptyBoxTrendChart"></canvas>
                </div>
            </div>

            <div class="insights">
                <h4>📊 空箱管理深度分析</h4>
                <ul>
                    <li><strong>供应精准度差异</strong>：JH011-SH和JH027-SC实现完美总量匹配（100.0%）</li>
                    <li><strong>日常管理水平</strong>：JH011-SH和JH027-SC匹配率最高（100.0%），管理最优</li>
                    <li><strong>供应相对充足</strong>：JT028匹配率97.8%，JH027-SD匹配率94.0%，表现良好</li>
                    <li><strong>需要改善机种</strong>：JT026匹配率80.1%，JH027-SB匹配率78.0%，有改善空间</li>
                    <li><strong>每日波动特征</strong>：从每日对比图可以看出空箱供应与生产需求的时间差异</li>
                    <li><strong>整体管理水平</strong>：平均匹配率91.6%，整体表现良好</li>
                </ul>
            </div>

            <div class="alert alert-info">
                <strong>📈 改善建议：</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>优化JT026和JH027-SB</strong>：提升匹配率至90%以上</li>
                    <li><strong>学习最佳实践</strong>：推广JH011-SH和JH027-SC的完美匹配经验</li>
                    <li><strong>保持优秀水平</strong>：维持JT028和JH027-SD的高匹配率</li>
                    <li><strong>建立预警机制</strong>：设置匹配率低于85%的预警阈值</li>
                    <li><strong>动态调配</strong>：建立机种间空箱余缺调剂机制</li>
                </ul>
            </div>
        </div>

        <!-- 分析3：综合评估与改善建议 -->
        <div class="card">
            <h2>💡 综合评估与改善建议</h2>

            <h3>机种综合表现排名</h3>
            <div class="metrics-grid">
                <div class="metric-card" style="background: linear-gradient(135deg, #f1c40f 0%, #f39c12 100%);">
                    <div class="metric-value">🥇</div>
                    <div class="metric-label">JT028 & JT026<br>完美执行型</div>
                </div>
                <div class="metric-card" style="background: linear-gradient(135deg, #c0392b 0%, #e74c3c 100%);">
                    <div class="metric-value">🥈</div>
                    <div class="metric-label">JH011-SH<br>稳定优秀型</div>
                </div>
                <div class="metric-card" style="background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);">
                    <div class="metric-value">🥉</div>
                    <div class="metric-label">JH027-SB<br>超额完成型</div>
                </div>
            </div>

            <h3>短期改善措施（1个月内）</h3>
            <div class="alert alert-warning">
                <strong>🚨 紧急处理项目</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>JH027-SC产能提升</strong>：执行率仅58.6%，需立即分析瓶颈原因</li>
                    <li><strong>JT026空箱供应</strong>：不足17.15车辆，影响正常生产</li>
                    <li><strong>JH027-SD稳定性</strong>：虽超额但波动大，需优化计划制定</li>
                </ul>
            </div>

            <h3>中期优化策略（3个月内）</h3>
            <div class="alert alert-info">
                <strong>📈 系统性改善</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>标杆学习</strong>：推广JT028、JT026的完美执行经验</li>
                    <li><strong>预测模型</strong>：建立基于历史数据的需求预测系统</li>
                    <li><strong>动态调配</strong>：实现空箱库存的智能调度</li>
                    <li><strong>监控体系</strong>：建立实时监控和预警机制</li>
                </ul>
            </div>

            <h3>预期效果</h3>
            <div class="metrics-grid">
                <div class="metric-card" style="background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);">
                    <div class="metric-value">95%+</div>
                    <div class="metric-label">目标执行率</div>
                </div>
                <div class="metric-card" style="background: linear-gradient(135deg, #3498db 0%, #5dade2 100%);">
                    <div class="metric-value">80%+</div>
                    <div class="metric-label">空箱匹配率</div>
                </div>
                <div class="metric-card" style="background: linear-gradient(135deg, #e67e22 0%, #f39c12 100%);">
                    <div class="metric-value">±0.3</div>
                    <div class="metric-label">目标日偏差</div>
                </div>
            </div>
        </div>

        <!-- 数据来源说明 -->
        <div class="card">
            <h2>📊 数据来源与说明</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h4>📁 数据来源</h4>
                    <ul>
                        <li><strong>主要数据文件</strong>：出荷统计表7.18（更新版).xlsx</li>
                        <li><strong>分析期间</strong>：2025年8月1日 - 9月30日</li>
                        <li><strong>数据天数</strong>：61天完整数据</li>
                        <li><strong>机种范围</strong>：6个主力生产机种</li>
                    </ul>
                </div>
                <div>
                    <h4>🔍 分析方法</h4>
                    <ul>
                        <li><strong>完美匹配</strong>：日偏差≤5%</li>
                        <li><strong>良好匹配</strong>：日偏差≤15%</li>
                        <li><strong>空箱完美匹配</strong>：日偏差≤0.1车辆</li>
                        <li><strong>空箱良好匹配</strong>：日偏差≤0.5车辆</li>
                    </ul>
                </div>
            </div>

            <div class="alert alert-success" style="margin-top: 20px;">
                <strong>✅ 数据准确性保证</strong>：本报告基于Excel原始数据直接提取，确保数据准确性和分析可靠性。所有计算均基于实际工作日数据，排除了无生产计划的机种和日期。
            </div>
        </div>
    </div>

    <script>
        // 8-9月分析数据
        const analysisData = {
            machines: ['JT028', 'JT026', 'JH011-SH', 'JH027-SB', 'JH027-SC', 'JH027-SD'],
            planData: [69.00, 86.15, 6.49, 13.10, 48.23, 35.15],
            actualData: [69.00, 86.15, 6.49, 15.58, 28.25, 46.80],
            emptyBoxData: [67.47, 69.00, 6.49, 12.15, 28.25, 43.98],
            executionRates: [100.0, 100.0, 100.0, 118.9, 58.6, 133.1],
            emptyBoxMatchRates: [97.8, 80.1, 100.0, 78.0, 100.0, 94.0]
        };

        // 每日趋势数据（8-9月61天）
                        const dailyTrendsData = {
            "JT028": {
                "dates": ["08-01", "08-02", "08-03", "08-04", "08-05", "08-06", "08-07", "08-08", "08-09", "08-10", "08-11", "08-12", "08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19", "08-20", "08-21", "08-22", "08-23", "08-24", "08-25", "08-26", "08-27", "08-28", "08-29", "08-30", "08-31", "09-01", "09-02", "09-03", "09-04", "09-05", "09-06", "09-07", "09-08", "09-09", "09-10", "09-11", "09-12", "09-13", "09-14", "09-15", "09-16", "09-17", "09-18", "09-19", "09-20", "09-21", "09-22", "09-23", "09-24", "09-25", "09-26", "09-27", "09-28", "09-29", "09-30"],
                "plan": [1.53333333333333, 0.0, 0.0, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.53333333333333, 0.0, 0.0, 1.53333333333333, 1.53333333333333, 1.15, 0.383333333333333, 0.0, 0.0, 0.0, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 0.0, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.15, 0.766666666666667, 0.766666666666667, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.15, 0.766666666666667, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.53333333333333, 0.766666666666667, 1.53333333333333, 1.53333333333333, 0.0],
                "actual": [1.53333333333333, 0.0, 0.0, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.53333333333333, 0.0, 0.0, 1.53333333333333, 1.53333333333333, 1.15, 0.383333333333333, 0.0, 0.0, 0.0, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 0.0, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.15, 0.766666666666667, 0.766666666666667, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.15, 0.766666666666667, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.53333333333333, 0.766666666666667, 1.53333333333333, 1.53333333333333, 0.0],
                "empty_box": [0.0, 0.0, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.53333333333333, 0.0, 0.0, 1.53333333333333, 1.53333333333333, 1.15, 0.383333333333333, 0.0, 0.0, 0.0, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 0.0, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.15, 0.766666666666667, 0.766666666666667, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.15, 0.766666666666667, 1.53333333333333, 1.53333333333333, 1.53333333333333, 1.15, 1.15, 1.53333333333333, 0.766666666666667, 1.53333333333333, 1.53333333333333, 0.0, 0.0]
            },
            "JT026": {
                "dates": ["08-01", "08-02", "08-03", "08-04", "08-05", "08-06", "08-07", "08-08", "08-09", "08-10", "08-11", "08-12", "08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19", "08-20", "08-21", "08-22", "08-23", "08-24", "08-25", "08-26", "08-27", "08-28", "08-29", "08-30", "08-31", "09-01", "09-02", "09-03", "09-04", "09-05", "09-06", "09-07", "09-08", "09-09", "09-10", "09-11", "09-12", "09-13", "09-14", "09-15", "09-16", "09-17", "09-18", "09-19", "09-20", "09-21", "09-22", "09-23", "09-24", "09-25", "09-26", "09-27", "09-28", "09-29", "09-30"],
                "plan": [0.95, 0.0, 0.0, 1.7, 1.9, 1.9, 1.9, 0.0, 0.0, 0.0, 1.7, 1.9, 1.9, 0.0, 0.0, 0.0, 0.0, 1.7, 1.9, 1.9, 1.9, 0.0, 1.9, 1.9, 1.9, 1.9, 1.575, 1.575, 0.0, 1.9, 1.9, 1.9, 1.9, 1.9, 1.9, 0.0, 1.9, 1.9, 1.9, 1.9, 1.9, 1.9, 0.95, 1.9, 1.9, 1.9, 1.9, 1.9, 1.9, 0.95, 1.9, 1.9, 1.9, 1.9, 1.9, 1.9, 0.95, 1.9, 1.9, 1.9, 0.0],
                "actual": [0.95, 0.0, 0.0, 1.7, 1.9, 1.9, 1.9, 0.0, 0.0, 0.0, 1.7, 1.9, 1.9, 0.0, 0.0, 0.0, 0.0, 1.7, 1.9, 1.9, 1.9, 0.0, 1.9, 1.9, 1.9, 1.9, 1.575, 1.575, 0.0, 1.9, 1.9, 1.9, 1.9, 1.9, 1.9, 0.0, 1.9, 1.9, 1.9, 1.9, 1.9, 1.9, 0.95, 1.9, 1.9, 1.9, 1.9, 1.9, 1.9, 0.95, 1.9, 1.9, 1.9, 1.9, 1.9, 1.9, 0.95, 1.9, 1.9, 1.9, 0.0],
                "empty_box": [2.0, 0.0, 2.0, 3.0, 3.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.0, 0.0, 3.0, 2.0, 2.0, 0.0, 2.0, 2.0, 0.0, 2.0, 2.0, 0.0, 0.0, 2.0, 2.0, 0.0, 2.0, 2.0, 0.0, 0.0, 2.0, 3.0, 0.0, 2.0, 2.0, 0.0, 0.0, 3.0, 2.0, 0.0, 2.0, 2.0, 0.0, 0.0, 2.0, 2.0, 0.0, 2.0, 2.0, 0.0, 2.0, 2.0, 3.0, 0.0, 0.0, 0.0, 0.0]
            },
            "JH011-SH": {
                "dates": ["08-01", "08-02", "08-03", "08-04", "08-05", "08-06", "08-07", "08-08", "08-09", "08-10", "08-11", "08-12", "08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19", "08-20", "08-21", "08-22", "08-23", "08-24", "08-25", "08-26", "08-27", "08-28", "08-29", "08-30", "08-31", "09-01", "09-02", "09-03", "09-04", "09-05", "09-06", "09-07", "09-08", "09-09", "09-10", "09-11", "09-12", "09-13", "09-14", "09-15", "09-16", "09-17", "09-18", "09-19", "09-20", "09-21", "09-22", "09-23", "09-24", "09-25", "09-26", "09-27", "09-28", "09-29", "09-30"],
                "plan": [0.0, 0.0, 0.0, 0.0, 0.525, 0.525, 0.525, 0.525, 0.0, 0.0, 0.525, 0.525, 0.525, 0.0, 0.0, 0.0, 0.0, 0.525, 0.525, 0.525, 0.525, 0.0, 0.0, 0.0, 0.525, 0.189583333333333, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
                "actual": [0.0, 0.0, 0.0, 0.0, 0.525, 0.525, 0.525, 0.525, 0.0, 0.0, 0.525, 0.525, 0.525, 0.0, 0.0, 0.0, 0.0, 0.525, 0.525, 0.525, 0.525, 0.0, 0.0, 0.0, 0.525, 0.189583333333333, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
                "empty_box": [0.0, 0.0, 0.0, 0.525, 0.525, 0.525, 0.525, 0.0, 0.0, 0.525, 0.525, 0.525, 0.0, 0.0, 0.0, 0.0, 0.525, 0.525, 0.525, 0.525, 0.0, 0.0, 0.0, 0.525, 0.189583333333333, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
            },
            "JH027-SB": {
                "dates": ["08-01", "08-02", "08-03", "08-04", "08-05", "08-06", "08-07", "08-08", "08-09", "08-10", "08-11", "08-12", "08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19", "08-20", "08-21", "08-22", "08-23", "08-24", "08-25", "08-26", "08-27", "08-28", "08-29", "08-30", "08-31", "09-01", "09-02", "09-03", "09-04", "09-05", "09-06", "09-07", "09-08", "09-09", "09-10", "09-11", "09-12", "09-13", "09-14", "09-15", "09-16", "09-17", "09-18", "09-19", "09-20", "09-21", "09-22", "09-23", "09-24", "09-25", "09-26", "09-27", "09-28", "09-29", "09-30"],
                "plan": [0.95, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.95, 0.95, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.0, 0.0],
                "actual": [0.95, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.65, 0.925, 0.0, 0.45, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.95, 0.95, 0.925, 0.0, 0.0, 0.425, 0.0, 0.0, 0.0, 1.85, 0.925, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.0, 0.0],
                "empty_box": [0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.95, 0.95, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
            },
            "JH027-SC": {
                "dates": ["08-01", "08-02", "08-03", "08-04", "08-05", "08-06", "08-07", "08-08", "08-09", "08-10", "08-11", "08-12", "08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19", "08-20", "08-21", "08-22", "08-23", "08-24", "08-25", "08-26", "08-27", "08-28", "08-29", "08-30", "08-31", "09-01", "09-02", "09-03", "09-04", "09-05", "09-06", "09-07", "09-08", "09-09", "09-10", "09-11", "09-12", "09-13", "09-14", "09-15", "09-16", "09-17", "09-18", "09-19", "09-20", "09-21", "09-22", "09-23", "09-24", "09-25", "09-26", "09-27", "09-28", "09-29", "09-30"],
                "plan": [0.0, 0.925, 1.85, 0.95, 0.425, 0.95, 0.95, 0.0, 0.0, 1.85, 1.9, 0.0, 1.9, 0.95, 0.0, 0.0, 0.0, 0.95, 0.0, 0.95, 0.95, 0.95, 0.0, 0.0, 0.95, 0.425, 0.95, 0.0, 0.0, 0.925, 0.925, 0.95, 0.0, 0.95, 0.95, 0.95, 0.925, 0.925, 1.9, 0.0, 1.9, 0.0, 0.0, 0.0, 1.85, 1.9, 0.0, 1.9, 0.95, 0.95, 0.0, 1.85, 1.9, 0.25, 1.9, 0.95, 1.9, 1.85, 0.0, 0.0, 0.0],
                "actual": [0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.95, 0.0, 0.0, 0.0, 0.0, 0.925, 0.925, 0.95, 0.0, 0.95, 0.95, 0.95, 0.0, 0.0, 1.875, 0.0, 1.875, 0.95, 0.95, 0.0, 0.925, 1.9, 0.0, 0.95, 0.95, 0.95, 0.0, 0.925, 0.95, 0.0, 0.95, 0.0, 0.95, 0.925, 0.95, 0.0, 0.0],
                "empty_box": [0.0, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.95, 0.0, 0.0, 0.0, 0.0, 0.0, 0.925, 0.95, 0.0, 0.0, 0.0, 0.0, 0.925, 0.925, 0.95, 0.0, 0.95, 0.95, 0.95, 0.0, 0.0, 1.875, 0.0, 1.875, 0.95, 0.95, 0.0, 0.925, 1.9, 0.0, 0.95, 0.95, 0.95, 0.0, 0.925, 0.95, 0.0, 0.95, 0.0, 0.95, 0.925, 0.95, 0.0, 0.0, 0.0]
            },
            "JH027-SD": {
                "dates": ["08-01", "08-02", "08-03", "08-04", "08-05", "08-06", "08-07", "08-08", "08-09", "08-10", "08-11", "08-12", "08-13", "08-14", "08-15", "08-16", "08-17", "08-18", "08-19", "08-20", "08-21", "08-22", "08-23", "08-24", "08-25", "08-26", "08-27", "08-28", "08-29", "08-30", "08-31", "09-01", "09-02", "09-03", "09-04", "09-05", "09-06", "09-07", "09-08", "09-09", "09-10", "09-11", "09-12", "09-13", "09-14", "09-15", "09-16", "09-17", "09-18", "09-19", "09-20", "09-21", "09-22", "09-23", "09-24", "09-25", "09-26", "09-27", "09-28", "09-29", "09-30"],
                "plan": [0.95, 0.0, 0.0, 0.95, 0.425, 0.95, 0.95, 1.9, 0.925, 0.0, 0.0, 0.0, 0.0, 0.95, 1.85, 0.925, 0.925, 0.95, 0.0, 0.95, 0.95, 0.95, 0.925, 1.85, 0.95, 0.0, 0.95, 0.95, 0.0, 0.925, 0.925, 0.0, 0.0, 0.95, 0.95, 0.95, 0.925, 0.0, 0.0, 0.0, 0.0, 0.95, 0.95, 0.925, 0.0, 0.0, 0.0, 0.0, 0.95, 0.95, 0.925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.9, 0.85, 0.0],
                "actual": [0.975, 0.0, 0.0, 1.9, 0.85, 1.9, 1.9, 1.9, 0.0, 0.0, 1.9, 0.0, 1.9, 0.95, 0.925, 0.0, 0.0, 0.95, 0.0, 1.9, 1.9, 1.9, 0.0, 0.0, 0.95, 0.425, 1.9, 0.95, 0.0, 0.925, 0.925, 0.0, 0.425, 0.95, 0.95, 0.95, 1.85, 0.925, 0.0, 0.85, 0.0, 0.0, 0.0, 0.925, 0.925, 0.0, 0.425, 0.95, 0.95, 0.95, 0.0, 0.0, 0.95, 0.425, 0.95, 0.95, 0.95, 0.925, 0.95, 0.85, 0.0],
                "empty_box": [0.0, 0.0, 1.9, 0.85, 1.9, 1.9, 1.9, 0.0, 0.0, 1.9, 0.0, 1.9, 0.95, 0.925, 0.0, 0.0, 0.95, 0.0, 1.9, 1.9, 1.9, 0.0, 0.0, 0.95, 0.425, 1.9, 0.95, 0.0, 0.925, 0.925, 0.0, 0.425, 0.95, 0.95, 0.95, 0.0, 0.925, 0.0, 0.85, 0.0, 0.0, 0.0, 0.925, 0.925, 0.0, 0.425, 0.95, 0.95, 0.95, 0.0, 0.0, 0.95, 0.425, 0.95, 0.95, 0.95, 0.925, 0.95, 0.85, 0.0, 0.0]
            }
        };

        // 生产计划vs实际执行对比图
        const planVsActualCtx = document.getElementById('planVsActualChart').getContext('2d');
        new Chart(planVsActualCtx, {
            type: 'bar',
            data: {
                labels: analysisData.machines,
                datasets: [{
                    label: '生产计划',
                    data: analysisData.planData,
                    backgroundColor: 'rgba(52, 152, 219, 0.8)',
                    borderColor: 'rgba(52, 152, 219, 1)',
                    borderWidth: 2
                }, {
                    label: '生产实绩',
                    data: analysisData.actualData,
                    backgroundColor: 'rgba(231, 76, 60, 0.8)',
                    borderColor: 'rgba(231, 76, 60, 1)',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '8-9月生产计划vs实际执行对比',
                        font: { size: 16, weight: 'bold' }
                    },
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '数量（车辆）'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '机种'
                        }
                    }
                }
            }
        });

        // 空箱纳入分析图
        const emptyBoxCtx = document.getElementById('emptyBoxChart').getContext('2d');
        new Chart(emptyBoxCtx, {
            type: 'bar',
            data: {
                labels: analysisData.machines,
                datasets: [{
                    label: '空箱纳入',
                    data: analysisData.emptyBoxData,
                    backgroundColor: 'rgba(46, 204, 113, 0.8)',
                    borderColor: 'rgba(46, 204, 113, 1)',
                    borderWidth: 2
                }, {
                    label: '生产实绩',
                    data: analysisData.actualData,
                    backgroundColor: 'rgba(231, 76, 60, 0.8)',
                    borderColor: 'rgba(231, 76, 60, 1)',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '8-9月空箱纳入vs生产实绩对比',
                        font: { size: 16, weight: 'bold' }
                    },
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '数量（车辆）'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '机种'
                        }
                    }
                }
            }
        });

        // 空箱匹配度波动分析图
        const emptyBoxVariationCtx = document.getElementById('emptyBoxVariationChart').getContext('2d');
        new Chart(emptyBoxVariationCtx, {
            type: 'radar',
            data: {
                labels: analysisData.machines,
                datasets: [{
                    label: '空箱完美匹配率(%)',
                    data: analysisData.emptyBoxMatchRates,
                    backgroundColor: 'rgba(155, 89, 182, 0.2)',
                    borderColor: 'rgba(155, 89, 182, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(155, 89, 182, 1)',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: 'rgba(155, 89, 182, 1)'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '各机种空箱匹配度雷达图',
                        font: { size: 16, weight: 'bold' }
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            stepSize: 20
                        }
                    }
                }
            }
        });

        // 每日趋势综合图
        const dailyTrendCtx = document.getElementById('dailyTrendChart').getContext('2d');
        new Chart(dailyTrendCtx, {
            type: 'line',
            data: {
                labels: dailyTrendsData.JT028.dates,
                datasets: [
                    {
                        label: 'JT028-计划',
                        data: dailyTrendsData.JT028.plan,
                        borderColor: 'rgba(52, 152, 219, 1)',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        borderWidth: 2,
                        fill: false
                    },
                    {
                        label: 'JT028-实绩',
                        data: dailyTrendsData.JT028.actual,
                        borderColor: 'rgba(231, 76, 60, 1)',
                        backgroundColor: 'rgba(231, 76, 60, 0.1)',
                        borderWidth: 2,
                        fill: false
                    },
                    {
                        label: 'JT026-计划',
                        data: dailyTrendsData.JT026.plan,
                        borderColor: 'rgba(46, 204, 113, 1)',
                        backgroundColor: 'rgba(46, 204, 113, 0.1)',
                        borderWidth: 2,
                        fill: false
                    },
                    {
                        label: 'JT026-实绩',
                        data: dailyTrendsData.JT026.actual,
                        borderColor: 'rgba(155, 89, 182, 1)',
                        backgroundColor: 'rgba(155, 89, 182, 0.1)',
                        borderWidth: 2,
                        fill: false
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '主要机种每日执行趋势对比（8-9月）',
                        font: { size: 16, weight: 'bold' }
                    },
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '数量（车辆）'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '日期'
                        },
                        ticks: {
                            maxTicksLimit: 15
                        }
                    }
                }
            }
        });

        // 创建各机种单独趋势图的函数
        function createMachineTrendChart(canvasId, machineKey, machineName) {
            const ctx = document.getElementById(canvasId).getContext('2d');
            const machineData = dailyTrendsData[machineKey];

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: machineData.dates,
                    datasets: [
                        {
                            label: '生产计划',
                            data: machineData.plan,
                            borderColor: 'rgba(52, 152, 219, 1)',
                            backgroundColor: 'rgba(52, 152, 219, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.1
                        },
                        {
                            label: '生产实绩',
                            data: machineData.actual,
                            borderColor: 'rgba(231, 76, 60, 1)',
                            backgroundColor: 'rgba(231, 76, 60, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.1
                        },
                        {
                            label: '空箱纳入',
                            data: machineData.empty_box,
                            borderColor: 'rgba(46, 204, 113, 1)',
                            backgroundColor: 'rgba(46, 204, 113, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: machineName + ' 每日趋势',
                            font: { size: 14, weight: 'bold' }
                        },
                        legend: {
                            position: 'top',
                            labels: {
                                boxWidth: 12,
                                font: { size: 10 }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '数量',
                                font: { size: 10 }
                            },
                            ticks: {
                                font: { size: 9 }
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '日期',
                                font: { size: 10 }
                            },
                            ticks: {
                                maxTicksLimit: 8,
                                font: { size: 8 }
                            }
                        }
                    }
                }
            });
        }

        // 创建空箱纳入vs生产实际对比图的函数
        function createEmptyBoxComparisonChart(canvasId, machineKey, machineName) {
            const ctx = document.getElementById(canvasId).getContext('2d');
            const machineData = dailyTrendsData[machineKey];

            // 计算每日差异（空箱纳入 - 生产实际）
            const dailyDifference = machineData.empty_box.map((empty, index) => {
                return empty - machineData.actual[index];
            });

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: machineData.dates,
                    datasets: [
                        {
                            label: '生产实际',
                            data: machineData.actual,
                            borderColor: 'rgba(52, 152, 219, 1)',
                            backgroundColor: 'rgba(52, 152, 219, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.1,
                            yAxisID: 'y'
                        },
                        {
                            label: '空箱纳入',
                            data: machineData.empty_box,
                            borderColor: 'rgba(255, 193, 7, 1)',
                            backgroundColor: 'rgba(255, 193, 7, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.1,
                            yAxisID: 'y'
                        },
                        {
                            label: '每日差异',
                            data: dailyDifference,
                            borderColor: 'rgba(231, 76, 60, 1)',
                            backgroundColor: 'rgba(231, 76, 60, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.1,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: machineName + ' 空箱vs实际对比',
                            font: { size: 14, weight: 'bold' }
                        },
                        legend: {
                            position: 'top',
                            labels: {
                                boxWidth: 12,
                                font: { size: 10 }
                            }
                        }
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '数量（车辆）',
                                font: { size: 10 }
                            },
                            ticks: {
                                font: { size: 9 }
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: '差异',
                                font: { size: 10 }
                            },
                            ticks: {
                                font: { size: 9 }
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        },
                        x: {
                            title: {
                                display: true,
                                text: '日期',
                                font: { size: 10 }
                            },
                            ticks: {
                                maxTicksLimit: 8,
                                font: { size: 8 }
                            }
                        }
                    }
                }
            });
        }

        // 添加页面加载完成提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('8-9月统计表分析报告加载完成');

            // 创建各机种趋势图
            createMachineTrendChart('jt028TrendChart', 'JT028', 'JT028');
            createMachineTrendChart('jt026TrendChart', 'JT026', 'JT026');
            createMachineTrendChart('jh011shTrendChart', 'JH011-SH', 'JH011-SH');
            createMachineTrendChart('jh027sbTrendChart', 'JH027-SB', 'JH027-SB');
            createMachineTrendChart('jh027scTrendChart', 'JH027-SC', 'JH027-SC');
            createMachineTrendChart('jh027sdTrendChart', 'JH027-SD', 'JH027-SD');

            // 创建空箱对比图
            createEmptyBoxComparisonChart('jt028EmptyBoxTrendChart', 'JT028', 'JT028');
            createEmptyBoxComparisonChart('jt026EmptyBoxTrendChart', 'JT026', 'JT026');
            createEmptyBoxComparisonChart('jh011shEmptyBoxTrendChart', 'JH011-SH', 'JH011-SH');
            createEmptyBoxComparisonChart('jh027sbEmptyBoxTrendChart', 'JH027-SB', 'JH027-SB');
            createEmptyBoxComparisonChart('jh027scEmptyBoxTrendChart', 'JH027-SC', 'JH027-SC');
            createEmptyBoxComparisonChart('jh027sdEmptyBoxTrendChart', 'JH027-SD', 'JH027-SD');

            // 添加卡片悬停效果
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
