#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析引擎模块
"""

import pandas as pd
import numpy as np
from datetime import datetime, time, timedelta
from typing import Dict, List, Any, Optional
import streamlit as st

class WorktimeAnalyzer:
    """工时分析器"""
    
    def __init__(self):
        self.normal_work_hours = 8
        self.lunch_break_start = time(11, 30)
        self.lunch_break_end = time(12, 30)
    
    @st.cache_data
    def calculate_daily_workload(_self, df: pd.DataFrame) -> pd.DataFrame:
        """计算每日工作负荷"""
        if df is None or len(df) == 0:
            return pd.DataFrame()
        
        # 按日期和栋别分组计算
        daily_workload = df.groupby(['标准日期', '栋别']).agg({
            '单任务工时': ['sum', 'count', 'mean'],
            '作业类型': lambda x: x.nunique()
        }).round(2)
        
        # 重命名列
        daily_workload.columns = ['总工时', '任务数量', '平均工时', '作业类型数']
        daily_workload = daily_workload.reset_index()
        
        # 计算加班工时
        daily_workload['加班工时'] = (daily_workload['总工时'] - _self.normal_work_hours).clip(lower=0)
        
        # 计算效率指标
        daily_workload['效率指标'] = daily_workload['任务数量'] / daily_workload['总工时']
        
        return daily_workload
    
    def analyze_overtime_patterns(self, daily_workload: pd.DataFrame) -> Dict[str, Any]:
        """分析加班模式"""
        if daily_workload.empty:
            return {}
        
        overtime_data = daily_workload[daily_workload['加班工时'] > 0]
        
        analysis = {
            'total_overtime_hours': daily_workload['加班工时'].sum(),
            'overtime_days': len(overtime_data),
            'avg_overtime_per_day': overtime_data['加班工时'].mean() if not overtime_data.empty else 0,
            'max_overtime_day': {
                'date': daily_workload.loc[daily_workload['加班工时'].idxmax(), '标准日期'] if not daily_workload.empty else None,
                'hours': daily_workload['加班工时'].max(),
                'building': daily_workload.loc[daily_workload['加班工时'].idxmax(), '栋别'] if not daily_workload.empty else None
            },
            'building_overtime': daily_workload.groupby('栋别')['加班工时'].sum().to_dict(),
            'overtime_frequency': daily_workload.groupby('栋别')['加班工时'].apply(lambda x: (x > 0).sum()).to_dict()
        }
        
        return analysis
    
    def calculate_workload_balance(self, daily_workload: pd.DataFrame) -> Dict[str, float]:
        """计算工作负荷均衡度"""
        if daily_workload.empty:
            return {}
        
        building_stats = daily_workload.groupby('栋别')['总工时'].agg(['sum', 'mean', 'std'])
        
        # 计算变异系数
        cv_total = building_stats['sum'].std() / building_stats['sum'].mean() if building_stats['sum'].mean() > 0 else 0
        cv_daily = building_stats['mean'].std() / building_stats['mean'].mean() if building_stats['mean'].mean() > 0 else 0
        
        # 计算均衡度分数 (0-100)
        balance_score = max(0, 100 - cv_total * 100)
        
        return {
            'balance_score': balance_score,
            'coefficient_of_variation': cv_total,
            'daily_cv': cv_daily,
            'max_min_ratio': building_stats['sum'].max() / building_stats['sum'].min() if building_stats['sum'].min() > 0 else 0
        }
    
    def identify_peak_hours(self, df: pd.DataFrame) -> Dict[str, Any]:
        """识别高峰工作时段"""
        if df is None or len(df) == 0 or '起始时间_parsed' not in df.columns:
            return {}
        
        # 统计各时段开始的任务数量
        hour_counts = df['起始时间_parsed'].apply(lambda x: x.hour if x else None).value_counts().sort_index()
        
        peak_analysis = {
            'peak_hour': hour_counts.idxmax() if not hour_counts.empty else None,
            'peak_count': hour_counts.max() if not hour_counts.empty else 0,
            'hourly_distribution': hour_counts.to_dict(),
            'morning_tasks': hour_counts[hour_counts.index < 12].sum() if not hour_counts.empty else 0,
            'afternoon_tasks': hour_counts[hour_counts.index >= 12].sum() if not hour_counts.empty else 0
        }
        
        return peak_analysis

class BalanceAnalyzer:
    """负荷均衡分析器"""
    
    def __init__(self):
        pass
    
    def analyze_building_balance(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析栋别间负荷均衡"""
        if df is None or len(df) == 0:
            return {}
        
        building_stats = df.groupby('栋别').agg({
            '单任务工时': ['sum', 'mean', 'count'],
            '作业类型': 'nunique'
        }).round(2)
        
        building_stats.columns = ['总工时', '平均工时', '任务数量', '作业类型数']
        
        # 计算负荷分布
        total_workload = building_stats['总工时'].sum()
        building_stats['负荷占比'] = building_stats['总工时'] / total_workload * 100
        
        # 计算均衡度指标
        workload_std = building_stats['总工时'].std()
        workload_mean = building_stats['总工时'].mean()
        balance_coefficient = workload_std / workload_mean if workload_mean > 0 else 0
        
        return {
            'building_stats': building_stats.to_dict('index'),
            'balance_coefficient': balance_coefficient,
            'most_loaded': building_stats['总工时'].idxmax(),
            'least_loaded': building_stats['总工时'].idxmin(),
            'load_ratio': building_stats['总工时'].max() / building_stats['总工时'].min() if building_stats['总工时'].min() > 0 else 0
        }
    
    def analyze_worktype_balance(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析作业类型均衡"""
        if df is None or len(df) == 0:
            return {}
        
        worktype_stats = df.groupby('作业类型').agg({
            '单任务工时': ['sum', 'mean', 'count'],
            '栋别': 'nunique'
        }).round(2)
        
        worktype_stats.columns = ['总工时', '平均工时', '任务数量', '涉及栋别数']
        
        # 计算集中度
        total_tasks = worktype_stats['任务数量'].sum()
        worktype_stats['任务占比'] = worktype_stats['任务数量'] / total_tasks * 100
        
        # 计算赫芬达尔指数（集中度指标）
        hhi = (worktype_stats['任务占比'] ** 2).sum()
        
        return {
            'worktype_stats': worktype_stats.to_dict('index'),
            'concentration_index': hhi,
            'most_frequent': worktype_stats['任务数量'].idxmax(),
            'diversity_score': len(worktype_stats)
        }

class AlertManager:
    """预警管理器"""
    
    def __init__(self):
        self.thresholds = {
            'excessive_overtime': 10,  # 单日加班超过10小时
            'workload_imbalance': 2.0,  # 栋别间负荷比例超过2:1
            'efficiency_drop': 0.5,    # 效率低于0.5任务/小时
            'continuous_overtime': 3   # 连续3天加班
        }
    
    def check_all_alerts(self, df: pd.DataFrame, daily_workload: pd.DataFrame) -> List[Dict[str, Any]]:
        """检查所有预警条件"""
        alerts = []
        
        # 检查过度加班
        alerts.extend(self._check_excessive_overtime(daily_workload))
        
        # 检查负荷不均衡
        alerts.extend(self._check_workload_imbalance(daily_workload))
        
        # 检查效率异常
        alerts.extend(self._check_efficiency_issues(daily_workload))
        
        # 检查连续加班
        alerts.extend(self._check_continuous_overtime(daily_workload))
        
        return alerts
    
    def _check_excessive_overtime(self, daily_workload: pd.DataFrame) -> List[Dict[str, Any]]:
        """检查过度加班"""
        alerts = []
        
        if daily_workload.empty:
            return alerts
        
        excessive_days = daily_workload[daily_workload['加班工时'] > self.thresholds['excessive_overtime']]
        
        for _, row in excessive_days.iterrows():
            alerts.append({
                'type': 'excessive_overtime',
                'level': 'high',
                'title': '过度加班预警',
                'message': f"{row['栋别']}栋在{row['标准日期'].strftime('%Y-%m-%d')}加班{row['加班工时']:.1f}小时",
                'date': row['标准日期'],
                'building': row['栋别'],
                'value': row['加班工时']
            })
        
        return alerts
    
    def _check_workload_imbalance(self, daily_workload: pd.DataFrame) -> List[Dict[str, Any]]:
        """检查负荷不均衡"""
        alerts = []
        
        if daily_workload.empty:
            return alerts
        
        building_totals = daily_workload.groupby('栋别')['总工时'].sum()
        
        if len(building_totals) > 1:
            max_workload = building_totals.max()
            min_workload = building_totals.min()
            
            if min_workload > 0 and max_workload / min_workload > self.thresholds['workload_imbalance']:
                alerts.append({
                    'type': 'workload_imbalance',
                    'level': 'medium',
                    'title': '负荷不均衡预警',
                    'message': f"栋别间负荷差异过大，最大负荷是最小负荷的{max_workload/min_workload:.1f}倍",
                    'max_building': building_totals.idxmax(),
                    'min_building': building_totals.idxmin(),
                    'ratio': max_workload / min_workload
                })
        
        return alerts
    
    def _check_efficiency_issues(self, daily_workload: pd.DataFrame) -> List[Dict[str, Any]]:
        """检查效率问题"""
        alerts = []
        
        if daily_workload.empty or '效率指标' not in daily_workload.columns:
            return alerts
        
        low_efficiency_days = daily_workload[daily_workload['效率指标'] < self.thresholds['efficiency_drop']]
        
        for _, row in low_efficiency_days.iterrows():
            alerts.append({
                'type': 'low_efficiency',
                'level': 'medium',
                'title': '效率异常预警',
                'message': f"{row['栋别']}栋在{row['标准日期'].strftime('%Y-%m-%d')}效率较低({row['效率指标']:.2f}任务/小时)",
                'date': row['标准日期'],
                'building': row['栋别'],
                'efficiency': row['效率指标']
            })
        
        return alerts
    
    def _check_continuous_overtime(self, daily_workload: pd.DataFrame) -> List[Dict[str, Any]]:
        """检查连续加班"""
        alerts = []
        
        if daily_workload.empty:
            return alerts
        
        # 按栋别分组检查连续加班
        for building in daily_workload['栋别'].unique():
            building_data = daily_workload[daily_workload['栋别'] == building].sort_values('标准日期')
            
            consecutive_count = 0
            start_date = None
            
            for _, row in building_data.iterrows():
                if row['加班工时'] > 0:
                    if consecutive_count == 0:
                        start_date = row['标准日期']
                    consecutive_count += 1
                else:
                    if consecutive_count >= self.thresholds['continuous_overtime']:
                        alerts.append({
                            'type': 'continuous_overtime',
                            'level': 'high',
                            'title': '连续加班预警',
                            'message': f"{building}栋连续{consecutive_count}天加班",
                            'building': building,
                            'start_date': start_date,
                            'duration': consecutive_count
                        })
                    consecutive_count = 0
            
            # 检查最后的连续加班
            if consecutive_count >= self.thresholds['continuous_overtime']:
                alerts.append({
                    'type': 'continuous_overtime',
                    'level': 'high',
                    'title': '连续加班预警',
                    'message': f"{building}栋连续{consecutive_count}天加班",
                    'building': building,
                    'start_date': start_date,
                    'duration': consecutive_count
                })
        
        return alerts
