#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
作业平衡度分析仪表板主应用
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.settings import DashboardConfig
from config.themes import AppleTheme
from core.data_manager import DataManager
from core.analyzers import WorktimeAnalyzer, BalanceAnalyzer, AlertManager
from pages import overview, worktime
# 其他页面模块将在后续创建

# 页面配置
st.set_page_config(
    page_title="作业平衡度分析仪表板",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 应用Apple主题样式
AppleTheme.apply_theme()

class DashboardApp:
    def __init__(self):
        self.config = DashboardConfig()
        self.data_manager = DataManager()
        self.worktime_analyzer = WorktimeAnalyzer()
        self.balance_analyzer = BalanceAnalyzer()
        self.alert_manager = AlertManager()
        
        # 初始化会话状态
        if 'data_loaded' not in st.session_state:
            st.session_state.data_loaded = False
        if 'current_data' not in st.session_state:
            st.session_state.current_data = None
    
    def render_sidebar(self):
        """渲染侧边栏"""
        with st.sidebar:
            # Logo和标题
            st.markdown("""
                <div class="sidebar-header">
                    <h1>📊 作业分析</h1>
                    <p>智能仓库管理系统</p>
                </div>
            """, unsafe_allow_html=True)
            
            st.markdown("---")
            
            # 数据上传区域
            st.subheader("📁 数据管理")
            uploaded_file = st.file_uploader(
                "上传Excel文件",
                type=['xlsx', 'xls'],
                help="支持作业登记表格式"
            )
            
            if uploaded_file is not None:
                if st.button("🔄 加载数据", type="primary"):
                    with st.spinner("正在处理数据..."):
                        try:
                            data = self.data_manager.load_excel_data(uploaded_file)
                            st.session_state.current_data = data
                            st.session_state.data_loaded = True
                            st.success("✅ 数据加载成功！")
                        except Exception as e:
                            st.error(f"❌ 数据加载失败: {str(e)}")
            
            st.markdown("---")
            
            # 时间范围选择
            st.subheader("📅 时间范围")
            if st.session_state.data_loaded:
                data = st.session_state.current_data
                min_date = data['日期'].min()
                max_date = data['日期'].max()
                
                date_range = st.date_input(
                    "选择分析时间范围",
                    value=(min_date, max_date),
                    min_value=min_date,
                    max_value=max_date
                )
                
                # 栋别筛选
                st.subheader("🏢 栋别筛选")
                buildings = st.multiselect(
                    "选择栋别",
                    options=data['栋别'].unique(),
                    default=data['栋别'].unique()
                )
                
                # 更新筛选后的数据
                if len(date_range) == 2:
                    filtered_data = data[
                        (data['日期'] >= pd.Timestamp(date_range[0])) &
                        (data['日期'] <= pd.Timestamp(date_range[1])) &
                        (data['栋别'].isin(buildings))
                    ]
                    st.session_state.filtered_data = filtered_data
            
            st.markdown("---")
            
            # 系统状态
            st.subheader("⚡ 系统状态")
            if st.session_state.data_loaded:
                data_count = len(st.session_state.current_data)
                st.metric("数据记录", f"{data_count:,}")
                
                last_update = datetime.now().strftime("%H:%M:%S")
                st.metric("最后更新", last_update)
                
                # 数据质量指标
                quality_score = self.data_manager.calculate_data_quality(
                    st.session_state.current_data
                )
                st.metric("数据质量", f"{quality_score:.1%}")
            else:
                st.info("请上传数据文件开始分析")
    
    def render_main_content(self):
        """渲染主内容区域"""
        # 导航标签页
        tab1, tab2, tab3, tab4, tab5 = st.tabs([
            "📈 概览", "⏰ 工时分析", "⚖️ 负荷均衡", 
            "🚨 预警监控", "🔮 预测分析"
        ])
        
        if not st.session_state.data_loaded:
            st.markdown("""
                <div class="welcome-container">
                    <h2>🎉 欢迎使用作业平衡度分析仪表板</h2>
                    <p>请在左侧上传Excel数据文件开始分析</p>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <h3>📊 实时分析</h3>
                            <p>动态分析工时数据和负荷分布</p>
                        </div>
                        <div class="feature-card">
                            <h3>🎯 智能预警</h3>
                            <p>自动检测异常和瓶颈问题</p>
                        </div>
                        <div class="feature-card">
                            <h3>🔮 趋势预测</h3>
                            <p>基于历史数据预测未来趋势</p>
                        </div>
                    </div>
                </div>
            """, unsafe_allow_html=True)
            return
        
        # 获取筛选后的数据
        data = st.session_state.get('filtered_data', st.session_state.current_data)
        
        with tab1:
            overview.render_overview_page(data, self.worktime_analyzer, self.balance_analyzer)
        
        with tab2:
            worktime.render_worktime_page(data, self.worktime_analyzer)

        with tab3:
            st.info("⚖️ 负荷均衡页面开发中...")

        with tab4:
            st.info("🚨 预警监控页面开发中...")

        with tab5:
            st.info("🔮 预测分析页面开发中...")
    
    def run(self):
        """运行仪表板应用"""
        # 渲染页面标题
        st.markdown("""
            <div class="main-header">
                <h1>📊 作业平衡度分析仪表板</h1>
                <p>智能化仓库作业管理与优化系统</p>
            </div>
        """, unsafe_allow_html=True)
        
        # 渲染侧边栏和主内容
        self.render_sidebar()
        self.render_main_content()
        
        # 页脚信息
        st.markdown("---")
        st.markdown("""
            <div class="footer">
                <p>© 2025 作业平衡度分析系统 | 基于 Streamlit 构建</p>
            </div>
        """, unsafe_allow_html=True)

def main():
    """主函数"""
    app = DashboardApp()
    app.run()

if __name__ == "__main__":
    main()
